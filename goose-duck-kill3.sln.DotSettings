&lt;wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
	&lt;s:String x:Key="/Default/CodeInspection/Highlighting/InspectionSeverities/=CheckNamespace/@EntryIndexedValue"&gt;DO_NOT_SHOW&lt;/s:String&gt;
	&lt;s:String x:Key="/Default/CodeInspection/Highlighting/InspectionSeverities/=NamespaceDoesNotMatchFolderStructure/@EntryIndexedValue"&gt;DO_NOT_SHOW&lt;/s:String&gt;
	&lt;s:String x:Key="/Default/CodeInspection/Highlighting/InspectionSeverities/=RoslynInspection_002EIDE0130/@EntryIndexedValue"&gt;DO_NOT_SHOW&lt;/s:String&gt;
	
	&lt;s:Boolean x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/KEEP_USER_LINEBREAKS/@EntryValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/WRAP_LINES/@EntryValue"&gt;False&lt;/s:Boolean&gt;
	&lt;s:Int64 x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/MAX_LINE_LENGTH/@EntryValue"&gt;120&lt;/s:Int64&gt;
	&lt;s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/PLACE_ACCESSORHOLDER_ATTRIBUTE_ON_SAME_LINE/@EntryValue"&gt;NEVER&lt;/s:String&gt;
	&lt;s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/PLACE_FIELD_ATTRIBUTE_ON_SAME_LINE/@EntryValue"&gt;NEVER&lt;/s:String&gt;
	&lt;s:Boolean x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/PLACE_CONSTRUCTOR_INITIALIZER_ON_SAME_LINE/@EntryValue"&gt;False&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/PLACE_TYPE_CONSTRAINTS_ON_SAME_LINE/@EntryValue"&gt;False&lt;/s:String&gt;
	
	&lt;s:Boolean x:Key="/Default/CodeStyle/CSharpUsing/AddImportsToDeepestScope/@EntryValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/CodeStyle/CSharpUsing/QualifiedUsingAtNestedScope/@EntryValue"&gt;True&lt;/s:Boolean&gt;
	
	&lt;s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/PredefinedNamingRules/=PrivateInstanceFields/@EntryIndexedValue"&gt;&lt;Policy Inspect="True" Prefix="_" Suffix="" Style="aaBb" /&gt;&lt;/s:String&gt;
	&lt;s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/PredefinedNamingRules/=PrivateStaticFields/@EntryIndexedValue"&gt;&lt;Policy Inspect="True" Prefix="_" Suffix="" Style="aaBb" /&gt;&lt;/s:String&gt;
	
	&lt;s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ECSharpAttributeForSingleLineMethodUpgrade/@EntryValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ECSharpKeepExistingMigration/@EntryValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ECSharpPlaceEmbeddedOnSameLineMigration/@EntryValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ECSharpReformatCodeMigration/@EntryValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ECSharpUseContinuousIndentInsideBracesMigration/@EntryValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ESettingsUpgrade_002EAddAccessorOwnerDeclarationBracesMigration/@EntryValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ESettingsUpgrade_002ECSharpPlaceAttributeOnSameLineMigration/@EntryValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ESettingsUpgrade_002EMigrateBlankLinesAroundFieldToBlankLinesAroundProperty/@EntryValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ESettingsUpgrade_002EMigrateThisQualifierSettings/@EntryValue"&gt;True&lt;/s:Boolean&gt;
&lt;/wpf:ResourceDictionary&gt;
