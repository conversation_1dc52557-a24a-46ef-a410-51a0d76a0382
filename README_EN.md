# 🦆 Goose Duck Kill 3

A multiplayer social deduction game built with Unity and custom networking framework.

[![Unity Version](https://img.shields.io/badge/Unity-2023.3+-blue.svg)](https://unity3d.com/get-unity/download)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()

**Language / 语言**: [English](README_EN.md) | [中文](README.md)

> **Important Note**: This project has been completely migrated to a custom networking framework, eliminating dependency on Photon Fusion and achieving a zero-cost networking solution.

## 📖 English Documentation

### Project Background
Goose Duck Kill 3 is a social deduction game inspired by Among Us, developed with Unity engine. Originally using Photon Fusion as the networking solution, we developed a completely autonomous networking framework considering cost and autonomy.

### Technical Features
- **🔧 Self-Developed**: Completely autonomous networking framework with no third-party dependencies
- **💰 Zero Cost**: Eliminates Photon Fusion's paid limitations
- **🚀 High Performance**: Tick-based deterministic network synchronization
- **🛡️ Secure & Reliable**: Server-authoritative architecture effectively prevents cheating
- **📈 Scalable**: Modular design, easy to extend and maintain

### Gameplay
This is a 4-10 player online social deduction game:
- **Geese (Good Guys)**: Complete tasks on the map while identifying and voting out ducks
- **Ducks (Impostors)**: Secretly eliminate geese, sabotage tasks, and mislead other players in meetings
- **Neutral Roles**: Characters with special abilities and unique victory conditions

## 🎮 Game Overview

Goose Duck Kill 3 is a social deduction game where players are divided into different factions:
- **🦆 Geese (Good Guys)**: Complete tasks and identify the ducks
- **🦆 Ducks (Impostors)**: Eliminate geese and sabotage their efforts
- **⚖️ Neutral Roles**: Special roles with unique victory conditions

## ✨ Core Features

### 🎯 Game Systems
- **Dynamic Role Assignment**: Configurable role ratios based on player count
- **State Management**: Complete flow from Lobby → Starting → Playing → Meeting → Ending
- **Scoring System**: Victory condition tracking and team scoring
- **Real-time Multiplayer**: Real-time synchronization using custom networking framework

### 🎮 Player Mechanics
- **Movement System**: 2D character movement with network synchronization
- **Interaction System**: Task completion, killing, reporting, and vent usage
- **Role Abilities**: Different capabilities based on assigned roles
- **State Management**: Normal, Dead, Ghost, InTask, InMeeting states

### 🌐 Custom Networking Framework
- **Completely Autonomous**: Eliminates Photon Fusion dependency, reducing costs
- **High Performance**: Tick-based deterministic game simulation
- **Server Authority**: Anti-cheat server validation logic
- **Flexible Extension**: Modular design, easy feature expansion

#### Detailed Networking Framework
Our custom networking framework includes the following core components:

**NetworkRunner**
- Supports multiple game modes: Server, Host, Client, AutoHostOrClient, etc.
- Manages network lifecycle and connection states
- Provides tick-based deterministic simulation

**NetworkBehaviour**
- Replaces Photon Fusion's NetworkBehaviour
- Provides network lifecycle methods: Spawned, Despawned, FixedUpdateNetwork
- Supports automatic network property synchronization

**NetworkObject**
- Manages network object lifecycle
- Handles input authority and state authority
- Supports network behavior component registration

**Compatibility Adapter**
- Provides Fusion compatibility layer, minimizing code changes
- Supports progressive migration
- Maintains original API interfaces

## 🚀 Quick Start

### 📋 System Requirements
- Unity 2023.3 LTS or higher
- .NET Framework 4.8+
- Windows/macOS/Linux support

### 🔧 Detailed Installation Steps

#### 1. Environment Setup
Ensure your development environment meets the following requirements:
- Unity 2023.3 LTS or higher
- Visual Studio 2022 or JetBrains Rider
- Git version control tool

#### 2. Get Source Code
```bash
# Clone the project repository
git clone https://github.com/silverflute/goose-duck-kill3.git

# Enter project directory
cd goose-duck-kill3
```

#### 3. Unity Project Setup
1. Launch Unity Hub
2. Click "Add" button and select project folder
3. Confirm Unity version is 2023.3 or higher
4. Open project (first-time opening may take longer)

#### 4. Project Configuration
1. Open `Window > Package Manager`
2. Confirm all required packages are installed
3. Check configuration in `Project Settings > Player`
4. Verify `Project Settings > XR Plug-in Management` settings

#### 5. Build and Run
```bash
# Development mode (in Unity Editor)
# 1. Open Assets/Scenes/MainScene
# 2. Click Play button

# Build release version
# 1. File > Build Settings
# 2. Select target platform
# 3. Click Build
```

### 🎮 Detailed Game Instructions

#### Basic Controls
- **WASD/Arrow Keys**: Character movement
- **E/Space**: Interact with environment (complete tasks, use devices)
- **Q/Left Mouse**: Execute character special actions (kill, use abilities)
- **R**: Report bodies or emergencies
- **Tab**: View task list and map
- **ESC**: Open game menu

#### Game Mechanics

**Task System**
- Geese faction needs to complete various tasks to achieve victory
- Task types include: repair devices, clean garbage, connect circuits, etc.
- Task progress is visible to all players
- Completing all tasks leads to victory

**Meeting System**
- Finding bodies or pressing emergency button calls a meeting
- All players enter discussion phase during meetings
- Voting phase follows after discussion time ends
- Player with most votes gets eliminated

**Role Abilities**
- **Geese**: Complete tasks, participate in voting, report bodies
- **Ducks**: Kill other players, sabotage devices, use vents
- **Neutral Roles**: Different special abilities based on specific roles

#### Victory Conditions
- **Geese Victory**: Complete all tasks OR eliminate all ducks
- **Duck Victory**: Duck count equals or exceeds geese count
- **Neutral Victory**: Based on specific role's special victory conditions

## 📁 Project Structure

```
Assets/Scripts/
├── Core/                    # Core game systems
│   ├── GameManager.cs       # Game lifecycle management
│   ├── GameStateManager.cs  # State machine management
│   ├── RoleManager.cs       # Role assignment and management
│   └── ScoreManager.cs      # Scoring and victory conditions
├── CustomNetworking/        # Custom networking framework
│   ├── Core/               # Core network components
│   ├── Adapters/           # Fusion compatibility adapters
│   └── Components/         # Network components
├── Network/                # Network management
│   └── NetworkManager.cs   # Network connection management
├── Player/                 # Player-related
│   ├── PlayerController.cs # Player controller
│   ├── PlayerMovement.cs   # Movement system
│   └── PlayerInteraction.cs # Interaction system
└── UI/                     # User interface
    └── ConnectionUI.cs     # Connection interface
```

## 🏗️ Architecture Design

### Core Components
- **GameManager**: Central game coordinator and state management
- **NetworkManager**: Handles all network operations
- **RoleManager**: Manages player roles and abilities
- **ScoreManager**: Tracks scores and victory conditions
- **PlayerController**: Individual player behavior and input handling

### Custom Networking Framework
- **NetworkRunner**: Network runner supporting multiple game modes
- **NetworkBehaviour**: Network behavior base class
- **NetworkObject**: Network object management
- **NetworkTypes**: Network data types (PlayerRef, NetworkBool, etc.)

## 🎯 Game Flow

1. **Lobby**: Players join and wait for game start
2. **Starting**: Role assignment and countdown
3. **Playing**: Main game loop
4. **Meeting**: Discussion and voting phase
5. **Ending**: Victory screen and results

## 🔧 Configuration Options

### Game Settings
```csharp
// GameManager Configuration
[Header("Game Settings")]
public float startingDuration = 10f;    // Start countdown
public float gameDuration = 300f;       // Game duration
public float meetingDuration = 120f;    // Meeting duration

// RoleManager Configuration
[Header("Role Configuration")]
public float duckRatio = 0.25f;         // Duck ratio
public float neutralRatio = 0.1f;       // Neutral role ratio
public int minDucks = 1;                // Minimum ducks
public int maxDucks = 3;                // Maximum ducks
```

### Network Settings
```csharp
// NetworkManager Configuration
[Header("Network Settings")]
public int maxPlayers = 10;             // Maximum players
public float tickRate = 60f;            // Network update frequency
public bool enableLagCompensation = true; // Lag compensation
```

## 🚧 Development Status

### ✅ Implemented
- Basic network type system
- Network object lifecycle management
- Player connect/disconnect handling
- Basic network synchronization
- Event system
- UI integration

### 🔄 In Progress
- Network transform synchronization (basic version)
- Input system (framework ready)
- Change detection (simplified version)

### 📋 To Be Implemented
- RPC system
- Reliable data transmission
- Host migration
- Advanced network optimization
- Anti-cheat system

## 📚 Documentation

- [Documentation Center](Docs/README.md) - Complete documentation index
- [Migration Documentation](MIGRATION_DOCUMENTATION.md) - Photon Fusion migration details
- [Architecture Guide](Docs/ARCHITECTURE.md) - Detailed system design
- [API Reference](Docs/API_REFERENCE.md) - Code documentation
- [Custom Network Backend](Docs/CUSTOM_NETWORK_BACKEND.md) - Network framework details
- [Implementation Summary](Docs/IMPLEMENTATION_SUMMARY.md) - Implementation overview
- [Technical Plan](Docs/GooseDuckKill_TechnicalPlan.md) - Project technical planning

## 🛠️ Development Technologies

- **Unity 2023.3+**: Game engine
- **C# .NET**: Programming language
- **Custom Networking Framework**: Replaces Photon Fusion
- **Universal Render Pipeline**: Graphics pipeline

## 🤝 Contributing

Contributions are welcome! Please check the [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Note**: This project has migrated from Photon Fusion to a custom networking framework, achieving a completely autonomous networking solution and significantly reducing operational costs.
