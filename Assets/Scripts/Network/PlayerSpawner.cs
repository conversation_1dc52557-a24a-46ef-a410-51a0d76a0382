using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 玩家生成器 - 负责在网络环境中生成和销毁玩家
    /// </summary>
    public class PlayerSpawner : MonoBehaviour
    {
        [Header("Spawner Settings")]
        [SerializeField] private GameObject playerPrefab; // 移除NetworkPrefabRef依赖
        [SerializeField] private Transform[] spawnPoints;
        [SerializeField] private bool randomizeSpawnPoints = true;

        // 玩家引用字典
        private Dictionary<PlayerRef, NetworkObject> _spawnedPlayers = new Dictionary<PlayerRef, NetworkObject>();
        private int _nextSpawnPointIndex = 0;

        // 事件
        public event Action<NetworkRunner, NetworkObject, PlayerRef> OnPlayerSpawned;
        public event Action<NetworkRunner, NetworkObject, PlayerRef> OnPlayerDespawned;

        private void Awake()
        {
            // 如果未设置生成点，则查找场景中的生成点
            if (spawnPoints == null || spawnPoints.Length == 0)
            {
                var spawnPointObjects = GameObject.FindGameObjectsWithTag("SpawnPoint");
                if (spawnPointObjects.Length > 0)
                {
                    spawnPoints = new Transform[spawnPointObjects.Length];
                    for (int i = 0; i < spawnPointObjects.Length; i++)
                    {
                        spawnPoints[i] = spawnPointObjects[i].transform;
                    }
                }
            }

            // 如果仍然没有生成点，则创建一个默认生成点
            if (spawnPoints == null || spawnPoints.Length == 0)
            {
                Debug.LogWarning("No spawn points defined. Using (0,0,0) as default spawn point.");
                GameObject defaultSpawnPoint = new GameObject("DefaultSpawnPoint");
                defaultSpawnPoint.transform.position = Vector3.zero;
                spawnPoints = new Transform[] { defaultSpawnPoint.transform };
            }
        }

        /// <summary>
        /// 生成玩家
        /// </summary>
        public void SpawnPlayer(NetworkRunner runner, PlayerRef player)
        {
            // 检查玩家是否已经生成
            if (_spawnedPlayers.ContainsKey(player))
            {
                Debug.LogWarning($"Player {player} already spawned");
                return;
            }

            // 获取生成位置和旋转
            Transform spawnPoint = GetNextSpawnPoint();
            Vector3 spawnPosition = spawnPoint != null ? spawnPoint.position : Vector3.zero;
            Quaternion spawnRotation = spawnPoint != null ? spawnPoint.rotation : Quaternion.identity;

            // 生成玩家
            NetworkObject playerObject = runner.Spawn(
                playerPrefab,
                spawnPosition,
                spawnRotation,
                player
            );

            // 初始化玩家
            if (playerObject != null)
            {
                Debug.Log($"Player {player} spawned");
            }

            // 添加到生成的玩家字典
            if (playerObject != null)
            {
                _spawnedPlayers[player] = playerObject;
                OnPlayerSpawned?.Invoke(runner, playerObject, player);
            }
        }

        /// <summary>
        /// 销毁玩家
        /// </summary>
        public void DespawnPlayer(NetworkRunner runner, PlayerRef player)
        {
            // 检查玩家是否存在
            if (_spawnedPlayers.TryGetValue(player, out NetworkObject playerObject))
            {
                // 销毁玩家
                runner.Despawn(playerObject);
                _spawnedPlayers.Remove(player);

                OnPlayerDespawned?.Invoke(runner, playerObject, player);

                Debug.Log($"Player {player} despawned");
            }
        }

        /// <summary>
        /// 获取玩家对象
        /// </summary>
        public NetworkObject GetPlayerObject(PlayerRef player)
        {
            if (_spawnedPlayers.TryGetValue(player, out NetworkObject playerObject))
            {
                return playerObject;
            }

            return null;
        }

        /// <summary>
        /// 获取下一个生成点
        /// </summary>
        private Transform GetNextSpawnPoint()
        {
            if (spawnPoints == null || spawnPoints.Length == 0)
                return null;

            Transform spawnPoint;

            if (randomizeSpawnPoints)
            {
                // 随机选择生成点
                int index = UnityEngine.Random.Range(0, spawnPoints.Length);
                spawnPoint = spawnPoints[index];
            }
            else
            {
                // 顺序选择生成点
                spawnPoint = spawnPoints[_nextSpawnPointIndex];
                _nextSpawnPointIndex = (_nextSpawnPointIndex + 1) % spawnPoints.Length;
            }

            return spawnPoint;
        }

        /// <summary>
        /// 重置生成器
        /// </summary>
        public void Reset()
        {
            _spawnedPlayers.Clear();
            _nextSpawnPointIndex = 0;
        }
    }
}
