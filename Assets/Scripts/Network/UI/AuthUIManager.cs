using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using GooseDuck<PERSON>ill.Network;
using GooseDuckKill.Network.Models;

namespace GooseDuckKill.Network.UI
{
    /// <summary>
    /// 认证UI管理器
    /// 提供完整的用户认证界面功能
    /// </summary>
    public class AuthUIManager : MonoBehaviour
    {
        [Header("登录界面")]
        [SerializeField] private GameObject loginPanel;
        [SerializeField] private InputField loginUsernameInput;
        [SerializeField] private InputField loginPasswordInput;
        [SerializeField] private Button loginButton;
        [SerializeField] private Button showRegisterButton;
        [SerializeField] private But<PERSON> forgotPasswordButton;
        [SerializeField] private Toggle rememberMeToggle;
        
        [Header("注册界面")]
        [SerializeField] private GameObject registerPanel;
        [SerializeField] private InputField registerUsernameInput;
        [SerializeField] private InputField registerEmailInput;
        [SerializeField] private InputField registerPasswordInput;
        [SerializeField] private InputField registerConfirmPasswordInput;
        [SerializeField] private InputField displayNameInput;
        [SerializeField] private Button registerButton;
        [SerializeField] private Button showLoginButton;
        [SerializeField] private Toggle acceptTermsToggle;
        
        [Header("忘记密码界面")]
        [SerializeField] private GameObject forgotPasswordPanel;
        [SerializeField] private InputField forgotEmailInput;
        [SerializeField] private Button sendResetEmailButton;
        [SerializeField] private Button backToLoginButton;
        
        [Header("用户信息界面")]
        [SerializeField] private GameObject userProfilePanel;
        [SerializeField] private Text usernameText;
        [SerializeField] private Text emailText;
        [SerializeField] private Text levelText;
        [SerializeField] private Text experienceText;
        [SerializeField] private Button logoutButton;
        [SerializeField] private Button changePasswordButton;
        [SerializeField] private Button editProfileButton;
        
        [Header("修改密码界面")]
        [SerializeField] private GameObject changePasswordPanel;
        [SerializeField] private InputField currentPasswordInput;
        [SerializeField] private InputField newPasswordInput;
        [SerializeField] private InputField confirmNewPasswordInput;
        [SerializeField] private Button confirmChangePasswordButton;
        [SerializeField] private Button cancelChangePasswordButton;
        
        [Header("状态显示")]
        [SerializeField] private Text statusText;
        [SerializeField] private GameObject loadingIndicator;
        
        // 认证管理器
        private AuthManager _authManager;
        
        // 当前显示的面板
        private GameObject _currentPanel;
        
        private void Start()
        {
            InitializeComponents();
            SetupUI();
            CheckAuthState();
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            _authManager = AuthManager.Instance;
            
            // 订阅认证事件
            _authManager.OnAuthStateChanged += HandleAuthStateChanged;
            _authManager.OnAuthError += HandleAuthError;
            _authManager.OnUserUpdated += HandleUserUpdated;
        }
        
        /// <summary>
        /// 设置UI
        /// </summary>
        private void SetupUI()
        {
            // 登录界面按钮
            loginButton?.onClick.AddListener(OnLoginButtonClicked);
            showRegisterButton?.onClick.AddListener(() => ShowPanel(registerPanel));
            forgotPasswordButton?.onClick.AddListener(() => ShowPanel(forgotPasswordPanel));
            
            // 注册界面按钮
            registerButton?.onClick.AddListener(OnRegisterButtonClicked);
            showLoginButton?.onClick.AddListener(() => ShowPanel(loginPanel));
            
            // 忘记密码界面按钮
            sendResetEmailButton?.onClick.AddListener(OnSendResetEmailButtonClicked);
            backToLoginButton?.onClick.AddListener(() => ShowPanel(loginPanel));
            
            // 用户信息界面按钮
            logoutButton?.onClick.AddListener(OnLogoutButtonClicked);
            changePasswordButton?.onClick.AddListener(() => ShowPanel(changePasswordPanel));
            editProfileButton?.onClick.AddListener(OnEditProfileButtonClicked);
            
            // 修改密码界面按钮
            confirmChangePasswordButton?.onClick.AddListener(OnConfirmChangePasswordButtonClicked);
            cancelChangePasswordButton?.onClick.AddListener(() => ShowPanel(userProfilePanel));
            
            // 隐藏加载指示器
            if (loadingIndicator != null)
                loadingIndicator.SetActive(false);
        }
        
        /// <summary>
        /// 检查认证状态
        /// </summary>
        private void CheckAuthState()
        {
            if (_authManager.IsAuthenticated)
            {
                ShowUserProfile();
            }
            else
            {
                ShowPanel(loginPanel);
            }
        }
        
        /// <summary>
        /// 显示指定面板
        /// </summary>
        private void ShowPanel(GameObject panel)
        {
            // 隐藏所有面板
            loginPanel?.SetActive(false);
            registerPanel?.SetActive(false);
            forgotPasswordPanel?.SetActive(false);
            userProfilePanel?.SetActive(false);
            changePasswordPanel?.SetActive(false);
            
            // 显示指定面板
            if (panel != null)
            {
                panel.SetActive(true);
                _currentPanel = panel;
            }
            
            // 清除状态文本
            UpdateStatusText("");
        }
        
        /// <summary>
        /// 显示用户信息
        /// </summary>
        private void ShowUserProfile()
        {
            ShowPanel(userProfilePanel);
            UpdateUserProfileDisplay();
        }
        
        /// <summary>
        /// 更新用户信息显示
        /// </summary>
        private void UpdateUserProfileDisplay()
        {
            var user = _authManager.CurrentUser;
            if (user == null) return;
            
            if (usernameText != null)
                usernameText.text = user.username;
                
            if (emailText != null)
                emailText.text = user.email;
                
            if (levelText != null)
                levelText.text = $"Level: {user.level}";
                
            if (experienceText != null)
                experienceText.text = $"Experience: {user.experience}";
        }
        
        /// <summary>
        /// 更新状态文本
        /// </summary>
        private void UpdateStatusText(string message)
        {
            if (statusText != null)
            {
                statusText.text = message;
            }
        }
        
        /// <summary>
        /// 显示/隐藏加载指示器
        /// </summary>
        private void SetLoadingState(bool isLoading)
        {
            if (loadingIndicator != null)
                loadingIndicator.SetActive(isLoading);
        }
        
        #region UI事件处理
        
        private void OnLoginButtonClicked()
        {
            var username = loginUsernameInput?.text ?? "";
            var password = loginPasswordInput?.text ?? "";
            
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                UpdateStatusText("请输入用户名和密码");
                return;
            }
            
            SetLoadingState(true);
            UpdateStatusText("正在登录...");
            
            _authManager.LoginAsync(username, password, (success) =>
            {
                SetLoadingState(false);
                if (success)
                {
                    UpdateStatusText("登录成功！");
                    ShowUserProfile();
                }
                else
                {
                    UpdateStatusText("登录失败，请检查用户名和密码");
                }
            });
        }
        
        private void OnRegisterButtonClicked()
        {
            var username = registerUsernameInput?.text ?? "";
            var email = registerEmailInput?.text ?? "";
            var password = registerPasswordInput?.text ?? "";
            var confirmPassword = registerConfirmPasswordInput?.text ?? "";
            var displayName = displayNameInput?.text ?? username;
            
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(email) || 
                string.IsNullOrEmpty(password) || string.IsNullOrEmpty(confirmPassword))
            {
                UpdateStatusText("请填写所有必填字段");
                return;
            }
            
            if (password != confirmPassword)
            {
                UpdateStatusText("密码确认不匹配");
                return;
            }
            
            if (acceptTermsToggle != null && !acceptTermsToggle.isOn)
            {
                UpdateStatusText("请同意服务条款");
                return;
            }
            
            SetLoadingState(true);
            UpdateStatusText("正在注册...");
            
            _authManager.RegisterAsync(username, email, password, confirmPassword, (success) =>
            {
                SetLoadingState(false);
                if (success)
                {
                    UpdateStatusText("注册成功！正在自动登录...");
                }
                else
                {
                    UpdateStatusText("注册失败，请检查输入信息");
                }
            });
        }
        
        private void OnSendResetEmailButtonClicked()
        {
            var email = forgotEmailInput?.text ?? "";
            
            if (string.IsNullOrEmpty(email))
            {
                UpdateStatusText("请输入邮箱地址");
                return;
            }
            
            SetLoadingState(true);
            UpdateStatusText("正在发送重置邮件...");
            
            _authManager.ForgotPasswordAsync(email, (success) =>
            {
                SetLoadingState(false);
                if (success)
                {
                    UpdateStatusText("重置邮件已发送，请检查您的邮箱");
                }
                else
                {
                    UpdateStatusText("发送重置邮件失败，请检查邮箱地址");
                }
            });
        }
        
        private void OnLogoutButtonClicked()
        {
            SetLoadingState(true);
            UpdateStatusText("正在登出...");
            
            _authManager.LogoutAsync(() =>
            {
                SetLoadingState(false);
                UpdateStatusText("已登出");
                ShowPanel(loginPanel);
            });
        }
        
        private void OnEditProfileButtonClicked()
        {
            // 这里可以打开编辑用户信息的界面
            UpdateStatusText("编辑用户信息功能待实现");
        }
        
        private void OnConfirmChangePasswordButtonClicked()
        {
            var currentPassword = currentPasswordInput?.text ?? "";
            var newPassword = newPasswordInput?.text ?? "";
            var confirmNewPassword = confirmNewPasswordInput?.text ?? "";
            
            if (string.IsNullOrEmpty(currentPassword) || string.IsNullOrEmpty(newPassword) || 
                string.IsNullOrEmpty(confirmNewPassword))
            {
                UpdateStatusText("请填写所有密码字段");
                return;
            }
            
            if (newPassword != confirmNewPassword)
            {
                UpdateStatusText("新密码确认不匹配");
                return;
            }
            
            SetLoadingState(true);
            UpdateStatusText("正在修改密码...");
            
            _authManager.ChangePasswordAsync(currentPassword, newPassword, confirmNewPassword, (success) =>
            {
                SetLoadingState(false);
                if (success)
                {
                    UpdateStatusText("密码修改成功！");
                    ShowPanel(userProfilePanel);
                    
                    // 清空密码输入框
                    if (currentPasswordInput != null) currentPasswordInput.text = "";
                    if (newPasswordInput != null) newPasswordInput.text = "";
                    if (confirmNewPasswordInput != null) confirmNewPasswordInput.text = "";
                }
                else
                {
                    UpdateStatusText("密码修改失败，请检查当前密码");
                }
            });
        }
        
        #endregion
        
        #region 认证事件处理
        
        private void HandleAuthStateChanged(bool isAuthenticated)
        {
            if (isAuthenticated)
            {
                ShowUserProfile();
            }
            else
            {
                ShowPanel(loginPanel);
            }
        }
        
        private void HandleAuthError(string error)
        {
            UpdateStatusText($"认证错误: {error}");
            SetLoadingState(false);
        }
        
        private void HandleUserUpdated(User user)
        {
            UpdateUserProfileDisplay();
        }
        
        #endregion
        
        private void OnDestroy()
        {
            // 清理事件订阅
            if (_authManager != null)
            {
                _authManager.OnAuthStateChanged -= HandleAuthStateChanged;
                _authManager.OnAuthError -= HandleAuthError;
                _authManager.OnUserUpdated -= HandleUserUpdated;
            }
        }
    }
}
