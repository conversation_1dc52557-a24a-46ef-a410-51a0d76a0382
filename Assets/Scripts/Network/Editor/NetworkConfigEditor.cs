using UnityEngine;
using UnityEditor;
using GooseDuckKill.Network;

namespace GooseDuckKill.Network.Editor
{
    /// <summary>
    /// NetworkConfig的自定义编辑器
    /// 提供更好的配置界面和验证功能
    /// </summary>
    [CustomEditor(typeof(NetworkConfig))]
    public class NetworkConfigEditor : UnityEditor.Editor
    {
        private NetworkConfig _config;
        private bool _showDevelopmentSettings = true;
        private bool _showProductionSettings = true;
        private bool _showAdvancedSettings = false;
        
        private void OnEnable()
        {
            _config = (NetworkConfig)target;
        }
        
        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("GooseDuckKill Network Configuration", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // 环境配置
            DrawEnvironmentSettings();
            EditorGUILayout.Space();
            
            // 开发环境配置
            DrawDevelopmentSettings();
            EditorGUILayout.Space();
            
            // 生产环境配置
            DrawProductionSettings();
            EditorGUILayout.Space();
            
            // 高级设置
            DrawAdvancedSettings();
            EditorGUILayout.Space();
            
            // 当前配置信息
            DrawCurrentConfigInfo();
            EditorGUILayout.Space();
            
            // 操作按钮
            DrawActionButtons();
            
            serializedObject.ApplyModifiedProperties();
        }
        
        private void DrawEnvironmentSettings()
        {
            EditorGUILayout.LabelField("Environment Settings", EditorStyles.boldLabel);
            
            EditorGUILayout.PropertyField(serializedObject.FindProperty("autoDetectEnvironment"));
            
            if (!_config.autoDetectEnvironment)
            {
                EditorGUILayout.PropertyField(serializedObject.FindProperty("useProductionEnvironment"));
            }
            
            // 显示当前环境
            var currentEnv = _config.IsProduction ? "Production" : "Development";
            var envColor = _config.IsProduction ? Color.red : Color.green;
            
            var oldColor = GUI.color;
            GUI.color = envColor;
            EditorGUILayout.LabelField($"Current Environment: {currentEnv}", EditorStyles.helpBox);
            GUI.color = oldColor;
        }
        
        private void DrawDevelopmentSettings()
        {
            _showDevelopmentSettings = EditorGUILayout.Foldout(_showDevelopmentSettings, "Development Environment URLs", true);
            
            if (_showDevelopmentSettings)
            {
                EditorGUI.indentLevel++;
                
                EditorGUILayout.PropertyField(serializedObject.FindProperty("devAuthServiceUrl"), new GUIContent("Auth Service URL"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("devGameServiceUrl"), new GUIContent("Game Service URL"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("devRoomServiceUrl"), new GUIContent("Room Service URL"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("devWebSocketUrl"), new GUIContent("WebSocket URL"));
                
                // 快速设置按钮
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Set Default Localhost"))
                {
                    SetDefaultLocalhostUrls();
                }
                if (GUILayout.Button("Test Connectivity"))
                {
                    TestDevelopmentConnectivity();
                }
                EditorGUILayout.EndHorizontal();
                
                EditorGUI.indentLevel--;
            }
        }
        
        private void DrawProductionSettings()
        {
            _showProductionSettings = EditorGUILayout.Foldout(_showProductionSettings, "Production Environment URLs", true);
            
            if (_showProductionSettings)
            {
                EditorGUI.indentLevel++;
                
                EditorGUILayout.PropertyField(serializedObject.FindProperty("prodAuthServiceUrl"), new GUIContent("Auth Service URL"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("prodGameServiceUrl"), new GUIContent("Game Service URL"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("prodRoomServiceUrl"), new GUIContent("Room Service URL"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("prodWebSocketUrl"), new GUIContent("WebSocket URL"));
                
                // 警告信息
                EditorGUILayout.HelpBox("Production URLs should use HTTPS/WSS protocols for security.", MessageType.Warning);
                
                EditorGUI.indentLevel--;
            }
        }
        
        private void DrawAdvancedSettings()
        {
            _showAdvancedSettings = EditorGUILayout.Foldout(_showAdvancedSettings, "Advanced Settings", true);
            
            if (_showAdvancedSettings)
            {
                EditorGUI.indentLevel++;
                
                EditorGUILayout.LabelField("API Configuration", EditorStyles.boldLabel);
                EditorGUILayout.PropertyField(serializedObject.FindProperty("apiVersion"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("requestTimeout"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("websocketTimeout"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("maxRetries"));
                
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("WebSocket Configuration", EditorStyles.boldLabel);
                EditorGUILayout.PropertyField(serializedObject.FindProperty("heartbeatInterval"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("maxReconnectAttempts"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("reconnectDelay"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("enableCompression"));
                
                EditorGUI.indentLevel--;
            }
        }
        
        private void DrawCurrentConfigInfo()
        {
            EditorGUILayout.LabelField("Current Configuration", EditorStyles.boldLabel);
            
            EditorGUI.BeginDisabledGroup(true);
            
            EditorGUILayout.TextField("Auth Service", _config.AuthServiceUrl);
            EditorGUILayout.TextField("Game Service", _config.GameServiceUrl);
            EditorGUILayout.TextField("Room Service", _config.RoomServiceUrl);
            EditorGUILayout.TextField("Room WebSocket", _config.RoomWebSocketUrl);
            EditorGUILayout.TextField("Game WebSocket", _config.GameWebSocketUrl);
            
            EditorGUI.EndDisabledGroup();
        }
        
        private void DrawActionButtons()
        {
            EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Validate Configuration"))
            {
                ValidateConfiguration();
            }
            
            if (GUILayout.Button("Print Current Config"))
            {
                _config.PrintCurrentConfig();
            }
            
            if (GUILayout.Button("Reset to Defaults"))
            {
                ResetToDefaults();
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Create NetworkConfig Asset"))
            {
                CreateNetworkConfigAsset();
            }
            
            if (GUILayout.Button("Open Resources Folder"))
            {
                OpenResourcesFolder();
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        private void SetDefaultLocalhostUrls()
        {
            serializedObject.FindProperty("devAuthServiceUrl").stringValue = "http://localhost:8000";
            serializedObject.FindProperty("devGameServiceUrl").stringValue = "http://localhost:8001";
            serializedObject.FindProperty("devRoomServiceUrl").stringValue = "http://localhost:8002";
            serializedObject.FindProperty("devWebSocketUrl").stringValue = "ws://localhost:8002";
            
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(_config);
            
            Debug.Log("Development URLs set to default localhost configuration.");
        }
        
        private void TestDevelopmentConnectivity()
        {
            Debug.Log("Testing development environment connectivity...");
            
            // 这里可以添加实际的连接测试逻辑
            var authUrl = _config.AuthServiceUrl;
            var gameUrl = _config.GameServiceUrl;
            var roomUrl = _config.RoomServiceUrl;
            
            Debug.Log($"Auth Service: {authUrl}");
            Debug.Log($"Game Service: {gameUrl}");
            Debug.Log($"Room Service: {roomUrl}");
            
            // 实际项目中可以发送HTTP请求测试连接
            EditorUtility.DisplayDialog("Connectivity Test", 
                "Connectivity test completed. Check console for details.", "OK");
        }
        
        private void ValidateConfiguration()
        {
            bool isValid = _config.ValidateConfig();
            
            if (isValid)
            {
                EditorUtility.DisplayDialog("Configuration Valid", 
                    "Network configuration is valid and ready to use.", "OK");
            }
            else
            {
                EditorUtility.DisplayDialog("Configuration Invalid", 
                    "Network configuration has errors. Please check console for details.", "OK");
            }
        }
        
        private void ResetToDefaults()
        {
            if (EditorUtility.DisplayDialog("Reset Configuration", 
                "Are you sure you want to reset all settings to defaults? This cannot be undone.", 
                "Reset", "Cancel"))
            {
                // 重置开发环境
                serializedObject.FindProperty("devAuthServiceUrl").stringValue = "http://localhost:8000";
                serializedObject.FindProperty("devGameServiceUrl").stringValue = "http://localhost:8001";
                serializedObject.FindProperty("devRoomServiceUrl").stringValue = "http://localhost:8002";
                serializedObject.FindProperty("devWebSocketUrl").stringValue = "ws://localhost:8002";
                
                // 重置生产环境
                serializedObject.FindProperty("prodAuthServiceUrl").stringValue = "https://auth.gooseduck.game";
                serializedObject.FindProperty("prodGameServiceUrl").stringValue = "https://game.gooseduck.game";
                serializedObject.FindProperty("prodRoomServiceUrl").stringValue = "https://room.gooseduck.game";
                serializedObject.FindProperty("prodWebSocketUrl").stringValue = "wss://room.gooseduck.game";
                
                // 重置其他设置
                serializedObject.FindProperty("apiVersion").stringValue = "v1";
                serializedObject.FindProperty("requestTimeout").intValue = 30;
                serializedObject.FindProperty("websocketTimeout").intValue = 10;
                serializedObject.FindProperty("maxRetries").intValue = 3;
                serializedObject.FindProperty("heartbeatInterval").floatValue = 30f;
                serializedObject.FindProperty("maxReconnectAttempts").intValue = 5;
                serializedObject.FindProperty("reconnectDelay").floatValue = 2f;
                serializedObject.FindProperty("enableCompression").boolValue = true;
                
                serializedObject.ApplyModifiedProperties();
                EditorUtility.SetDirty(_config);
                
                Debug.Log("Configuration reset to defaults.");
            }
        }
        
        private void CreateNetworkConfigAsset()
        {
            string path = "Assets/Resources/NetworkConfig.asset";
            
            // 确保Resources文件夹存在
            if (!AssetDatabase.IsValidFolder("Assets/Resources"))
            {
                AssetDatabase.CreateFolder("Assets", "Resources");
            }
            
            // 检查是否已存在
            if (AssetDatabase.LoadAssetAtPath<NetworkConfig>(path) != null)
            {
                EditorUtility.DisplayDialog("Asset Exists", 
                    "NetworkConfig asset already exists in Resources folder.", "OK");
                return;
            }
            
            // 创建新的NetworkConfig实例
            var newConfig = CreateInstance<NetworkConfig>();
            AssetDatabase.CreateAsset(newConfig, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            // 选中新创建的资源
            Selection.activeObject = newConfig;
            EditorGUIUtility.PingObject(newConfig);
            
            Debug.Log($"NetworkConfig asset created at: {path}");
        }
        
        private void OpenResourcesFolder()
        {
            string resourcesPath = "Assets/Resources";
            
            if (!AssetDatabase.IsValidFolder(resourcesPath))
            {
                AssetDatabase.CreateFolder("Assets", "Resources");
            }
            
            Selection.activeObject = AssetDatabase.LoadAssetAtPath<Object>(resourcesPath);
            EditorGUIUtility.PingObject(Selection.activeObject);
        }
    }
}
