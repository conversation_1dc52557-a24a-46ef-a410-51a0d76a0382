using System.Collections;
using UnityEngine;
using GooseDuckKill.Network;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 认证状态监控器
    /// 监控Token状态，自动处理Token刷新和过期
    /// </summary>
    public class AuthStatusMonitor : MonoBehaviour
    {
        [Header("监控配置")]
        [SerializeField] private float checkInterval = 60f; // 检查间隔（秒）
        [SerializeField] private float refreshThreshold = 300f; // 刷新阈值（秒）
        [SerializeField] private bool enableAutoRefresh = true;
        [SerializeField] private bool enableDebugLogs = true;
        
        [Header("状态显示")]
        [SerializeField] private bool showStatusInUI = false;
        [SerializeField] private UnityEngine.UI.Text statusText;
        
        // 认证管理器
        private AuthManager _authManager;
        
        // 监控状态
        private bool _isMonitoring = false;
        private Coroutine _monitorCoroutine;
        
        // 单例实例
        private static AuthStatusMonitor _instance;
        public static AuthStatusMonitor Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("AuthStatusMonitor");
                    _instance = go.AddComponent<AuthStatusMonitor>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                Initialize();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            StartMonitoring();
        }
        
        /// <summary>
        /// 初始化监控器
        /// </summary>
        private void Initialize()
        {
            _authManager = AuthManager.Instance;
            
            // 订阅认证事件
            _authManager.OnAuthStateChanged += HandleAuthStateChanged;
            _authManager.OnAuthError += HandleAuthError;
            
            Log("AuthStatusMonitor initialized.");
        }
        
        /// <summary>
        /// 开始监控
        /// </summary>
        public void StartMonitoring()
        {
            if (_isMonitoring) return;
            
            _isMonitoring = true;
            _monitorCoroutine = StartCoroutine(MonitorCoroutine());
            
            Log("Auth status monitoring started.");
        }
        
        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            if (!_isMonitoring) return;
            
            _isMonitoring = false;
            
            if (_monitorCoroutine != null)
            {
                StopCoroutine(_monitorCoroutine);
                _monitorCoroutine = null;
            }
            
            Log("Auth status monitoring stopped.");
        }
        
        /// <summary>
        /// 监控协程
        /// </summary>
        private IEnumerator MonitorCoroutine()
        {
            while (_isMonitoring)
            {
                yield return new WaitForSeconds(checkInterval);
                
                if (_authManager.IsAuthenticated)
                {
                    CheckTokenStatus();
                }
                
                UpdateStatusDisplay();
            }
        }
        
        /// <summary>
        /// 检查Token状态
        /// </summary>
        private void CheckTokenStatus()
        {
            var remainingTime = _authManager.GetTokenRemainingTime();
            
            Log($"Token remaining time: {remainingTime:F0} seconds");
            
            // 检查是否需要刷新Token
            if (enableAutoRefresh && _authManager.IsTokenExpiringSoon(refreshThreshold))
            {
                Log("Token is expiring soon, attempting to refresh...");
                RefreshToken();
            }
            
            // 检查Token是否已过期
            if (remainingTime <= 0)
            {
                LogWarning("Token has expired!");
                HandleTokenExpired();
            }
        }
        
        /// <summary>
        /// 刷新Token
        /// </summary>
        private void RefreshToken()
        {
            _authManager.RefreshTokenAsync((success) =>
            {
                if (success)
                {
                    Log("Token refreshed successfully.");
                }
                else
                {
                    LogError("Failed to refresh token. User may need to re-authenticate.");
                    HandleTokenRefreshFailed();
                }
            });
        }
        
        /// <summary>
        /// 处理Token过期
        /// </summary>
        private void HandleTokenExpired()
        {
            LogWarning("Token expired. User needs to re-authenticate.");
            
            // 可以在这里触发重新登录流程
            // 例如：显示登录界面，清除用户数据等
        }
        
        /// <summary>
        /// 处理Token刷新失败
        /// </summary>
        private void HandleTokenRefreshFailed()
        {
            LogError("Token refresh failed. Clearing authentication state.");
            
            // 清除认证状态，用户需要重新登录
            _authManager.LogoutAsync(() =>
            {
                Log("User logged out due to token refresh failure.");
            });
        }
        
        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatusDisplay()
        {
            if (!showStatusInUI || statusText == null) return;
            
            if (_authManager.IsAuthenticated)
            {
                var remainingTime = _authManager.GetTokenRemainingTime();
                var minutes = (int)(remainingTime / 60);
                var seconds = (int)(remainingTime % 60);
                
                statusText.text = $"认证状态: 已登录\nToken剩余时间: {minutes:D2}:{seconds:D2}";
                
                // 根据剩余时间设置颜色
                if (remainingTime <= refreshThreshold)
                {
                    statusText.color = Color.yellow; // 即将过期
                }
                else if (remainingTime <= 60)
                {
                    statusText.color = Color.red; // 即将过期
                }
                else
                {
                    statusText.color = Color.green; // 正常
                }
            }
            else
            {
                statusText.text = "认证状态: 未登录";
                statusText.color = Color.gray;
            }
        }
        
        /// <summary>
        /// 验证Token有效性
        /// </summary>
        public void ValidateToken()
        {
            if (!_authManager.IsAuthenticated)
            {
                Log("No token to validate.");
                return;
            }
            
            Log("Validating token with server...");
            
            _authManager.ValidateTokenAsync((isValid) =>
            {
                if (isValid)
                {
                    Log("Token validation successful.");
                }
                else
                {
                    LogWarning("Token validation failed. Token may be invalid or expired.");
                    HandleTokenExpired();
                }
            });
        }
        
        /// <summary>
        /// 获取认证状态信息
        /// </summary>
        public AuthStatusInfo GetAuthStatusInfo()
        {
            return new AuthStatusInfo
            {
                IsAuthenticated = _authManager.IsAuthenticated,
                Username = _authManager.CurrentUser?.username ?? "",
                TokenRemainingTime = _authManager.GetTokenRemainingTime(),
                IsTokenExpiringSoon = _authManager.IsTokenExpiringSoon(refreshThreshold),
                IsMonitoring = _isMonitoring
            };
        }
        
        #region 认证事件处理
        
        private void HandleAuthStateChanged(bool isAuthenticated)
        {
            Log($"Auth state changed: {(isAuthenticated ? "Authenticated" : "Not Authenticated")}");
            
            if (isAuthenticated)
            {
                // 用户登录后，立即检查Token状态
                CheckTokenStatus();
            }
            
            UpdateStatusDisplay();
        }
        
        private void HandleAuthError(string error)
        {
            LogError($"Auth error: {error}");
        }
        
        #endregion
        
        #region 日志方法
        
        private void Log(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[AuthStatusMonitor] {message}");
            }
        }
        
        private void LogWarning(string message)
        {
            if (enableDebugLogs)
            {
                Debug.LogWarning($"[AuthStatusMonitor] {message}");
            }
        }
        
        private void LogError(string message)
        {
            Debug.LogError($"[AuthStatusMonitor] {message}");
        }
        
        #endregion
        
        private void OnDestroy()
        {
            StopMonitoring();
            
            // 清理事件订阅
            if (_authManager != null)
            {
                _authManager.OnAuthStateChanged -= HandleAuthStateChanged;
                _authManager.OnAuthError -= HandleAuthError;
            }
        }
    }
    
    /// <summary>
    /// 认证状态信息
    /// </summary>
    [System.Serializable]
    public class AuthStatusInfo
    {
        public bool IsAuthenticated;
        public string Username;
        public double TokenRemainingTime;
        public bool IsTokenExpiringSoon;
        public bool IsMonitoring;
        
        public override string ToString()
        {
            return $"Auth Status - Authenticated: {IsAuthenticated}, User: {Username}, " +
                   $"Token Time: {TokenRemainingTime:F0}s, Expiring Soon: {IsTokenExpiringSoon}, " +
                   $"Monitoring: {IsMonitoring}";
        }
    }
}
