using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 房间信息类 - 用于在内部管理房间数据
    /// 因为Fusion 2.0中SessionInfo是只读的，所以需要创建此包装类
    /// </summary>
    [Serializable]
    public class RoomInfo
    {
        // 房间数据
        public string Name { get; set; }
        public int PlayerCount { get; set; }
        public int MaxPlayers { get; set; }
        public bool IsOpen { get; set; }
        public bool IsVisible { get; set; }
        
        // 原始SessionInfo引用（如果有）
        private SessionInfo _sessionInfo;
        private bool _hasSessionInfo;
        
        /// <summary>
        /// 创建空房间信息
        /// </summary>
        public RoomInfo()
        {
            Name = string.Empty;
            PlayerCount = 0;
            MaxPlayers = 0;
            IsOpen = true;
            IsVisible = true;
            _hasSessionInfo = false;
        }
        
        /// <summary>
        /// 从SessionInfo创建房间信息
        /// </summary>
        public RoomInfo(SessionInfo sessionInfo)
        {
            Name = sessionInfo.Name;
            PlayerCount = sessionInfo.PlayerCount;
            MaxPlayers = sessionInfo.MaxPlayers;
            IsOpen = sessionInfo.IsOpen;
            IsVisible = sessionInfo.IsVisible;
            _sessionInfo = sessionInfo;
            _hasSessionInfo = true;
        }
        
        /// <summary>
        /// 从字段创建房间信息
        /// </summary>
        public RoomInfo(string name, int maxPlayers, bool isOpen = true, bool isVisible = true)
        {
            Name = name;
            PlayerCount = 0;
            MaxPlayers = maxPlayers;
            IsOpen = isOpen;
            IsVisible = isVisible;
            _hasSessionInfo = false;
        }
        
        /// <summary>
        /// 获取原始SessionInfo（如果有）
        /// </summary>
        public SessionInfo GetSessionInfo()
        {
            return _sessionInfo;
        }
        
        /// <summary>
        /// 检查是否有有效的SessionInfo
        /// </summary>
        public bool HasValidSessionInfo()
        {
            return _hasSessionInfo && _sessionInfo != null && !string.IsNullOrEmpty(_sessionInfo.Name);
        }
        
        /// <summary>
        /// 从SessionInfo列表转换为RoomInfo列表
        /// </summary>
        public static List<RoomInfo> FromSessionInfoList(List<SessionInfo> sessionInfos)
        {
            List<RoomInfo> roomInfos = new List<RoomInfo>();
            
            foreach (var sessionInfo in sessionInfos)
            {
                roomInfos.Add(new RoomInfo(sessionInfo));
            }
            
            return roomInfos;
        }
    }
} 