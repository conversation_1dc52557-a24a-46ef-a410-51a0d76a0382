using UnityEngine;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 网络配置管理器 - 统一管理所有网络服务的URL和配置
    /// 确保前台与后台服务端点的一致性
    /// </summary>
    [CreateAssetMenu(fileName = "NetworkConfig", menuName = "GooseDuckKill/Network Config")]
    public class NetworkConfig : ScriptableObject
    {
        [Header("环境配置")]
        [SerializeField] public bool useProductionEnvironment = false;
        [SerializeField] public bool autoDetectEnvironment = true;

        [Header("开发环境配置")]
        [SerializeField] public string devAuthServiceUrl = "http://localhost:8000";
        [SerializeField] public string devGameServiceUrl = "http://localhost:8001";
        [SerializeField] public string devRoomServiceUrl = "http://localhost:8002";
        [SerializeField] public string devWebSocketUrl = "ws://localhost:8002";

        [Header("生产环境配置")]
        [SerializeField] public string prodAuthServiceUrl = "https://auth.gooseduck.game";
        [SerializeField] public string prodGameServiceUrl = "https://game.gooseduck.game";
        [SerializeField] public string prodRoomServiceUrl = "https://room.gooseduck.game";
        [SerializeField] public string prodWebSocketUrl = "wss://room.gooseduck.game";

        [Header("API配置")]
        [SerializeField] public string apiVersion = "v1";
        [SerializeField] public int requestTimeout = 30;
        [SerializeField] public int websocketTimeout = 10;
        [SerializeField] public int maxRetries = 3;

        [Header("WebSocket配置")]
        [SerializeField] public float heartbeatInterval = 30f;
        [SerializeField] public int maxReconnectAttempts = 5;
        [SerializeField] public float reconnectDelay = 2f;
        [SerializeField] public bool enableCompression = true;
        
        // 单例实例
        private static NetworkConfig _instance;
        public static NetworkConfig Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = Resources.Load<NetworkConfig>("NetworkConfig");
                    if (_instance == null)
                    {
                        Debug.LogError("NetworkConfig not found in Resources folder! Please create one.");
                    }
                }
                return _instance;
            }
        }
        
        /// <summary>
        /// 是否使用生产环境
        /// </summary>
        public bool IsProduction
        {
            get
            {
                if (autoDetectEnvironment)
                {
                    return !Debug.isDebugBuild;
                }
                return useProductionEnvironment;
            }
        }
        
        /// <summary>
        /// 认证服务URL
        /// </summary>
        public string AuthServiceUrl => IsProduction ? prodAuthServiceUrl : devAuthServiceUrl;
        
        /// <summary>
        /// 游戏服务URL
        /// </summary>
        public string GameServiceUrl => IsProduction ? prodGameServiceUrl : devGameServiceUrl;
        
        /// <summary>
        /// 房间服务URL
        /// </summary>
        public string RoomServiceUrl => IsProduction ? prodRoomServiceUrl : devRoomServiceUrl;
        
        /// <summary>
        /// WebSocket基础URL
        /// </summary>
        public string WebSocketBaseUrl => IsProduction ? prodWebSocketUrl : devWebSocketUrl;
        
        /// <summary>
        /// 房间WebSocket完整URL
        /// </summary>
        public string RoomWebSocketUrl => $"{WebSocketBaseUrl}/api/{apiVersion}/rooms/ws";
        
        /// <summary>
        /// 游戏WebSocket完整URL
        /// </summary>
        public string GameWebSocketUrl => $"{WebSocketBaseUrl}/api/{apiVersion}/games/ws";
        
        /// <summary>
        /// API版本
        /// </summary>
        public string ApiVersion => apiVersion;
        
        /// <summary>
        /// 请求超时时间（秒）
        /// </summary>
        public int RequestTimeout => requestTimeout;
        
        /// <summary>
        /// WebSocket超时时间（秒）
        /// </summary>
        public int WebSocketTimeout => websocketTimeout;
        
        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetries => maxRetries;
        
        /// <summary>
        /// 心跳间隔（秒）
        /// </summary>
        public float HeartbeatInterval => heartbeatInterval;
        
        /// <summary>
        /// 最大重连尝试次数
        /// </summary>
        public int MaxReconnectAttempts => maxReconnectAttempts;
        
        /// <summary>
        /// 重连延迟（秒）
        /// </summary>
        public float ReconnectDelay => reconnectDelay;
        
        /// <summary>
        /// 是否启用压缩
        /// </summary>
        public bool EnableCompression => enableCompression;
        
        /// <summary>
        /// 获取完整的API URL
        /// </summary>
        /// <param name="service">服务类型</param>
        /// <param name="endpoint">端点路径</param>
        /// <returns>完整的API URL</returns>
        public string GetApiUrl(ServiceType service, string endpoint)
        {
            string baseUrl = service switch
            {
                ServiceType.Auth => AuthServiceUrl,
                ServiceType.Game => GameServiceUrl,
                ServiceType.Room => RoomServiceUrl,
                _ => throw new System.ArgumentException($"Unknown service type: {service}")
            };
            
            // 确保endpoint以/开头
            if (!endpoint.StartsWith("/"))
            {
                endpoint = "/" + endpoint;
            }
            
            // 如果endpoint不包含api版本，则添加
            if (!endpoint.StartsWith($"/api/{apiVersion}"))
            {
                endpoint = $"/api/{apiVersion}{endpoint}";
            }
            
            return baseUrl + endpoint;
        }
        
        /// <summary>
        /// 获取WebSocket URL（包含认证Token）
        /// </summary>
        /// <param name="service">服务类型</param>
        /// <param name="token">认证Token</param>
        /// <returns>WebSocket URL</returns>
        public string GetWebSocketUrl(ServiceType service, string token = null)
        {
            string wsUrl = service switch
            {
                ServiceType.Room => RoomWebSocketUrl,
                ServiceType.Game => GameWebSocketUrl,
                _ => throw new System.ArgumentException($"WebSocket not supported for service: {service}")
            };
            
            if (!string.IsNullOrEmpty(token))
            {
                wsUrl += $"?token={token}";
            }
            
            return wsUrl;
        }
        
        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        /// <returns>配置是否有效</returns>
        public bool ValidateConfig()
        {
            bool isValid = true;
            
            // 检查开发环境配置
            if (string.IsNullOrEmpty(devAuthServiceUrl) || 
                string.IsNullOrEmpty(devGameServiceUrl) || 
                string.IsNullOrEmpty(devRoomServiceUrl) ||
                string.IsNullOrEmpty(devWebSocketUrl))
            {
                Debug.LogError("Development environment URLs are not properly configured!");
                isValid = false;
            }
            
            // 检查生产环境配置
            if (string.IsNullOrEmpty(prodAuthServiceUrl) || 
                string.IsNullOrEmpty(prodGameServiceUrl) || 
                string.IsNullOrEmpty(prodRoomServiceUrl) ||
                string.IsNullOrEmpty(prodWebSocketUrl))
            {
                Debug.LogError("Production environment URLs are not properly configured!");
                isValid = false;
            }
            
            // 检查其他配置
            if (requestTimeout <= 0 || websocketTimeout <= 0 || maxRetries < 0)
            {
                Debug.LogError("Invalid timeout or retry configuration!");
                isValid = false;
            }
            
            return isValid;
        }
        
        /// <summary>
        /// 打印当前配置信息（用于调试）
        /// </summary>
        public void PrintCurrentConfig()
        {
            Debug.Log($"=== Network Configuration ===");
            Debug.Log($"Environment: {(IsProduction ? "Production" : "Development")}");
            Debug.Log($"Auth Service: {AuthServiceUrl}");
            Debug.Log($"Game Service: {GameServiceUrl}");
            Debug.Log($"Room Service: {RoomServiceUrl}");
            Debug.Log($"WebSocket: {WebSocketBaseUrl}");
            Debug.Log($"API Version: {ApiVersion}");
            Debug.Log($"Request Timeout: {RequestTimeout}s");
            Debug.Log($"WebSocket Timeout: {WebSocketTimeout}s");
            Debug.Log($"Max Retries: {MaxRetries}");
            Debug.Log($"==============================");
        }
        
        private void OnValidate()
        {
            // 在Inspector中修改值时进行验证
            if (requestTimeout <= 0) requestTimeout = 30;
            if (websocketTimeout <= 0) websocketTimeout = 10;
            if (maxRetries < 0) maxRetries = 3;
            if (heartbeatInterval <= 0) heartbeatInterval = 30f;
            if (maxReconnectAttempts < 0) maxReconnectAttempts = 5;
            if (reconnectDelay <= 0) reconnectDelay = 2f;
        }
    }
    
    /// <summary>
    /// 服务类型枚举
    /// </summary>
    public enum ServiceType
    {
        Auth,   // 认证服务
        Game,   // 游戏服务
        Room    // 房间服务
    }
}
