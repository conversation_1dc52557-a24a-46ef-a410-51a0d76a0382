using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.SceneManagement;
using CustomNetworking.Core;
using CustomNetworking.ErrorHandling;
using System.Linq;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 网络管理器 - 使用统一网络管理器的包装器，保持向后兼容性
    /// 使用单例模式便于全局访问
    /// </summary>
    public class NetworkManager : MonoBehaviour, INetworkRunnerCallbacks, CustomNetworking.Core.INetworkManager
    {
        [Header("Network Settings")]
        [SerializeField] private NetworkRunner.GameMode defaultGameMode = NetworkRunner.GameMode.Shared;
        [SerializeField] private int maxPlayers = 16;

        [Header("Components")]
        [SerializeField] private RoomManager roomManager;
        [SerializeField] private PlayerSpawner playerSpawner;
        [SerializeField] private GameObject playerPrefab; // 移除NetworkPrefabRef依赖

        // 网络Runner
        private NetworkRunner _runner;

        // 连接状态
        private bool _isConnecting;
        private bool _isConnected;

        // 错误处理和恢复
        private NetworkErrorHandler _errorHandler;
        private NetworkConnectionMonitor _connectionMonitor;
        private bool _isReconnecting;
        private float _lastHeartbeatTime;

        // 统一网络管理器引用
        private UnifiedNetworkManager _unifiedManager;

        // 单例实例
        private static NetworkManager _instance;

        // 事件
        public event Action OnConnectedToServerEvent;
        public event Action<NetDisconnectReason> OnDisconnectedFromServerEvent;
        public event Action<NetworkRunner> OnRunnerCreated;
        public event Action<List<SessionInfo>> OnRoomListUpdated;
        public event Action<string> OnErrorOccurred;

        /// <summary>
        /// 单例访问器
        /// </summary>
        public static NetworkManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = UnityEngine.Object.FindAnyObjectByType<NetworkManager>();

                    if (_instance == null)
                    {
                        GameObject obj = new GameObject("NetworkManager");
                        _instance = obj.AddComponent<NetworkManager>();
                    }
                }

                return _instance;
            }
        }

        /// <summary>
        /// 获取NetworkRunner实例
        /// </summary>
        public NetworkRunner Runner => _runner;

        /// <summary>
        /// 获取当前连接状态
        /// </summary>
        public bool IsConnected => _isConnected;

        /// <summary>
        /// 获取正在连接状态
        /// </summary>
        public bool IsConnecting => _isConnecting;

        private void Awake()
        {
            // 确保单例
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }

            _instance = this;
            DontDestroyOnLoad(gameObject);

            // 初始化统一网络管理器
            _unifiedManager = UnifiedNetworkManager.Instance;
            if (_unifiedManager != null)
            {
                // 订阅统一管理器事件
                _unifiedManager.OnStateChanged += HandleUnifiedManagerStateChanged;
                _unifiedManager.OnPlayerJoinedEvent += HandleUnifiedManagerPlayerJoined;
                _unifiedManager.OnPlayerLeftEvent += HandleUnifiedManagerPlayerLeft;
                _unifiedManager.OnConnectionError += HandleUnifiedManagerConnectionError;
            }

            // 初始化组件
            if (roomManager == null)
            {
                roomManager = GetComponentInChildren<RoomManager>();
                if (roomManager == null && transform.childCount > 0)
                {
                    roomManager = transform.GetChild(0).gameObject.AddComponent<RoomManager>();
                }
            }

            if (playerSpawner == null)
            {
                playerSpawner = GetComponentInChildren<PlayerSpawner>();
                if (playerSpawner == null && transform.childCount > 0)
                {
                    playerSpawner = transform.GetChild(0).gameObject.AddComponent<PlayerSpawner>();
                }
            }

            // 初始化状态
            _isConnecting = false;
            _isConnected = false;
            _isReconnecting = false;

            // 初始化错误处理
            InitializeErrorHandling();
        }

        /// <summary>
        /// 连接到自定义网络服务器
        /// </summary>
        public async void ConnectToServer()
        {
            if (_isConnecting) return;

            _isConnecting = true;

            if (_runner == null)
            {
                _runner = gameObject.AddComponent<NetworkRunner>();
                _runner.AddCallbacks(this);
            }

            // 设置连接参数
            var startGameArgs = new StartGameArgs
            {
                GameMode = NetworkRunner.GameMode.AutoHostOrClient,
                SessionName = "AutoRoom",
                Address = NetAddress.Any(),
                MaxPlayers = maxPlayers
            };

            try
            {
                var result = await _runner.StartGame(startGameArgs);
                if (result.Ok)
                {
                    Debug.Log("Connected to custom network server");
                }
                else
                {
                    throw new Exception(result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to connect: {ex.Message}");
                OnErrorOccurred?.Invoke($"连接失败: {ex.Message}");
                _isConnecting = false;
            }
        }

        /// <summary>
        /// 断开与服务器的连接
        /// </summary>
        public async void DisconnectFromServer()
        {
            if (_runner != null)
            {
                await _runner.Shutdown();
                _isConnected = false;
            }
        }

        /// <summary>
        /// 创建或加入房间
        /// </summary>
        public async void CreateOrJoinRoom(string roomName)
        {
            if (_runner == null || _isConnecting)
            {
                OnErrorOccurred?.Invoke("网络未准备就绪，无法创建房间");
                return;
            }

            _isConnecting = true;

            try
            {
                // 关闭当前会话（如果有）
                if (_runner.IsRunning)
                {
                    await _runner.Shutdown();
                }

                // 创建或加入指定房间
                var startGameArgs = new StartGameArgs
                {
                    GameMode = defaultGameMode,
                    SessionName = roomName,
                    Address = NetAddress.Any(),
                    MaxPlayers = maxPlayers
                };

                var result = await _runner.StartGame(startGameArgs);
                if (result.Ok)
                {
                    Debug.Log($"Connected to room: {roomName}");
                }
                else
                {
                    throw new Exception(result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to create/join room: {ex.Message}");
                OnErrorOccurred?.Invoke($"创建/加入房间失败: {ex.Message}");
            }

            _isConnecting = false;
        }

        /// <summary>
        /// 创建房间
        /// </summary>
        public async void CreateRoom(string roomName, bool? isOpen = null, bool? isVisible = null)
        {
            if (_runner == null || _isConnecting)
            {
                OnErrorOccurred?.Invoke("网络未准备就绪，无法创建房间");
                return;
            }

            _isConnecting = true;

            try
            {
                // 关闭当前会话（如果有）
                if (_runner.IsRunning)
                {
                    await _runner.Shutdown();
                }

                // 创建房间
                var startGameArgs = new StartGameArgs
                {
                    GameMode = NetworkRunner.GameMode.Host,
                    SessionName = roomName,
                    Address = NetAddress.Any(),
                    MaxPlayers = maxPlayers,
                    IsOpen = isOpen ?? true,
                    IsVisible = isVisible ?? true
                };

                var result = await _runner.StartGame(startGameArgs);
                if (result.Ok)
                {
                    Debug.Log($"Created room: {roomName}");
                }
                else
                {
                    throw new Exception(result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to create room: {ex.Message}");
                OnErrorOccurred?.Invoke($"创建房间失败: {ex.Message}");
            }

            _isConnecting = false;
        }

        /// <summary>
        /// 加入房间
        /// </summary>
        public async void JoinRoom(string roomName)
        {
            if (_runner == null || _isConnecting)
            {
                OnErrorOccurred?.Invoke("网络未准备就绪，无法加入房间");
                return;
            }

            _isConnecting = true;

            try
            {
                // 关闭当前会话（如果有）
                if (_runner.IsRunning)
                {
                    await _runner.Shutdown();
                }

                // 加入指定房间
                var startGameArgs = new StartGameArgs
                {
                    GameMode = NetworkRunner.GameMode.Client,
                    SessionName = roomName,
                    Address = NetAddress.Any(),
                    MaxPlayers = maxPlayers
                };

                var result = await _runner.StartGame(startGameArgs);
                if (result.Ok)
                {
                    Debug.Log($"Joined room: {roomName}");
                }
                else
                {
                    throw new Exception(result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to join room: {ex.Message}");
                OnErrorOccurred?.Invoke($"加入房间失败: {ex.Message}");
            }

            _isConnecting = false;
        }

        /// <summary>
        /// 启动房间列表
        /// </summary>
        public async void StartRoomList()
        {
            if (_runner == null || _isConnecting)
            {
                OnErrorOccurred?.Invoke("网络未准备就绪，无法获取房间列表");
                return;
            }

            try
            {
                // 关闭当前会话（如果有）
                if (_runner.IsRunning)
                {
                    await _runner.Shutdown();
                }

                // 启动房间列表 - 在自定义网络框架中，我们模拟房间列表
                var startGameArgs = new StartGameArgs
                {
                    GameMode = NetworkRunner.GameMode.AutoHostOrClient,
                    SessionName = null,
                    Address = NetAddress.Any(),
                    MaxPlayers = maxPlayers
                };

                var result = await _runner.StartGame(startGameArgs);
                if (result.Ok)
                {
                    Debug.Log("Started room listing");

                    // 模拟房间列表更新
                    var mockSessionList = new List<SessionInfo>
                    {
                        new SessionInfo("Room1") { PlayerCount = 3, MaxPlayers = 10, IsOpen = true, IsVisible = true },
                        new SessionInfo("Room2") { PlayerCount = 7, MaxPlayers = 10, IsOpen = true, IsVisible = true },
                        new SessionInfo("Room3") { PlayerCount = 1, MaxPlayers = 8, IsOpen = true, IsVisible = true }
                    };

                    OnRoomListUpdated?.Invoke(mockSessionList);
                }
                else
                {
                    throw new Exception(result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to start room listing: {ex.Message}");
                OnErrorOccurred?.Invoke($"获取房间列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化NetworkRunner - 简化版本用于自定义网络框架
        /// </summary>
        private async Task<bool> InitializeNetworkRunner(StartGameArgs startGameArgs)
        {
            try
            {
                var result = await _runner.StartGame(startGameArgs);

                if (result.Ok)
                {
                    _runner.ProvideInput = true;

                    // 触发Runner创建事件
                    OnRunnerCreated?.Invoke(_runner);

                    return true;
                }
                else
                {
                    OnErrorOccurred?.Invoke($"启动游戏失败: {result.ErrorMessage}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred?.Invoke($"启动游戏异常: {ex.Message}");
                return false;
            }
        }

        #region INetworkRunnerCallbacks实现

        public void OnPlayerJoined(NetworkRunner runner, PlayerRef player)
        {
            Debug.Log($"Player {player} joined");

            if (playerSpawner != null)
            {
                playerSpawner.SpawnPlayer(runner, player);
            }
        }

        public void OnPlayerLeft(NetworkRunner runner, PlayerRef player)
        {
            Debug.Log($"Player {player} left");

            if (playerSpawner != null)
            {
                playerSpawner.DespawnPlayer(runner, player);
            }
        }

        public void OnInput(NetworkRunner runner, NetworkInput input)
        {
            // 这里不处理输入，玩家控制器会处理
        }

        public void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInput input)
        {
            // 可以在这里处理输入丢失的情况
        }

        public void OnShutdown(NetworkRunner runner, NetDisconnectReason shutdownReason)
        {
            Debug.Log($"Runner shutdown: {shutdownReason}");
            _isConnected = false;

            // 通知断开连接
            OnDisconnectedFromServerEvent?.Invoke(shutdownReason);
        }

        public void OnConnectedToServer(NetworkRunner runner)
        {
            Debug.Log("Connected to server");
            _isConnected = true;
            _isConnecting = false;

            // 通知连接成功
            OnConnectedToServerEvent?.Invoke();
        }

        public void OnDisconnectedFromServer(NetworkRunner runner, NetDisconnectReason reason)
        {
            Debug.Log($"Disconnected from server: {reason}");
            _isConnected = false;

            // 通知断开连接
            OnDisconnectedFromServerEvent?.Invoke(reason);
        }

        public void OnConnectRequest(NetworkRunner runner, NetworkRunnerCallbackArgs.ConnectRequest request, byte[] token)
        {
            // 可以在这里添加自定义连接验证逻辑
            request.Accept();
        }

        public void OnConnectFailed(NetworkRunner runner, NetAddress remoteAddress, NetConnectFailedReason reason)
        {
            Debug.Log($"Connect failed: {reason}");
            _isConnected = false;
            _isConnecting = false;

            // 通知连接失败
            OnErrorOccurred?.Invoke($"连接失败: {reason}");
        }

        public void OnUserSimulationMessage(NetworkRunner runner, SimulationMessagePtr message)
        {
            // 处理自定义模拟消息
        }

        public void OnSessionListUpdated(NetworkRunner runner, List<SessionInfo> sessionList)
        {
            Debug.Log($"Session list updated: {sessionList.Count} rooms found");

            // 通知房间列表更新
            OnRoomListUpdated?.Invoke(sessionList);
        }

        public void OnCustomAuthenticationResponse(NetworkRunner runner, Dictionary<string, object> data)
        {
            // 处理自定义认证响应
        }

        public void OnHostMigration(NetworkRunner runner, HostMigrationToken hostMigrationToken)
        {
            // 处理主机迁移
        }

        public void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data)
        {
            // 处理可靠数据接收
        }

        public void OnReliableDataProgress(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress)
        {
            // 处理可靠数据传输进度
        }

        public void OnSceneLoadDone(NetworkRunner runner)
        {
            // 场景加载完成
        }

        public void OnSceneLoadStart(NetworkRunner runner)
        {
            // 场景开始加载
        }

        public void OnObjectExitAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player)
        {
            // 对象离开兴趣区域
        }

        public void OnObjectEnterAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player)
        {
            // 对象进入兴趣区域
        }

        #endregion

        #region 错误处理和恢复机制

        /// <summary>
        /// 初始化错误处理
        /// </summary>
        private void InitializeErrorHandling()
        {
            // 获取或创建错误处理器
            _errorHandler = NetworkErrorHandler.Instance;

            // 获取或创建连接监控器
            _connectionMonitor = GetComponent<NetworkConnectionMonitor>();
            if (_connectionMonitor == null)
            {
                _connectionMonitor = gameObject.AddComponent<NetworkConnectionMonitor>();
            }

            // 订阅错误处理事件
            _errorHandler.OnConnectionLost += HandleConnectionLost;
            _errorHandler.OnConnectionRestored += HandleConnectionRestored;
            _errorHandler.OnRecoveryFailed += HandleRecoveryFailed;

            // 订阅连接监控事件
            _connectionMonitor.OnConnectionStateChanged += HandleConnectionStateChanged;
            _connectionMonitor.OnConnectionUnstable += HandleConnectionUnstable;
        }

        /// <summary>
        /// 处理连接丢失
        /// </summary>
        private void HandleConnectionLost()
        {
            Debug.LogWarning("[NetworkManager] 连接丢失，开始恢复流程");
            _isConnected = false;

            if (!_isReconnecting)
            {
                StartReconnection();
            }
        }

        /// <summary>
        /// 处理连接恢复
        /// </summary>
        private void HandleConnectionRestored()
        {
            Debug.Log("[NetworkManager] 连接已恢复");
            _isConnected = true;
            _isReconnecting = false;

            // 重新启动连接监控
            if (_connectionMonitor != null)
            {
                _connectionMonitor.StartMonitoring();
            }
        }

        /// <summary>
        /// 处理恢复失败
        /// </summary>
        private void HandleRecoveryFailed(ErrorType errorType)
        {
            Debug.LogError($"[NetworkManager] 网络恢复失败: {errorType}");
            _isReconnecting = false;

            // 通知用户连接失败
            OnErrorOccurred?.Invoke("网络连接恢复失败，请检查网络设置");
        }

        /// <summary>
        /// 处理连接状态变化
        /// </summary>
        private void HandleConnectionStateChanged(ConnectionState state)
        {
            Debug.Log($"[NetworkManager] 连接状态变化: {state}");

            switch (state)
            {
                case ConnectionState.Disconnected:
                    _isConnected = false;
                    break;
                case ConnectionState.Connected:
                    _isConnected = true;
                    break;
                case ConnectionState.Unstable:
                    // 连接不稳定，但仍然连接
                    break;
                case ConnectionState.Failed:
                    _isConnected = false;
                    _errorHandler.HandleError(ErrorType.ConnectionLost, "连接失败");
                    break;
            }
        }

        /// <summary>
        /// 处理连接不稳定
        /// </summary>
        private void HandleConnectionUnstable()
        {
            Debug.LogWarning("[NetworkManager] 连接不稳定");
            _errorHandler.HandleError(ErrorType.ConnectionTimeout, "连接不稳定");
        }

        /// <summary>
        /// 开始重连
        /// </summary>
        private async void StartReconnection()
        {
            if (_isReconnecting) return;

            _isReconnecting = true;
            Debug.Log("[NetworkManager] 开始重连...");

            try
            {
                await ReconnectAsync();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[NetworkManager] 重连失败: {ex.Message}");
                _errorHandler.HandleError(ErrorType.ConnectionLost, "重连失败", ex);
            }
        }

        /// <summary>
        /// 异步重连
        /// </summary>
        public async Task ReconnectAsync()
        {
            if (_runner != null && _runner.IsRunning)
            {
                await _runner.Shutdown();
            }

            // 等待一段时间再重连
            await Task.Delay(2000);

            // 尝试重新连接
            ConnectToServer();
        }

        /// <summary>
        /// 发送心跳
        /// </summary>
        public async Task SendHeartbeat()
        {
            if (_runner == null || !_runner.IsRunning)
            {
                throw new InvalidOperationException("网络未连接");
            }

            _lastHeartbeatTime = Time.time;

            // 这里应该发送实际的心跳包
            // 简化实现：假设心跳总是成功
            await Task.Delay(10);

            // 记录心跳到连接监控器
            if (_connectionMonitor != null)
            {
                _connectionMonitor.HandleHeartbeatReceived();
            }
        }

        /// <summary>
        /// 请求重新同步
        /// </summary>
        public void RequestResync()
        {
            if (_runner == null || !_runner.IsRunning)
            {
                _errorHandler.HandleError(ErrorType.SynchronizationError, "无法请求重新同步：网络未连接");
                return;
            }

            Debug.Log("[NetworkManager] 请求重新同步");

            // 这里应该实现实际的重新同步逻辑
            // 例如：重新获取所有网络对象的状态
        }

        /// <summary>
        /// 请求完全重新同步
        /// </summary>
        public async Task RequestFullResync()
        {
            if (_runner == null || !_runner.IsRunning)
            {
                throw new InvalidOperationException("网络未连接");
            }

            Debug.Log("[NetworkManager] 请求完全重新同步");

            // 这里应该实现完全重新同步的逻辑
            // 例如：重新加载场景、重新获取所有状态等
            await Task.Delay(100); // 模拟异步操作
        }

        /// <summary>
        /// 获取网络诊断信息
        /// </summary>
        public CustomNetworking.ErrorHandling.NetworkDiagnostics GetNetworkDiagnostics()
        {
            if (_connectionMonitor != null)
            {
                return _connectionMonitor.GetCurrentDiagnostics();
            }

            return new CustomNetworking.ErrorHandling.NetworkDiagnostics
            {
                State = _isConnected ? ConnectionState.Connected : ConnectionState.Disconnected,
                Quality = NetworkQuality.Fair,
                Latency = 0f,
                PacketLoss = 0f,
                BandwidthUsage = 0f,
                Stability = _isConnected ? 1f : 0f,
                LastUpdateTime = Time.time
            };
        }

        /// <summary>
        /// 设置错误恢复配置
        /// </summary>
        public void SetErrorRecoveryConfig(ErrorRecoveryConfig config)
        {
            if (_errorHandler != null)
            {
                _errorHandler.SetAutoRecovery(config.EnableAutoRecovery);
            }
        }

        private void OnDestroy()
        {
            // 取消订阅统一管理器事件
            if (_unifiedManager != null)
            {
                _unifiedManager.OnStateChanged -= HandleUnifiedManagerStateChanged;
                _unifiedManager.OnPlayerJoinedEvent -= HandleUnifiedManagerPlayerJoined;
                _unifiedManager.OnPlayerLeftEvent -= HandleUnifiedManagerPlayerLeft;
                _unifiedManager.OnConnectionError -= HandleUnifiedManagerConnectionError;
            }

            // 取消订阅事件
            if (_errorHandler != null)
            {
                _errorHandler.OnConnectionLost -= HandleConnectionLost;
                _errorHandler.OnConnectionRestored -= HandleConnectionRestored;
                _errorHandler.OnRecoveryFailed -= HandleRecoveryFailed;
            }

            if (_connectionMonitor != null)
            {
                _connectionMonitor.OnConnectionStateChanged -= HandleConnectionStateChanged;
                _connectionMonitor.OnConnectionUnstable -= HandleConnectionUnstable;
            }
        }

        #region 统一管理器事件处理

        /// <summary>
        /// 处理统一管理器状态变化
        /// </summary>
        private void HandleUnifiedManagerStateChanged(NetworkManagerState state)
        {
            switch (state)
            {
                case NetworkManagerState.Connected:
                    _isConnected = true;
                    _isConnecting = false;
                    OnConnectedToServerEvent?.Invoke();
                    break;

                case NetworkManagerState.Disconnected:
                    _isConnected = false;
                    _isConnecting = false;
                    OnDisconnectedFromServerEvent?.Invoke(NetDisconnectReason.Shutdown);
                    break;

                case NetworkManagerState.Connecting:
                    _isConnecting = true;
                    break;

                case NetworkManagerState.Error:
                    _isConnected = false;
                    _isConnecting = false;
                    OnErrorOccurred?.Invoke("Network connection error");
                    break;
            }
        }

        /// <summary>
        /// 处理统一管理器玩家加入
        /// </summary>
        private void HandleUnifiedManagerPlayerJoined(PlayerRef player)
        {
            // 转发到现有的事件处理逻辑
            OnPlayerJoined(_runner, player);
        }

        /// <summary>
        /// 处理统一管理器玩家离开
        /// </summary>
        private void HandleUnifiedManagerPlayerLeft(PlayerRef player)
        {
            // 转发到现有的事件处理逻辑
            OnPlayerLeft(_runner, player);
        }

        /// <summary>
        /// 处理统一管理器连接错误
        /// </summary>
        private void HandleUnifiedManagerConnectionError(string error)
        {
            OnErrorOccurred?.Invoke(error);
        }

        #endregion

        #endregion
    }
}
