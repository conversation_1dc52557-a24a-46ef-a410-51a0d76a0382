using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 网络事件系统 - 提供统一的网络事件接口
    /// 使用静态事件允许不同系统之间轻松通信
    /// </summary>
    public static class NetworkEvents
    {
        // 连接事件
        public static event Action OnConnecting;
        public static event Action OnConnected;
        public static event Action<string> OnConnectFailed;
        public static event Action<NetDisconnectReason> OnDisconnected;
        
        // 房间事件
        public static event Action<RoomInfo> OnRoomJoined;
        public static event Action<RoomInfo> OnRoomLeft;
        public static event Action<RoomInfo> OnRoomCreated;
        public static event Action<List<RoomInfo>> OnRoomListUpdated;
        
        // 玩家事件
        public static event Action<NetworkRunner, NetworkObject, PlayerRef> OnPlayerSpawned;
        public static event Action<NetworkRunner, NetworkObject, PlayerRef> OnPlayerDespawned;
        public static event Action<NetworkRunner, PlayerRef> OnPlayerJoined;
        public static event Action<NetworkRunner, PlayerRef> OnPlayerLeft;
        
        // 游戏状态事件
        public static event Action OnGameStarted;
        public static event Action OnGameEnded;
        public static event Action<NetworkRunner> OnGameStartFailed;
        
        // 初始化
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        private static void Initialize()
        {
            // 在游戏启动时初始化事件订阅
            Debug.Log("NetworkEvents initialized");
        }
        
        // 公共方法，可由UI等外部组件调用
        public static void RaiseConnecting()
        {
            OnConnecting?.Invoke();
        }
        
        internal static void RaiseConnected()
        {
            OnConnected?.Invoke();
        }
        
        internal static void RaiseConnectFailed(string reason)
        {
            OnConnectFailed?.Invoke(reason);
        }
        
        internal static void RaiseDisconnected(NetDisconnectReason reason)
        {
            OnDisconnected?.Invoke(reason);
        }
        
        internal static void RaiseRoomJoined(RoomInfo roomInfo)
        {
            OnRoomJoined?.Invoke(roomInfo);
        }
        
        internal static void RaiseRoomLeft(RoomInfo roomInfo)
        {
            OnRoomLeft?.Invoke(roomInfo);
        }
        
        internal static void RaiseRoomCreated(RoomInfo roomInfo)
        {
            OnRoomCreated?.Invoke(roomInfo);
        }
        
        internal static void RaiseRoomListUpdated(List<RoomInfo> roomList)
        {
            OnRoomListUpdated?.Invoke(roomList);
        }
        
        internal static void RaisePlayerSpawned(NetworkRunner runner, NetworkObject playerObject, PlayerRef player)
        {
            OnPlayerSpawned?.Invoke(runner, playerObject, player);
        }
        
        internal static void RaisePlayerDespawned(NetworkRunner runner, NetworkObject playerObject, PlayerRef player)
        {
            OnPlayerDespawned?.Invoke(runner, playerObject, player);
        }
        
        internal static void RaisePlayerJoined(NetworkRunner runner, PlayerRef player)
        {
            OnPlayerJoined?.Invoke(runner, player);
        }
        
        internal static void RaisePlayerLeft(NetworkRunner runner, PlayerRef player)
        {
            OnPlayerLeft?.Invoke(runner, player);
        }
        
        internal static void RaiseGameStarted()
        {
            OnGameStarted?.Invoke();
        }
        
        internal static void RaiseGameEnded()
        {
            OnGameEnded?.Invoke();
        }
        
        internal static void RaiseGameStartFailed(NetworkRunner runner)
        {
            OnGameStartFailed?.Invoke(runner);
        }
    }
} 