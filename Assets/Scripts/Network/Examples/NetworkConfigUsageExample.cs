﻿﻿﻿﻿﻿﻿﻿using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using GooseDuckKill.Network;
using GooseDuckKill.Network.Models;

namespace GooseDuckKill.Network.Examples
{
    /// <summary>
    /// 网络配置使用示例
    /// 展示如何使用统一的网络配置进行前后台通信
    /// </summary>
    public class NetworkConfigUsageExample : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private Button connectButton;
        [SerializeField] private Button createRoomButton;
        [SerializeField] private Button joinRoomButton;
        [SerializeField] private Button leaveRoomButton;
        [SerializeField] private InputField roomNameInput;
        [SerializeField] private InputField passwordInput;
        [SerializeField] private Text statusText;
        [SerializeField] private Text configInfoText;
        
        [Header("Settings")]
        [SerializeField] private bool useBackendServices = true;
        [SerializeField] private bool showConfigInfo = true;
        
        // 网络组件
        private ImprovedNetworkManager _networkManager;
        private AuthManager _authManager;
        private NetworkServiceManager _serviceManager;
        
        private void Start()
        {
            InitializeComponents();
            SetupUI();
            DisplayConfigInfo();
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 获取网络管理器
            _networkManager = ImprovedNetworkManager.Instance;
            _authManager = FindFirstObjectByType<AuthManager>();
            _serviceManager = NetworkServiceManager.Instance;
            
            // 订阅事件
            if (_networkManager != null)
            {
                _networkManager.OnConnectedToServerEvent += HandleConnectedToServer;
                _networkManager.OnDisconnectedFromServerEvent += HandleDisconnectedFromServer;
                _networkManager.OnRoomJoined += HandleRoomJoined;
                _networkManager.OnRoomLeft += HandleRoomLeft;
                _networkManager.OnErrorOccurred += HandleNetworkError;
            }
            
            if (_authManager != null)
            {
                _authManager.OnAuthStateChanged += HandleAuthStateChanged;
                _authManager.OnAuthError += HandleAuthError;
            }
        }
        
        /// <summary>
        /// 设置UI
        /// </summary>
        private void SetupUI()
        {
            if (connectButton != null)
                connectButton.onClick.AddListener(OnConnectButtonClicked);
                
            if (createRoomButton != null)
                createRoomButton.onClick.AddListener(OnCreateRoomButtonClicked);
                
            if (joinRoomButton != null)
                joinRoomButton.onClick.AddListener(OnJoinRoomButtonClicked);
                
            if (leaveRoomButton != null)
                leaveRoomButton.onClick.AddListener(OnLeaveRoomButtonClicked);
            
            // 初始状态
            UpdateUIState();
        }
        
        /// <summary>
        /// 显示配置信息
        /// </summary>
        private void DisplayConfigInfo()
        {
            if (!showConfigInfo || configInfoText == null) return;
            
            var config = NetworkConfig.Instance;
            if (config == null)
            {
                configInfoText.text = "NetworkConfig not found!";
                return;
            }
            
            var configInfo = $"Environment: {(config.IsProduction ? "Production" : "Development")}\n" +
                           $"Auth Service: {config.AuthServiceUrl}\n" +
                           $"Game Service: {config.GameServiceUrl}\n" +
                           $"Room Service: {config.RoomServiceUrl}\n" +
                           $"WebSocket: {config.RoomWebSocketUrl}";
            
            configInfoText.text = configInfo;
        }
        
        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUIState()
        {
            bool isConnected = _networkManager != null && _networkManager.IsConnected;
            bool isAuthenticated = _authManager != null && _authManager.IsAuthenticated;
            bool inRoom = !string.IsNullOrEmpty(_networkManager?.CurrentRoomId);
            
            if (connectButton != null)
                connectButton.interactable = !isConnected && (!useBackendServices || isAuthenticated);
                
            if (createRoomButton != null)
                createRoomButton.interactable = isConnected && !inRoom;
                
            if (joinRoomButton != null)
                joinRoomButton.interactable = isConnected && !inRoom;
                
            if (leaveRoomButton != null)
                leaveRoomButton.interactable = isConnected && inRoom;
        }
        
        /// <summary>
        /// 更新状态文本
        /// </summary>
        private void UpdateStatusText(string message)
        {
            if (statusText != null)
            {
                statusText.text = $"[{System.DateTime.Now:HH:mm:ss}] {message}";
            }
            
            Debug.Log($"[NetworkExample] {message}");
        }
        
        #region UI事件处理
        
        private async void OnConnectButtonClicked()
        {
            UpdateStatusText("Connecting to server...");
            
            if (useBackendServices && (_authManager == null || !_authManager.IsAuthenticated))
            {
                UpdateStatusText("Please login first when using backend services.");
                
                // 示例：自动登录（实际项目中应该有登录界面）
                if (_authManager != null)
                {
                    UpdateStatusText("Attempting auto-login...");

                    // 使用回调方式而不是await
                    _authManager.LoginAsync("<EMAIL>", "testpass", (loginSuccess) =>
                    {
                        if (!loginSuccess)
                        {
                            UpdateStatusText("Auto-login failed. Please implement proper login flow.");
                            return;
                        }

                        UpdateStatusText("Auto-login successful.");

                        // 登录成功后继续连接
                        StartCoroutine(ConnectAfterLogin());
                    });

                    return; // 等待登录回调
                }
            }
            
            bool success = await _networkManager.ConnectToServerAsync();
            
            if (success)
            {
                UpdateStatusText("Connected to server successfully!");
            }
            else
            {
                UpdateStatusText("Failed to connect to server.");
            }
            
            UpdateUIState();
        }
        
        private async void OnCreateRoomButtonClicked()
        {
            string roomName = roomNameInput?.text ?? "TestRoom";
            string password = passwordInput?.text ?? "";
            
            UpdateStatusText($"Creating room: {roomName}");
            
            // 创建房间设置
            var settings = new RoomSettings
            {
                game_mode = "classic",
                map_name = "skeld",
                task_count = 10,
                duck_ratio = 0.25f
            };
            
            bool success = await _networkManager.CreateRoomAsync(roomName, password, settings);
            
            if (success)
            {
                UpdateStatusText($"Room '{roomName}' created successfully!");
            }
            else
            {
                UpdateStatusText($"Failed to create room '{roomName}'.");
            }
            
            UpdateUIState();
        }
        
        private async void OnJoinRoomButtonClicked()
        {
            string roomName = roomNameInput?.text ?? "TestRoom";
            string password = passwordInput?.text ?? "";
            
            UpdateStatusText($"Joining room: {roomName}");
            
            bool success = await _networkManager.JoinRoomAsync(roomName, password);
            
            if (success)
            {
                UpdateStatusText($"Joined room '{roomName}' successfully!");
            }
            else
            {
                UpdateStatusText($"Failed to join room '{roomName}'.");
            }
            
            UpdateUIState();
        }
        
        private async void OnLeaveRoomButtonClicked()
        {
            string currentRoom = _networkManager.CurrentRoomId;
            UpdateStatusText($"Leaving room: {currentRoom}");
            
            await _networkManager.LeaveRoomAsync();
            
            UpdateStatusText($"Left room '{currentRoom}'.");
            UpdateUIState();
        }
        
        #endregion
        
        #region 网络事件处理
        
        private void HandleConnectedToServer()
        {
            UpdateStatusText("Connected to server!");
            UpdateUIState();
        }
        
        private void HandleDisconnectedFromServer(string reason)
        {
            UpdateStatusText($"Disconnected from server: {reason}");
            UpdateUIState();
        }
        
        private void HandleRoomJoined(string roomId)
        {
            UpdateStatusText($"Joined room: {roomId}");
            UpdateUIState();
        }
        
        private void HandleRoomLeft(string roomId)
        {
            UpdateStatusText($"Left room: {roomId}");
            UpdateUIState();
        }
        
        private void HandleNetworkError(string error)
        {
            UpdateStatusText($"Network error: {error}");
            UpdateUIState();
        }
        
        #endregion
        
        #region 认证事件处理
        
        private void HandleAuthStateChanged(bool isAuthenticated)
        {
            UpdateStatusText($"Auth state changed: {(isAuthenticated ? "Authenticated" : "Not Authenticated")}");
            UpdateUIState();
        }
        
        private void HandleAuthError(string error)
        {
            UpdateStatusText($"Auth error: {error}");
        }
        
        #endregion
        
        #region 测试方法
        
        /// <summary>
        /// 测试网络配置
        /// </summary>
        [ContextMenu("Test Network Config")]
        public void TestNetworkConfig()
        {
            var config = NetworkConfig.Instance;
            if (config == null)
            {
                Debug.LogError("NetworkConfig not found!");
                return;
            }
            
            Debug.Log("=== Network Configuration Test ===");
            config.PrintCurrentConfig();
            
            // 测试URL生成
            Debug.Log($"Auth Login URL: {config.GetApiUrl(ServiceType.Auth, "/auth/login")}");
            Debug.Log($"Room List URL: {config.GetApiUrl(ServiceType.Room, "/rooms")}");
            Debug.Log($"Game Start URL: {config.GetApiUrl(ServiceType.Game, "/games")}");
            Debug.Log($"Room WebSocket URL: {config.GetWebSocketUrl(ServiceType.Room)}");
            
            // 验证配置
            bool isValid = config.ValidateConfig();
            Debug.Log($"Configuration is {(isValid ? "valid" : "invalid")}");
        }
        
        /// <summary>
        /// 测试服务健康状态
        /// </summary>
        [ContextMenu("Test Service Health")]
        public void TestServiceHealth()
        {
            if (_serviceManager == null)
            {
                Debug.LogError("NetworkServiceManager not found!");
                return;
            }
            
            Debug.Log("Testing service health...");
            
            _serviceManager.CheckServiceHealth(ServiceType.Auth, isHealthy =>
            {
                Debug.Log($"Auth Service: {(isHealthy ? "Healthy" : "Unhealthy")}");
            });
            
            _serviceManager.CheckServiceHealth(ServiceType.Game, isHealthy =>
            {
                Debug.Log($"Game Service: {(isHealthy ? "Healthy" : "Unhealthy")}");
            });
            
            _serviceManager.CheckServiceHealth(ServiceType.Room, isHealthy =>
            {
                Debug.Log($"Room Service: {(isHealthy ? "Healthy" : "Unhealthy")}");
            });
        }
        
        #endregion

        /// <summary>
        /// 登录成功后连接服务器的协程
        /// </summary>
        private IEnumerator ConnectAfterLogin()
        {
            yield return new WaitForSeconds(0.5f); // 等待一下确保登录状态更新

            // 启动异步连接任务
            var connectTask = _networkManager.ConnectToServerAsync();

            // 等待任务完成
            while (!connectTask.IsCompleted)
            {
                yield return null;
            }

            bool success = connectTask.Result;

            if (success)
            {
                UpdateStatusText("Connected to server successfully!");
            }
            else
            {
                UpdateStatusText("Failed to connect to server.");
            }

            UpdateUIState();
        }

        private void OnDestroy()
        {
            // 清理事件订阅
            if (_networkManager != null)
            {
                _networkManager.OnConnectedToServerEvent -= HandleConnectedToServer;
                _networkManager.OnDisconnectedFromServerEvent -= HandleDisconnectedFromServer;
                _networkManager.OnRoomJoined -= HandleRoomJoined;
                _networkManager.OnRoomLeft -= HandleRoomLeft;
                _networkManager.OnErrorOccurred -= HandleNetworkError;
            }
            
            if (_authManager != null)
            {
                _authManager.OnAuthStateChanged -= HandleAuthStateChanged;
                _authManager.OnAuthError -= HandleAuthError;
            }
        }
    }
}
