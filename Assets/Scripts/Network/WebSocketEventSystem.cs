using System;
using System.Collections.Generic;
using UnityEngine;
using GooseDuckKill.Network.Models;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// WebSocket事件系统
    /// 提供类型安全的WebSocket消息事件分发
    /// </summary>
    public class WebSocketEventSystem : MonoBehaviour
    {
        // 单例实例
        private static WebSocketEventSystem _instance;
        public static WebSocketEventSystem Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("WebSocketEventSystem");
                    _instance = go.AddComponent<WebSocketEventSystem>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }
        
        #region 房间相关事件
        
        public event Action<string> OnRoomJoined;
        public event Action<string> OnRoomLeft;
        public event Action<RoomUpdatedData> OnRoomUpdated;
        public event Action<PlayerJoinedData> OnPlayerJoined;
        public event Action<PlayerLeftData> OnPlayerLeft;
        public event Action<string> OnPlayerReady;
        
        #endregion
        
        #region 游戏相关事件
        
        public event Action<GameStateUpdateData> OnGameStarted;
        public event Action<GameStateUpdateData> OnGameStateUpdated;
        public event Action<PlayerMoveData> OnPlayerMoved;
        public event Action<PlayerActionData> OnPlayerAction;
        
        #endregion
        
        #region 投票相关事件
        
        public event Action<VotingStartedData> OnVotingStarted;
        public event Action<VoteCastData> OnVoteCast;
        public event Action<VotingEndedData> OnVotingEnded;
        
        #endregion
        
        #region 任务相关事件
        
        public event Action<TaskStartedData> OnTaskStarted;
        public event Action<TaskProgressData> OnTaskProgress;
        public event Action<TaskCompletedData> OnTaskCompleted;
        
        #endregion
        
        #region 游戏事件
        
        public event Action<PlayerKilledData> OnPlayerKilled;
        public event Action<BodyReportedData> OnBodyReported;
        public event Action<EmergencyMeetingData> OnEmergencyMeeting;
        public event Action<SabotageData> OnSabotage;
        
        #endregion
        
        #region 通信相关事件
        
        public event Action<ChatMessageData> OnChatMessage;
        public event Action<HeartbeatData> OnHeartbeat;
        public event Action<ErrorMessageData> OnError;
        
        #endregion
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }
        
        /// <summary>
        /// 处理WebSocket消息并触发相应事件
        /// </summary>
        public void HandleMessage(WebSocketMessage message)
        {
            try
            {
                switch (message.type)
                {
                    case WebSocketMessageTypes.JOIN_ROOM:
                        var joinData = message.GetData<JoinRoomData>();
                        OnRoomJoined?.Invoke(joinData?.room_id);
                        break;
                        
                    case WebSocketMessageTypes.LEAVE_ROOM:
                        OnRoomLeft?.Invoke(message.room_id);
                        break;
                        
                    case WebSocketMessageTypes.ROOM_UPDATED:
                        var roomData = message.GetData<RoomUpdatedData>();
                        OnRoomUpdated?.Invoke(roomData);
                        break;
                        
                    case WebSocketMessageTypes.PLAYER_JOINED:
                        var playerJoinedData = message.GetData<PlayerJoinedData>();
                        OnPlayerJoined?.Invoke(playerJoinedData);
                        break;
                        
                    case WebSocketMessageTypes.PLAYER_LEFT:
                        var playerLeftData = message.GetData<PlayerLeftData>();
                        OnPlayerLeft?.Invoke(playerLeftData);
                        break;
                        
                    case WebSocketMessageTypes.PLAYER_READY:
                        OnPlayerReady?.Invoke(message.sender_id);
                        break;
                        
                    case WebSocketMessageTypes.GAME_STARTED:
                        var gameStartedData = message.GetData<GameStateUpdateData>();
                        OnGameStarted?.Invoke(gameStartedData);
                        break;
                        
                    case WebSocketMessageTypes.GAME_STATE_UPDATE:
                        var gameStateData = message.GetData<GameStateUpdateData>();
                        OnGameStateUpdated?.Invoke(gameStateData);
                        break;
                        
                    case WebSocketMessageTypes.PLAYER_MOVE:
                        var moveData = message.GetData<PlayerMoveData>();
                        OnPlayerMoved?.Invoke(moveData);
                        break;
                        
                    case WebSocketMessageTypes.PLAYER_ACTION:
                        var actionData = message.GetData<PlayerActionData>();
                        OnPlayerAction?.Invoke(actionData);
                        break;
                        
                    case WebSocketMessageTypes.VOTING_STARTED:
                        var votingStartedData = message.GetData<VotingStartedData>();
                        OnVotingStarted?.Invoke(votingStartedData);
                        break;
                        
                    case WebSocketMessageTypes.VOTE_CAST:
                        var voteData = message.GetData<VoteCastData>();
                        OnVoteCast?.Invoke(voteData);
                        break;
                        
                    case WebSocketMessageTypes.VOTING_ENDED:
                        var votingEndedData = message.GetData<VotingEndedData>();
                        OnVotingEnded?.Invoke(votingEndedData);
                        break;
                        
                    case WebSocketMessageTypes.TASK_STARTED:
                        var taskStartedData = message.GetData<TaskStartedData>();
                        OnTaskStarted?.Invoke(taskStartedData);
                        break;
                        
                    case WebSocketMessageTypes.TASK_PROGRESS:
                        var taskProgressData = message.GetData<TaskProgressData>();
                        OnTaskProgress?.Invoke(taskProgressData);
                        break;
                        
                    case WebSocketMessageTypes.TASK_COMPLETED:
                        var taskCompletedData = message.GetData<TaskCompletedData>();
                        OnTaskCompleted?.Invoke(taskCompletedData);
                        break;
                        
                    case WebSocketMessageTypes.PLAYER_KILLED:
                        var killedData = message.GetData<PlayerKilledData>();
                        OnPlayerKilled?.Invoke(killedData);
                        break;
                        
                    case WebSocketMessageTypes.BODY_REPORTED:
                        var bodyData = message.GetData<BodyReportedData>();
                        OnBodyReported?.Invoke(bodyData);
                        break;
                        
                    case WebSocketMessageTypes.EMERGENCY_MEETING:
                        var meetingData = message.GetData<EmergencyMeetingData>();
                        OnEmergencyMeeting?.Invoke(meetingData);
                        break;
                        
                    case WebSocketMessageTypes.SABOTAGE:
                        var sabotageData = message.GetData<SabotageData>();
                        OnSabotage?.Invoke(sabotageData);
                        break;
                        
                    case WebSocketMessageTypes.CHAT_MESSAGE:
                        var chatData = message.GetData<ChatMessageData>();
                        OnChatMessage?.Invoke(chatData);
                        break;
                        
                    case WebSocketMessageTypes.HEARTBEAT:
                        var heartbeatData = message.GetData<HeartbeatData>();
                        OnHeartbeat?.Invoke(heartbeatData);
                        break;
                        
                    case WebSocketMessageTypes.ERROR:
                        var errorData = message.GetData<ErrorMessageData>();
                        OnError?.Invoke(errorData);
                        break;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[WebSocketEventSystem] Error handling message {message.type}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 清除所有事件订阅
        /// </summary>
        public void ClearAllEvents()
        {
            OnRoomJoined = null;
            OnRoomLeft = null;
            OnRoomUpdated = null;
            OnPlayerJoined = null;
            OnPlayerLeft = null;
            OnPlayerReady = null;
            
            OnGameStarted = null;
            OnGameStateUpdated = null;
            OnPlayerMoved = null;
            OnPlayerAction = null;
            
            OnVotingStarted = null;
            OnVoteCast = null;
            OnVotingEnded = null;
            
            OnTaskStarted = null;
            OnTaskProgress = null;
            OnTaskCompleted = null;
            
            OnPlayerKilled = null;
            OnBodyReported = null;
            OnEmergencyMeeting = null;
            OnSabotage = null;
            
            OnChatMessage = null;
            OnHeartbeat = null;
            OnError = null;
        }
        
        /// <summary>
        /// 获取事件订阅统计
        /// </summary>
        public Dictionary<string, int> GetEventSubscriptionStats()
        {
            var stats = new Dictionary<string, int>();
            
            stats["OnRoomJoined"] = OnRoomJoined?.GetInvocationList().Length ?? 0;
            stats["OnRoomLeft"] = OnRoomLeft?.GetInvocationList().Length ?? 0;
            stats["OnRoomUpdated"] = OnRoomUpdated?.GetInvocationList().Length ?? 0;
            stats["OnPlayerJoined"] = OnPlayerJoined?.GetInvocationList().Length ?? 0;
            stats["OnPlayerLeft"] = OnPlayerLeft?.GetInvocationList().Length ?? 0;
            stats["OnPlayerReady"] = OnPlayerReady?.GetInvocationList().Length ?? 0;
            
            stats["OnGameStarted"] = OnGameStarted?.GetInvocationList().Length ?? 0;
            stats["OnGameStateUpdated"] = OnGameStateUpdated?.GetInvocationList().Length ?? 0;
            stats["OnPlayerMoved"] = OnPlayerMoved?.GetInvocationList().Length ?? 0;
            stats["OnPlayerAction"] = OnPlayerAction?.GetInvocationList().Length ?? 0;
            
            stats["OnVotingStarted"] = OnVotingStarted?.GetInvocationList().Length ?? 0;
            stats["OnVoteCast"] = OnVoteCast?.GetInvocationList().Length ?? 0;
            stats["OnVotingEnded"] = OnVotingEnded?.GetInvocationList().Length ?? 0;
            
            stats["OnTaskStarted"] = OnTaskStarted?.GetInvocationList().Length ?? 0;
            stats["OnTaskProgress"] = OnTaskProgress?.GetInvocationList().Length ?? 0;
            stats["OnTaskCompleted"] = OnTaskCompleted?.GetInvocationList().Length ?? 0;
            
            stats["OnPlayerKilled"] = OnPlayerKilled?.GetInvocationList().Length ?? 0;
            stats["OnBodyReported"] = OnBodyReported?.GetInvocationList().Length ?? 0;
            stats["OnEmergencyMeeting"] = OnEmergencyMeeting?.GetInvocationList().Length ?? 0;
            stats["OnSabotage"] = OnSabotage?.GetInvocationList().Length ?? 0;
            
            stats["OnChatMessage"] = OnChatMessage?.GetInvocationList().Length ?? 0;
            stats["OnHeartbeat"] = OnHeartbeat?.GetInvocationList().Length ?? 0;
            stats["OnError"] = OnError?.GetInvocationList().Length ?? 0;
            
            return stats;
        }
        
        /// <summary>
        /// 打印事件订阅统计
        /// </summary>
        [ContextMenu("Print Event Stats")]
        public void PrintEventStats()
        {
            var stats = GetEventSubscriptionStats();
            var totalSubscriptions = 0;
            
            Debug.Log("=== WebSocket Event Subscription Stats ===");
            foreach (var kvp in stats)
            {
                if (kvp.Value > 0)
                {
                    Debug.Log($"{kvp.Key}: {kvp.Value} subscribers");
                    totalSubscriptions += kvp.Value;
                }
            }
            Debug.Log($"Total active subscriptions: {totalSubscriptions}");
        }
        
        private void OnDestroy()
        {
            ClearAllEvents();
        }
    }
}
