﻿﻿﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace GooseDuckKill.Network.Models
{
    /// <summary>
    /// Unity JsonUtility兼容的数据模型
    /// 使用public字段而不是属性，确保与Unity序列化系统兼容
    /// </summary>
    
    #region 基础数据类型
    
    [Serializable]
    public class Vector3Data
    {
        public float x;
        public float y;
        public float z;
        
        public Vector3Data() { }
        
        public Vector3Data(float x, float y, float z)
        {
            this.x = x;
            this.y = y;
            this.z = z;
        }
        
        public Vector3 ToVector3()
        {
            return new Vector3(x, y, z);
        }
        
        public static Vector3Data FromVector3(Vector3 vector)
        {
            return new Vector3Data(vector.x, vector.y, vector.z);
        }
    }
    
    [Serializable]
    public class Vector2Data
    {
        public float x;
        public float y;
        
        public Vector2Data() { }
        
        public Vector2Data(float x, float y)
        {
            this.x = x;
            this.y = y;
        }
        
        public Vector2 ToVector2()
        {
            return new Vector2(x, y);
        }
        
        public static Vector2Data FromVector2(Vector2 vector)
        {
            return new Vector2Data(vector.x, vector.y);
        }
    }
    
    #endregion
    
    #region 房间相关模型
    
    [Serializable]
    public class Room
    {
        public string id;
        public string name;
        public string host_id;
        public int max_players;
        public int current_size;
        public string status;
        public bool is_private;
        public string password;
        public RoomSettings settings;
        public string created_at;
        public string updated_at;
        
        public Room()
        {
            settings = new RoomSettings();
        }
    }
    
    [Serializable]
    public class RoomSettings
    {
        public string game_mode = "classic";
        public string map_name = "skeld";
        public int task_count = 10;
        public float duck_ratio = 0.25f;
        public int meeting_time = 120;
        public int voting_time = 30;
        public int emergency_meetings = 1;
        public int kill_cooldown = 45;
        public float player_speed = 1.0f;
        public float crew_vision = 1.0f;
        public float impostor_vision = 1.5f;
        public bool confirm_ejects = true;
        public bool visual_tasks = true;
        public bool anonymous_votes = false;
    }
    
    [Serializable]
    public class CreateRoomRequest
    {
        public string name;
        public int max_players = 10;
        public bool is_private = false;
        public string password = "";
        public RoomSettings settings;
        
        public CreateRoomRequest()
        {
            settings = new RoomSettings();
        }
    }
    
    [Serializable]
    public class JoinRoomRequest
    {
        public string password = "";
    }
    
    #endregion
    
    #region 玩家相关模型
    
    [Serializable]
    public class Player
    {
        public string id;
        public string username;
        public string role;
        public bool is_alive = true;
        public Vector3Data position;
        public bool is_ready = false;
        public string color;
        public string hat;
        public string pet;
        
        public Player()
        {
            position = new Vector3Data();
        }
    }
    
    [Serializable]
    public class GamePlayer
    {
        public string id;
        public string user_id;
        public string username;
        public string role;
        public bool is_alive = true;
        public bool is_ready = false;
        public Vector3Data position;
        public string color;
        public string hat;
        public string pet;
        public int tasks_completed = 0;
        public int total_tasks = 0;
        public int kills = 0;
        public int votes_cast = 0;
        public int emergency_meetings_called = 0;
        public string joined_at;
        
        public GamePlayer()
        {
            position = new Vector3Data();
        }
    }
    
    #endregion
    
    #region 游戏相关模型
    
    [Serializable]
    public class Game
    {
        public string id;
        public string room_id;
        public string phase;
        public int round;
        public List<GamePlayer> players;
        public GameSettings settings;
        public List<GameTask> tasks;
        public List<GameEvent> events;
        public VotingSession voting;
        public GameStatistics statistics;
        public string started_at;
        public string ended_at;
        public string created_at;
        public string updated_at;
        
        public Game()
        {
            players = new List<GamePlayer>();
            settings = new GameSettings();
            tasks = new List<GameTask>();
            events = new List<GameEvent>();
            statistics = new GameStatistics();
        }
    }
    
    [Serializable]
    public class GameSettings
    {
        public string game_mode = "classic";
        public string map_name = "skeld";
        public int max_players = 10;
        public int duck_count = 2;
        public int task_count = 10;
        public int meeting_time = 120;
        public int voting_time = 30;
        public int discussion_time = 15;
        public int emergency_meetings = 1;
        public int emergency_cooldown = 15;
        public int kill_cooldown = 45;
        public float kill_distance = 1.0f;
        public float player_speed = 1.0f;
        public float crew_vision = 1.0f;
        public float impostor_vision = 1.5f;
        public bool confirm_ejects = true;
        public bool visual_tasks = true;
        public bool anonymous_votes = false;
        public string task_bar_updates = "always";
    }
    
    [Serializable]
    public class GameTask
    {
        public string id;
        public string name;
        public string type;
        public string location;
        public string description;
        public string assigned_to;
        public string status = "pending";
        public float progress = 0f;
        public Vector3Data position;
        public bool is_visual = false;
        public float duration = 0f;
        public string started_at;
        public string completed_at;
        
        public GameTask()
        {
            position = new Vector3Data();
        }
    }
    
    [Serializable]
    public class GameEvent
    {
        public string id;
        public string type;
        public string player_id;
        public string target_id;
        public string location;
        public string data; // JSON字符串
        public string timestamp;
    }
    
    [Serializable]
    public class GameStatistics
    {
        public int total_players = 0;
        public int goose_count = 0;
        public int duck_count = 0;
        public int tasks_completed = 0;
        public int total_tasks = 0;
        public int players_killed = 0;
        public int emergency_meetings = 0;
        public int body_reports = 0;
        public int votes_cast = 0;
        public int players_ejected = 0;
        public float game_duration = 0f;
        public string winner = "";
        public string win_reason = "";
    }
    
    #endregion
    
    #region 投票相关模型
    
    [Serializable]
    public class VotingSession
    {
        public string id;
        public string type = "emergency";
        public string caller_id;
        public string reason;
        public List<Vote> votes;
        public List<string> candidates;
        public int discussion_time = 15;
        public int voting_time = 30;
        public string started_at;
        public string ends_at;
        public VotingResult result;
        
        public VotingSession()
        {
            votes = new List<Vote>();
            candidates = new List<string>();
        }
    }
    
    [Serializable]
    public class Vote
    {
        public string voter_id;
        public string target_id;
        public bool is_skip = false;
        public string timestamp;
    }
    
    [Serializable]
    public class VotingResult
    {
        public string ejected_player_id;
        public bool was_impostor;
        public string vote_counts; // JSON字符串表示Dictionary
        public int skip_count = 0;
        public int total_votes = 0;
        public bool is_tie = false;
    }
    
    #endregion
    
    #region 用户相关模型
    
    [Serializable]
    public class User
    {
        public string id;
        public string username;
        public string email;
        public string display_name;
        public string avatar_url;
        public int level = 1;
        public int experience = 0;
        public int coins = 0;
        public int gems = 0;
        public bool is_online = false;
        public string last_seen;
        public string created_at;
        public string updated_at;
        public UserProfile profile;
        public UserStatistics statistics;
        
        public User()
        {
            profile = new UserProfile();
            statistics = new UserStatistics();
        }
    }
    
    [Serializable]
    public class UserProfile
    {
        public string bio = "";
        public string country = "";
        public string language = "en";
        public string timezone = "UTC";
        public string birthday;
        public string favorite_color = "red";
        public string favorite_hat = "none";
        public string favorite_pet = "none";
        public List<string> achievements;
        public List<string> badges;
        
        public UserProfile()
        {
            achievements = new List<string>();
            badges = new List<string>();
        }
    }
    
    [Serializable]
    public class UserStatistics
    {
        public int games_played = 0;
        public int games_won = 0;
        public int games_lost = 0;
        public float win_rate = 0f;
        public int total_playtime = 0;
        public RoleStatistics as_goose;
        public RoleStatistics as_duck;
        public int tasks_completed = 0;
        public int emergency_meetings_called = 0;
        public int bodies_reported = 0;
        public int votes_cast = 0;
        public int correct_votes = 0;
        public int times_ejected = 0;
        public int times_killed = 0;
        public float survival_rate = 0f;
        
        public UserStatistics()
        {
            as_goose = new RoleStatistics();
            as_duck = new RoleStatistics();
        }
    }
    
    [Serializable]
    public class RoleStatistics
    {
        public int games_played = 0;
        public int games_won = 0;
        public float win_rate = 0f;
        public int kills = 0;
        public int sabotages = 0;
        public float average_kill_time = 0f;
    }
    
    #endregion
    
    #region 认证相关模型
    
    [Serializable]
    public class LoginRequest
    {
        public string email;        // 与后台保持一致，使用email而不是username
        public string password;
        public bool remember_me = false; // 可选字段，后台可忽略
    }
    
    [Serializable]
    public class RegisterRequest
    {
        public string username;
        public string email;
        public string password;
        public string confirm_password;
        public string display_name;
        public bool accept_terms = false;
    }
    
    [Serializable]
    public class LoginResponse
    {
        public string access_token;
        public string refresh_token;
        public string expires_at;      // 与后台保持一致，使用expires_at而不是expires_in
        public User user;

        public LoginResponse()
        {
            user = new User();
        }

        // 兼容性方法：从expires_at计算expires_in
        public int GetExpiresIn()
        {
            if (DateTime.TryParse(expires_at, out DateTime expireTime))
            {
                return (int)(expireTime - DateTime.UtcNow).TotalSeconds;
            }
            return 0;
        }
    }
    
    [Serializable]
    public class RegisterResponse
    {
        public string user_id;
        public string message;
        public bool verification_required = false;
    }
    
    [Serializable]
    public class RefreshTokenRequest
    {
        public string refresh_token;
    }

    [Serializable]
    public class ChangePasswordRequest
    {
        public string old_password;     // 与后台保持一致，使用old_password而不是current_password
        public string new_password;
        public string confirm_password; // 前台验证用，后台可忽略
    }

    [Serializable]
    public class ForgotPasswordRequest
    {
        public string email;
    }

    [Serializable]
    public class ResetPasswordRequest
    {
        public string token;
        public string new_password;
        public string confirm_password;
    }

    #endregion
    
    #region API响应模型
    
    [Serializable]
    public class APIResponse<T>
    {
        public int code;
        public string message;
        public T data;
        public long timestamp;
        public string request_id;
        
        public bool IsSuccess => code == 200;
        
        public DateTime GetTimestamp()
        {
            return DateTimeOffset.FromUnixTimeSeconds(timestamp).DateTime;
        }
    }
    
    [Serializable]
    public class ErrorResponse
    {
        public int code;
        public string message;
        public string details;
        public long timestamp;
        public string request_id;
        public List<FieldError> errors;
        
        public ErrorResponse()
        {
            errors = new List<FieldError>();
        }
    }
    
    [Serializable]
    public class FieldError
    {
        public string field;
        public string message;
        public string code;
    }
    
    #endregion
}
