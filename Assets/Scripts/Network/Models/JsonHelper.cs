using System;
using System.Collections.Generic;
using UnityEngine;

namespace GooseDuckKill.Network.Models
{
    /// <summary>
    /// JSON序列化助手类
    /// 提供Unity JsonUtility的扩展功能，支持复杂数据类型的序列化
    /// 作为Newtonsoft.Json的替代方案
    /// </summary>
    public static class JsonHelper
    {
        /// <summary>
        /// 序列化对象为JSON字符串
        /// </summary>
        public static string ToJson(object obj, bool prettyPrint = false)
        {
            if (obj == null) return "null";
            
            try
            {
                return JsonUtility.ToJson(obj, prettyPrint);
            }
            catch (Exception ex)
            {
                Debug.LogError($"JSON序列化失败: {ex.Message}");
                return "{}";
            }
        }
        
        /// <summary>
        /// 从JSON字符串反序列化对象
        /// </summary>
        public static T FromJson<T>(string json)
        {
            if (string.IsNullOrEmpty(json)) return default(T);
            
            try
            {
                return JsonUtility.FromJson<T>(json);
            }
            catch (Exception ex)
            {
                Debug.LogError($"JSON反序列化失败: {ex.Message}");
                return default(T);
            }
        }
        
        /// <summary>
        /// 序列化列表为JSON字符串
        /// </summary>
        public static string ToJson<T>(List<T> list, bool prettyPrint = false)
        {
            if (list == null) return "[]";
            
            var wrapper = new ListWrapper<T> { items = list };
            return ToJson(wrapper, prettyPrint);
        }
        
        /// <summary>
        /// 从JSON字符串反序列化列表
        /// </summary>
        public static List<T> FromJsonList<T>(string json)
        {
            if (string.IsNullOrEmpty(json)) return new List<T>();
            
            try
            {
                var wrapper = FromJson<ListWrapper<T>>(json);
                return wrapper?.items ?? new List<T>();
            }
            catch (Exception ex)
            {
                Debug.LogError($"JSON列表反序列化失败: {ex.Message}");
                return new List<T>();
            }
        }
        
        /// <summary>
        /// 序列化字典为JSON字符串
        /// </summary>
        public static string ToJson<TKey, TValue>(Dictionary<TKey, TValue> dict, bool prettyPrint = false)
        {
            if (dict == null) return "{}";
            
            var wrapper = new DictionaryWrapper<TKey, TValue> { dict = dict };
            return ToJson(wrapper, prettyPrint);
        }
        
        /// <summary>
        /// 从JSON字符串反序列化字典
        /// </summary>
        public static Dictionary<TKey, TValue> FromJsonDict<TKey, TValue>(string json)
        {
            if (string.IsNullOrEmpty(json)) return new Dictionary<TKey, TValue>();
            
            try
            {
                var wrapper = FromJson<DictionaryWrapper<TKey, TValue>>(json);
                return wrapper?.dict ?? new Dictionary<TKey, TValue>();
            }
            catch (Exception ex)
            {
                Debug.LogError($"JSON字典反序列化失败: {ex.Message}");
                return new Dictionary<TKey, TValue>();
            }
        }
        
        /// <summary>
        /// 深拷贝对象
        /// </summary>
        public static T DeepCopy<T>(T obj)
        {
            if (obj == null) return default(T);
            
            var json = ToJson(obj);
            return FromJson<T>(json);
        }
        
        /// <summary>
        /// 验证JSON字符串格式
        /// </summary>
        public static bool IsValidJson(string json)
        {
            if (string.IsNullOrEmpty(json)) return false;
            
            try
            {
                // 尝试解析为通用对象
                JsonUtility.FromJson<object>(json);
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 合并两个JSON对象
        /// </summary>
        public static string MergeJson(string json1, string json2)
        {
            try
            {
                // 这是一个简化的合并实现
                // 实际项目中可能需要更复杂的合并逻辑
                if (string.IsNullOrEmpty(json1)) return json2;
                if (string.IsNullOrEmpty(json2)) return json1;
                
                // 简单的字符串合并（仅作示例）
                var obj1 = json1.Trim().TrimStart('{').TrimEnd('}');
                var obj2 = json2.Trim().TrimStart('{').TrimEnd('}');
                
                if (string.IsNullOrEmpty(obj1)) return json2;
                if (string.IsNullOrEmpty(obj2)) return json1;
                
                return "{" + obj1 + "," + obj2 + "}";
            }
            catch (Exception ex)
            {
                Debug.LogError($"JSON合并失败: {ex.Message}");
                return json1;
            }
        }
    }
    
    /// <summary>
    /// 列表包装器，用于JsonUtility序列化列表
    /// </summary>
    [Serializable]
    public class ListWrapper<T>
    {
        public List<T> items;
    }
    
    /// <summary>
    /// 字典包装器，用于JsonUtility序列化字典
    /// </summary>
    [Serializable]
    public class DictionaryWrapper<TKey, TValue>
    {
        public Dictionary<TKey, TValue> dict;
    }
    
    /// <summary>
    /// JSON属性特性，用于标记字段的JSON名称
    /// 模拟Newtonsoft.Json的JsonProperty特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Field | AttributeTargets.Property)]
    public class JsonPropertyAttribute : Attribute
    {
        public string PropertyName { get; }
        
        public JsonPropertyAttribute(string propertyName)
        {
            PropertyName = propertyName;
        }
    }
    
    /// <summary>
    /// 扩展方法类
    /// </summary>
    public static class JsonExtensions
    {
        /// <summary>
        /// 将对象转换为JSON字符串
        /// </summary>
        public static string ToJson(this object obj, bool prettyPrint = false)
        {
            return JsonHelper.ToJson(obj, prettyPrint);
        }
        
        /// <summary>
        /// 从JSON字符串创建对象
        /// </summary>
        public static T FromJson<T>(this string json)
        {
            return JsonHelper.FromJson<T>(json);
        }
        
        /// <summary>
        /// 检查字符串是否为有效的JSON
        /// </summary>
        public static bool IsValidJson(this string json)
        {
            return JsonHelper.IsValidJson(json);
        }
    }
    
    /// <summary>
    /// API响应基类（使用Unity JsonUtility）
    /// </summary>
    [Serializable]
    public class UnityAPIResponse<T>
    {
        public int code;
        public string message;
        public T data;
        public long timestamp;
        public string request_id;
        
        public bool IsSuccess => code == 200;
        
        public DateTime GetTimestamp()
        {
            return DateTimeOffset.FromUnixTimeSeconds(timestamp).DateTime;
        }
    }
    
    /// <summary>
    /// 分页响应类（使用Unity JsonUtility）
    /// </summary>
    [Serializable]
    public class UnityPaginatedResponse<T>
    {
        public List<T> items = new List<T>();
        public int total;
        public int page = 1;
        public int page_size = 20;
        public int total_pages;
        public bool has_next;
        public bool has_previous;
    }
    
    /// <summary>
    /// WebSocket消息类（使用Unity JsonUtility）
    /// </summary>
    [Serializable]
    public class UnityWebSocketMessage
    {
        public string type;
        public string data; // 存储为JSON字符串
        public long timestamp;
        public string sender_id;
        public string room_id;
        public string game_id;
        public long sequence;
        
        public UnityWebSocketMessage() { }
        
        public UnityWebSocketMessage(string messageType, object messageData, string roomId = null, string gameId = null)
        {
            type = messageType;
            data = messageData?.ToJson() ?? "{}";
            room_id = roomId;
            game_id = gameId;
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        }
        
        public T GetData<T>()
        {
            if (string.IsNullOrEmpty(data)) return default(T);
            return data.FromJson<T>();
        }
        
        public void SetData<T>(T messageData)
        {
            data = messageData?.ToJson() ?? "{}";
        }
    }
}
