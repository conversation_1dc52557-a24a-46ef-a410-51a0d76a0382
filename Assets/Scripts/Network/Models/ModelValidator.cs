﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace GooseDuckKill.Network.Models
{
    /// <summary>
    /// 数据模型验证器
    /// 确保前台数据模型与后台服务的一致性和有效性
    /// </summary>
    public static class ModelValidator
    {
        #region 用户数据验证
        
        /// <summary>
        /// 验证登录请求
        /// </summary>
        public static ValidationResult ValidateLoginRequest(LoginRequest request)
        {
            var result = new ValidationResult();
            
            if (request == null)
            {
                result.AddError("request", "登录请求不能为空");
                return result;
            }
            
            if (string.IsNullOrEmpty(request.email))
            {
                result.AddError("email", "邮箱不能为空");
            }
            else if (!IsValidEmail(request.email))
            {
                result.AddError("email", "邮箱格式无效");
            }

            if (string.IsNullOrEmpty(request.password))
            {
                result.AddError("password", "密码不能为空");
            }
            
            return result;
        }
        
        /// <summary>
        /// 验证注册请求
        /// </summary>
        public static ValidationResult ValidateRegisterRequest(RegisterRequest request)
        {
            var result = new ValidationResult();
            
            if (request == null)
            {
                result.AddError("request", "注册请求不能为空");
                return result;
            }
            
            // 验证用户名
            if (string.IsNullOrEmpty(request.username))
            {
                result.AddError("username", "用户名不能为空");
            }
            else if (!IsValidUsername(request.username))
            {
                result.AddError("username", "用户名格式无效（3-20位，只能包含字母、数字、下划线）");
            }

            // 验证邮箱
            if (string.IsNullOrEmpty(request.email))
            {
                result.AddError("email", "邮箱不能为空");
            }
            else if (!IsValidEmail(request.email))
            {
                result.AddError("email", "邮箱格式无效");
            }

            // 验证密码
            if (string.IsNullOrEmpty(request.password))
            {
                result.AddError("password", "密码不能为空");
            }
            else if (!IsValidPassword(request.password))
            {
                result.AddError("password", "密码强度不足（至少8位，包含大小写字母和数字）");
            }

            // 验证确认密码
            if (request.password != request.confirm_password)
            {
                result.AddError("confirm_password", "确认密码与密码不匹配");
            }

            // 验证服务条款
            if (!request.accept_terms)
            {
                result.AddError("accept_terms", "必须同意服务条款");
            }
            
            return result;
        }
        
        #endregion
        
        #region 房间数据验证
        
        /// <summary>
        /// 验证创建房间请求
        /// </summary>
        public static ValidationResult ValidateCreateRoomRequest(CreateRoomRequest request)
        {
            var result = new ValidationResult();
            
            if (request == null)
            {
                result.AddError("request", "创建房间请求不能为空");
                return result;
            }
            
            // 验证房间名称
            if (string.IsNullOrEmpty(request.name))
            {
                result.AddError("name", "房间名称不能为空");
            }
            else if (request.name.Length < 3 || request.name.Length > 50)
            {
                result.AddError("name", "房间名称长度必须在3-50个字符之间");
            }

            // 验证最大玩家数
            if (request.max_players < 4 || request.max_players > 15)
            {
                result.AddError("max_players", "最大玩家数必须在4-15之间");
            }

            // 验证密码（如果是私人房间）
            if (request.is_private && string.IsNullOrEmpty(request.password))
            {
                result.AddError("password", "私人房间必须设置密码");
            }

            // 验证房间设置
            if (request.settings != null)
            {
                var settingsResult = ValidateRoomSettings(request.settings);
                result.Merge(settingsResult);
            }
            
            return result;
        }
        
        /// <summary>
        /// 验证房间设置
        /// </summary>
        public static ValidationResult ValidateRoomSettings(RoomSettings settings)
        {
            var result = new ValidationResult();
            
            if (settings == null)
            {
                result.AddError("settings", "房间设置不能为空");
                return result;
            }
            
            // 验证游戏模式
            var validGameModes = new[] { "classic", "hide_and_seek", "custom" };
            if (!validGameModes.Contains(settings.game_mode))
            {
                result.AddError("game_mode", "无效的游戏模式");
            }

            // 验证地图名称
            var validMaps = new[] { "skeld", "mira", "polus", "airship" };
            if (!validMaps.Contains(settings.map_name))
            {
                result.AddError("map_name", "无效的地图名称");
            }

            // 验证任务数量
            if (settings.task_count < 1 || settings.task_count > 20)
            {
                result.AddError("task_count", "任务数量必须在1-20之间");
            }

            // 验证鸭子比例
            if (settings.duck_ratio < 0.1f || settings.duck_ratio > 0.5f)
            {
                result.AddError("duck_ratio", "鸭子比例必须在0.1-0.5之间");
            }
            
            // 验证时间设置
            if (settings.meeting_time < 15 || settings.meeting_time > 300)
            {
                result.AddError("meeting_time", "会议时间必须在15-300秒之间");
            }

            if (settings.voting_time < 15 || settings.voting_time > 120)
            {
                result.AddError("voting_time", "投票时间必须在15-120秒之间");
            }

            if (settings.kill_cooldown < 10 || settings.kill_cooldown > 120)
            {
                result.AddError("kill_cooldown", "击杀冷却时间必须在10-120秒之间");
            }

            // 验证速度和视野设置
            if (settings.player_speed < 0.5f || settings.player_speed > 3.0f)
            {
                result.AddError("player_speed", "玩家速度必须在0.5-3.0之间");
            }

            if (settings.crew_vision < 0.25f || settings.crew_vision > 5.0f)
            {
                result.AddError("crew_vision", "船员视野必须在0.25-5.0之间");
            }

            if (settings.impostor_vision < 0.25f || settings.impostor_vision > 5.0f)
            {
                result.AddError("impostor_vision", "内鬼视野必须在0.25-5.0之间");
            }
            
            return result;
        }
        
        #endregion
        
        #region 游戏数据验证
        
        /// <summary>
        /// 验证玩家移动数据
        /// </summary>
        public static ValidationResult ValidatePlayerMoveData(PlayerMoveData moveData)
        {
            var result = new ValidationResult();
            
            if (moveData == null)
            {
                result.AddError("move_data", "移动数据不能为空");
                return result;
            }
            
            // 验证位置数据
            if (moveData.position == null)
            {
                result.AddError("position", "位置数据不能为空");
            }
            else
            {
                // 检查位置是否在合理范围内
                if (Math.Abs(moveData.position.x) > 1000 ||
                    Math.Abs(moveData.position.y) > 1000 ||
                    Math.Abs(moveData.position.z) > 1000)
                {
                    result.AddError("position", "位置数据超出合理范围");
                }
            }

            // 验证速度数据
            if (moveData.velocity == null)
            {
                result.AddError("velocity", "速度数据不能为空");
            }
            else
            {
                // 检查速度是否在合理范围内
                var speed = Math.Sqrt(moveData.velocity.x * moveData.velocity.x +
                                    moveData.velocity.y * moveData.velocity.y +
                                    moveData.velocity.z * moveData.velocity.z);
                if (speed > 50) // 最大速度限制
                {
                    result.AddError("velocity", "移动速度过快");
                }
            }

            // 验证旋转角度
            if (moveData.rotation < -360 || moveData.rotation > 360)
            {
                result.AddError("rotation", "旋转角度超出范围");
            }
            
            return result;
        }
        
        /// <summary>
        /// 验证玩家动作数据
        /// </summary>
        public static ValidationResult ValidatePlayerActionData(PlayerActionData actionData)
        {
            var result = new ValidationResult();
            
            if (actionData == null)
            {
                result.AddError("action_data", "动作数据不能为空");
                return result;
            }
            
            // 验证动作类型
            var validActions = new[] { "kill", "report", "use", "sabotage", "vent", "emergency" };
            if (string.IsNullOrEmpty(actionData.action) || !validActions.Contains(actionData.action))
            {
                result.AddError("action", "无效的动作类型");
            }

            // 验证目标ID（某些动作需要目标）
            var actionsRequiringTarget = new[] { "kill", "report" };
            if (actionsRequiringTarget.Contains(actionData.action) && string.IsNullOrEmpty(actionData.target_id))
            {
                result.AddError("target_id", $"动作 '{actionData.action}' 需要指定目标");
            }
            
            return result;
        }
        
        #endregion
        
        #region WebSocket消息验证
        
        /// <summary>
        /// 验证WebSocket消息
        /// </summary>
        public static ValidationResult ValidateWebSocketMessage(WebSocketMessage message)
        {
            var result = new ValidationResult();
            
            if (message == null)
            {
                result.AddError("message", "WebSocket消息不能为空");
                return result;
            }
            
            // 验证消息类型
            if (string.IsNullOrEmpty(message.type))
            {
                result.AddError("type", "消息类型不能为空");
            }

            // 验证时间戳
            if (message.timestamp <= 0)
            {
                result.AddError("timestamp", "时间戳无效");
            }

            // 验证时间戳是否过期（超过5分钟的消息认为无效）
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            if (Math.Abs(currentTime - message.timestamp) > 300)
            {
                result.AddError("timestamp", "消息时间戳过期");
            }
            
            return result;
        }
        
        #endregion
        
        #region 数据一致性检查
        
        /// <summary>
        /// 检查房间数据一致性
        /// </summary>
        public static ValidationResult CheckRoomConsistency(Room room)
        {
            var result = new ValidationResult();
            
            if (room == null)
            {
                result.AddError("room", "房间数据不能为空");
                return result;
            }
            
            // 检查当前玩家数是否超过最大玩家数
            if (room.current_size > room.max_players)
            {
                result.AddError("current_size", "当前玩家数超过最大玩家数");
            }

            // 检查私人房间是否有密码
            if (room.is_private && string.IsNullOrEmpty(room.password))
            {
                result.AddError("password", "私人房间必须设置密码");
            }
            
            return result;
        }
        
        /// <summary>
        /// 检查游戏数据一致性
        /// </summary>
        public static ValidationResult CheckGameConsistency(Game game)
        {
            var result = new ValidationResult();
            
            if (game == null)
            {
                result.AddError("game", "游戏数据不能为空");
                return result;
            }
            
            // 检查玩家数量
            if (game.players.Count < 4)
            {
                result.AddError("players", "游戏至少需要4名玩家");
            }

            // 检查鸭子数量
            var duckCount = game.players.Count(p => p.role == "duck");
            var expectedDuckCount = game.settings.duck_count;
            if (duckCount != expectedDuckCount)
            {
                result.AddError("duck_count", "鸭子数量与设置不符");
            }

            // 检查任务分配
            var totalTasks = game.tasks.Count;
            var expectedTasks = game.settings.task_count * game.players.Count(p => p.role == "goose");
            if (Math.Abs(totalTasks - expectedTasks) > game.players.Count)
            {
                result.AddError("task_count", "任务数量分配异常");
            }
            
            return result;
        }
        
        #endregion

        #region 验证工具方法

        /// <summary>
        /// 验证邮箱格式
        /// </summary>
        private static bool IsValidEmail(string email)
        {
            if (string.IsNullOrEmpty(email))
                return false;

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证用户名格式
        /// </summary>
        private static bool IsValidUsername(string username)
        {
            if (string.IsNullOrEmpty(username))
                return false;

            // 用户名长度3-20，只能包含字母、数字、下划线
            if (username.Length < 3 || username.Length > 20)
                return false;

            foreach (char c in username)
            {
                if (!char.IsLetterOrDigit(c) && c != '_')
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 验证密码强度
        /// </summary>
        private static bool IsValidPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return false;

            // 密码长度至少8位
            if (password.Length < 8)
                return false;

            bool hasUpper = false;
            bool hasLower = false;
            bool hasDigit = false;

            foreach (char c in password)
            {
                if (char.IsUpper(c)) hasUpper = true;
                if (char.IsLower(c)) hasLower = true;
                if (char.IsDigit(c)) hasDigit = true;
            }

            // 至少包含大写字母、小写字母、数字中的两种
            int complexity = (hasUpper ? 1 : 0) + (hasLower ? 1 : 0) + (hasDigit ? 1 : 0);
            return complexity >= 2;
        }

        #endregion
    }

    /// <summary>
    /// 验证结果类
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid => Errors.Count == 0;
        public List<ValidationError> Errors { get; } = new List<ValidationError>();
        
        public void AddError(string field, string message)
        {
            Errors.Add(new ValidationError { Field = field, Message = message });
        }
        
        public void Merge(ValidationResult other)
        {
            if (other != null)
            {
                Errors.AddRange(other.Errors);
            }
        }
        
        public string GetErrorMessage()
        {
            if (IsValid) return "";
            
            return string.Join("; ", Errors.Select(e => $"{e.Field}: {e.Message}"));
        }
        
        public void LogErrors()
        {
            if (!IsValid)
            {
                foreach (var error in Errors)
                {
                    Debug.LogError($"Validation Error - {error.Field}: {error.Message}");
                }
            }
        }
    }
    
    /// <summary>
    /// 验证错误类
    /// </summary>
    public class ValidationError
    {
        public string Field { get; set; }
        public string Message { get; set; }
    }
}
