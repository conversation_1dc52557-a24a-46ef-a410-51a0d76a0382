﻿﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace GooseDuckKill.Network.Models
{
    /// <summary>
    /// Unity JsonUtility兼容的WebSocket消息模型
    /// 与后台Go服务的WebSocket消息格式保持一致
    /// </summary>
    
    #region WebSocket消息类型常量
    
    public static class WebSocketMessageTypes
    {
        // 房间相关消息 - 与后台保持一致
        public const string JOIN_ROOM = "join_room";
        public const string LEAVE_ROOM = "leave_room";
        public const string PLAYER_READY = "player_ready";
        public const string START_GAME = "start_game";
        
        // 游戏相关消息
        public const string PLAYER_MOVE = "player_move";
        public const string PLAYER_ACTION = "player_action";
        public const string PLAYER_INPUT = "player_input";
        
        // 通信相关消息
        public const string CHAT_MESSAGE = "chat_message";
        public const string HEARTBEAT = "heartbeat";
        
        // 服务器响应消息 - 与后台Go服务保持一致
        public const string ROOM_UPDATED = "room_update";        // 修正：与后台一致
        public const string PLAYER_JOINED = "player_joined";
        public const string PLAYER_LEFT = "player_left";
        public const string GAME_STARTED = "game_started";
        public const string GAME_FINISHED = "game_finished";     // 新增：后台有此类型
        public const string GAME_STATE_UPDATE = "state_update";  // 修正：与后台一致
        public const string ERROR = "error";
        public const string HEARTBEAT_RESPONSE = "heartbeat_response"; // 新增：后台有此类型
        
        // Tick同步消息 - 与后台Go服务保持一致
        public const string TICK_SYNC = "tick_sync";             // 后台有此类型
        public const string TICK_INPUT = "tick_input";           // 后台有此类型
        public const string TICK_STATE = "tick_state";           // 前台扩展类型
        
        // 投票相关消息
        public const string VOTING_STARTED = "voting_started";
        public const string VOTE_CAST = "vote_cast";
        public const string VOTING_ENDED = "voting_ended";
        
        // 任务相关消息
        public const string TASK_STARTED = "task_started";
        public const string TASK_PROGRESS = "task_progress";
        public const string TASK_COMPLETED = "task_completed";
        
        // 事件消息
        public const string PLAYER_KILLED = "player_killed";
        public const string BODY_REPORTED = "body_reported";
        public const string EMERGENCY_MEETING = "emergency_meeting";
        public const string SABOTAGE = "sabotage";
    }
    
    #endregion
    
    #region 基础WebSocket消息
    
    [Serializable]
    public class WebSocketMessage
    {
        // 与后台Go服务完全一致的字段
        public string type;
        public string data; // JSON字符串，对应后台的interface{}
        public long timestamp;
        public string request_id;    // 后台有此字段

        // 前台扩展字段（后台可选支持）
        public string sender_id;
        public string room_id;
        public string game_id;
        public long sequence;
        
        public WebSocketMessage() { }
        
        public WebSocketMessage(string messageType, object messageData, string roomId = null, string gameId = null)
        {
            type = messageType;
            data = JsonHelper.ToJson(messageData);
            room_id = roomId;
            game_id = gameId;
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        }
        
        public T GetData<T>()
        {
            if (string.IsNullOrEmpty(data)) return default(T);
            return JsonHelper.FromJson<T>(data);
        }
        
        public void SetData<T>(T messageData)
        {
            data = JsonHelper.ToJson(messageData);
        }
    }
    
    #endregion
    
    #region 房间相关消息数据
    
    [Serializable]
    public class JoinRoomData
    {
        public string room_id;
        public string password;
        public PlayerInfo player_info;
        
        public JoinRoomData()
        {
            player_info = new PlayerInfo();
        }
    }
    
    [Serializable]
    public class PlayerInfo
    {
        public string username;
        public string color;
        public string hat;
        public string pet;
    }
    
    [Serializable]
    public class RoomUpdatedData
    {
        public Room room;
        public List<Player> players;
        
        public RoomUpdatedData()
        {
            room = new Room();
            players = new List<Player>();
        }
    }
    
    [Serializable]
    public class PlayerJoinedData
    {
        public Player player;
        public Room room;
        
        public PlayerJoinedData()
        {
            player = new Player();
            room = new Room();
        }
    }
    
    [Serializable]
    public class PlayerLeftData
    {
        public string player_id;
        public string username;
        public string reason;
        public Room room;
        
        public PlayerLeftData()
        {
            room = new Room();
        }
    }
    
    #endregion
    
    #region 游戏相关消息数据
    
    [Serializable]
    public class PlayerMoveData
    {
        public Vector3Data position;
        public Vector3Data velocity;
        public float rotation;
        public long timestamp;
        
        public PlayerMoveData()
        {
            position = new Vector3Data();
            velocity = new Vector3Data();
        }
    }
    
    [Serializable]
    public class PlayerActionData
    {
        public string action;
        public string target_id;
        public string location;
        public string parameters; // JSON字符串
    }
    
    [Serializable]
    public class GameStateUpdateData
    {
        public Game game;
        public List<GamePlayer> players;
        public List<GameEvent> events;
        
        public GameStateUpdateData()
        {
            game = new Game();
            players = new List<GamePlayer>();
            events = new List<GameEvent>();
        }
    }
    
    [Serializable]
    public class TickSyncData
    {
        public long tick;
        public long timestamp;
        public List<PlayerInputData> inputs;
        
        public TickSyncData()
        {
            inputs = new List<PlayerInputData>();
        }
    }
    
    [Serializable]
    public class PlayerInputData
    {
        public string player_id;
        public string input_type;
        public string data; // JSON字符串
        public long timestamp;
    }
    
    #endregion
    
    #region 投票相关消息数据
    
    [Serializable]
    public class VotingStartedData
    {
        public VotingSession voting_session;
        public List<string> eligible_voters;
        
        public VotingStartedData()
        {
            voting_session = new VotingSession();
            eligible_voters = new List<string>();
        }
    }
    
    [Serializable]
    public class VoteCastData
    {
        public string voter_id;
        public string target_id;
        public bool is_skip = false;
    }
    
    [Serializable]
    public class VotingEndedData
    {
        public VotingResult result;
        public string next_phase;
        
        public VotingEndedData()
        {
            result = new VotingResult();
        }
    }
    
    #endregion
    
    #region 任务相关消息数据
    
    [Serializable]
    public class TaskStartedData
    {
        public GameTask task;
        public string player_id;
        
        public TaskStartedData()
        {
            task = new GameTask();
        }
    }
    
    [Serializable]
    public class TaskProgressData
    {
        public string task_id;
        public string player_id;
        public float progress;
        public string data; // JSON字符串
    }
    
    [Serializable]
    public class TaskCompletedData
    {
        public GameTask task;
        public string player_id;
        public float completion_time;
        
        public TaskCompletedData()
        {
            task = new GameTask();
        }
    }
    
    #endregion
    
    #region 事件相关消息数据
    
    [Serializable]
    public class PlayerKilledData
    {
        public string victim_id;
        public string killer_id;
        public string location;
        public Vector3Data position;
        public string method = "kill";
        
        public PlayerKilledData()
        {
            position = new Vector3Data();
        }
    }
    
    [Serializable]
    public class BodyReportedData
    {
        public string reporter_id;
        public string victim_id;
        public string location;
        public Vector3Data position;
        
        public BodyReportedData()
        {
            position = new Vector3Data();
        }
    }
    
    [Serializable]
    public class EmergencyMeetingData
    {
        public string caller_id;
        public string reason;
        public string location;
    }
    
    [Serializable]
    public class SabotageData
    {
        public string saboteur_id;
        public string sabotage_type;
        public string target;
        public string location;
        public float duration = 0f;
    }
    
    #endregion
    
    #region 通信相关消息数据
    
    [Serializable]
    public class ChatMessageData
    {
        public string message;
        public string channel = "all";
        public string sender_name;
        public bool is_dead = false;
    }
    
    [Serializable]
    public class HeartbeatData
    {
        public long client_time;
        public float ping;
    }
    
    [Serializable]
    public class ErrorMessageData
    {
        public int code;
        public string message;
        public string details;
        public int retry_after = 0;
    }
    
    #endregion
    
    #region 请求相关模型
    
    [Serializable]
    public class StartGameRequest
    {
        public string room_id;
        public List<GamePlayer> players;
        public GameSettings settings;
        
        public StartGameRequest()
        {
            players = new List<GamePlayer>();
            settings = new GameSettings();
        }
    }
    
    [Serializable]
    public class PlayerMoveRequest
    {
        public Vector3Data position;
        public Vector3Data velocity;
        public float rotation;
        
        public PlayerMoveRequest()
        {
            position = new Vector3Data();
            velocity = new Vector3Data();
        }
    }
    
    [Serializable]
    public class PlayerActionRequest
    {
        public string action;
        public string target_id;
        public string location;
        public string parameters; // JSON字符串
    }
    
    [Serializable]
    public class VoteRequest
    {
        public string target_id;
        public bool is_skip = false;
    }
    
    [Serializable]
    public class TaskActionRequest
    {
        public string task_id;
        public string action;
        public float progress = 0f;
        public string data; // JSON字符串
    }
    
    #endregion
    
    #region 工具方法
    
    public static class WebSocketMessageHelper
    {
        /// <summary>
        /// 创建加入房间消息
        /// </summary>
        public static WebSocketMessage CreateJoinRoomMessage(string roomId, string password, PlayerInfo playerInfo)
        {
            var data = new JoinRoomData
            {
                room_id = roomId,
                password = password,
                player_info = playerInfo
            };
            
            return new WebSocketMessage(WebSocketMessageTypes.JOIN_ROOM, data, roomId);
        }
        
        /// <summary>
        /// 创建玩家移动消息
        /// </summary>
        public static WebSocketMessage CreatePlayerMoveMessage(Vector3 position, Vector3 velocity, float rotation, string roomId, string gameId)
        {
            var data = new PlayerMoveData
            {
                position = Vector3Data.FromVector3(position),
                velocity = Vector3Data.FromVector3(velocity),
                rotation = rotation,
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };
            
            return new WebSocketMessage(WebSocketMessageTypes.PLAYER_MOVE, data, roomId, gameId);
        }
        
        /// <summary>
        /// 创建聊天消息
        /// </summary>
        public static WebSocketMessage CreateChatMessage(string message, string channel, string senderName, bool isDead, string roomId)
        {
            var data = new ChatMessageData
            {
                message = message,
                channel = channel,
                sender_name = senderName,
                is_dead = isDead
            };
            
            return new WebSocketMessage(WebSocketMessageTypes.CHAT_MESSAGE, data, roomId);
        }
        
        /// <summary>
        /// 创建心跳消息
        /// </summary>
        public static WebSocketMessage CreateHeartbeatMessage(float ping)
        {
            var data = new HeartbeatData
            {
                client_time = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                ping = ping
            };
            
            return new WebSocketMessage(WebSocketMessageTypes.HEARTBEAT, data);
        }
        
        /// <summary>
        /// 创建投票消息
        /// </summary>
        public static WebSocketMessage CreateVoteMessage(string targetId, bool isSkip, string roomId, string gameId)
        {
            var data = new VoteCastData
            {
                target_id = targetId,
                is_skip = isSkip
            };
            
            return new WebSocketMessage(WebSocketMessageTypes.VOTE_CAST, data, roomId, gameId);
        }
        
        /// <summary>
        /// 验证WebSocket消息格式
        /// </summary>
        public static bool ValidateMessage(WebSocketMessage message)
        {
            if (message == null) return false;
            if (string.IsNullOrEmpty(message.type)) return false;
            if (message.timestamp <= 0) return false;
            
            // 检查时间戳是否过期（5分钟）
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            if (Math.Abs(currentTime - message.timestamp) > 300) return false;
            
            return true;
        }
    }
    
    #endregion
}
