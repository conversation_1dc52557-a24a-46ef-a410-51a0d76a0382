﻿﻿﻿﻿﻿﻿﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Networking;
using GooseDuckKill.Network.Models;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 认证管理器 - 使用Unity兼容的数据模型
    /// 管理用户登录、注册、Token刷新等认证功能
    /// </summary>
    public class AuthManager : MonoBehaviour
    {
        [Header("认证配置")]
        [SerializeField] private bool autoRefreshToken = true;
        [SerializeField] private float refreshThreshold = 300f; // 5分钟前刷新
        [SerializeField] private bool enableDebugLogs = true;
        
        // 单例实例
        private static AuthManager _instance;
        public static AuthManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("AuthManager");
                    _instance = go.AddComponent<AuthManager>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }
        
        // Token信息
        private string _accessToken;
        private string _refreshToken;
        private DateTime _tokenExpireTime;
        private User _currentUser;
        
        // 网络组件
        private NetworkServiceManager _serviceManager;
        
        // 事件
        public event Action<bool> OnAuthStateChanged;
        public event Action<string> OnAuthError;
        public event Action<User> OnUserUpdated;
        
        // 属性
        public bool IsAuthenticated => !string.IsNullOrEmpty(_accessToken) && DateTime.Now < _tokenExpireTime;
        public string AccessToken => _accessToken;
        public User CurrentUser => _currentUser;
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                Initialize();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            if (autoRefreshToken)
            {
                StartCoroutine(AutoRefreshTokenCoroutine());
            }
        }
        
        /// <summary>
        /// 初始化认证管理器
        /// </summary>
        private void Initialize()
        {
            _serviceManager = NetworkServiceManager.Instance;
            LoadTokenFromPlayerPrefs();
            
            Log("AuthManager initialized.");
        }
        
        /// <summary>
        /// 用户登录
        /// </summary>
        public void LoginAsync(string email, string password, Action<bool> callback = null)
        {
            StartCoroutine(LoginCoroutine(email, password, callback));
        }
        
        /// <summary>
        /// 用户注册
        /// </summary>
        public void RegisterAsync(string username, string email, string password, string confirmPassword, Action<bool> callback = null)
        {
            StartCoroutine(RegisterCoroutine(username, email, password, confirmPassword, callback));
        }
        
        /// <summary>
        /// 刷新Token
        /// </summary>
        public void RefreshTokenAsync(Action<bool> callback = null)
        {
            StartCoroutine(RefreshTokenCoroutine(callback));
        }
        
        /// <summary>
        /// 登出
        /// </summary>
        public void LogoutAsync(Action callback = null)
        {
            StartCoroutine(LogoutCoroutine(callback));
        }
        
        /// <summary>
        /// 获取认证头
        /// </summary>
        public string GetAuthHeader()
        {
            return string.IsNullOrEmpty(_accessToken) ? "" : $"Bearer {_accessToken}";
        }

        /// <summary>
        /// 检查Token是否即将过期
        /// </summary>
        public bool IsTokenExpiringSoon(float thresholdSeconds = 300f)
        {
            if (!IsAuthenticated) return false;

            var timeToExpire = (_tokenExpireTime - DateTime.Now).TotalSeconds;
            return timeToExpire <= thresholdSeconds;
        }

        /// <summary>
        /// 获取Token剩余时间（秒）
        /// </summary>
        public double GetTokenRemainingTime()
        {
            if (!IsAuthenticated) return 0;

            return (_tokenExpireTime - DateTime.Now).TotalSeconds;
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        public void ChangePasswordAsync(string currentPassword, string newPassword, string confirmPassword, Action<bool> callback = null)
        {
            StartCoroutine(ChangePasswordCoroutine(currentPassword, newPassword, confirmPassword, callback));
        }

        /// <summary>
        /// 忘记密码
        /// </summary>
        public void ForgotPasswordAsync(string email, Action<bool> callback = null)
        {
            StartCoroutine(ForgotPasswordCoroutine(email, callback));
        }

        /// <summary>
        /// 重置密码
        /// </summary>
        public void ResetPasswordAsync(string token, string newPassword, string confirmPassword, Action<bool> callback = null)
        {
            StartCoroutine(ResetPasswordCoroutine(token, newPassword, confirmPassword, callback));
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        public void UpdateUserProfileAsync(UserProfile profile, Action<bool> callback = null)
        {
            StartCoroutine(UpdateUserProfileCoroutine(profile, callback));
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        public void GetUserProfileAsync(Action<User> callback = null)
        {
            StartCoroutine(GetUserProfileCoroutine(callback));
        }

        /// <summary>
        /// 验证Token有效性
        /// </summary>
        public void ValidateTokenAsync(Action<bool> callback = null)
        {
            StartCoroutine(ValidateTokenCoroutine(callback));
        }
        
        /// <summary>
        /// 登录协程
        /// </summary>
        private IEnumerator LoginCoroutine(string email, string password, Action<bool> callback)
        {
            Log($"Attempting login for user: {email}");

            // 验证输入
            var loginRequest = new LoginRequest
            {
                email = email,          // 修正：使用email字段
                password = password,
                remember_me = true
            };
            
            var validation = ModelValidator.ValidateLoginRequest(loginRequest);
            if (!validation.IsValid)
            {
                var errorMessage = validation.GetErrorMessage();
                LogError($"Login validation failed: {errorMessage}");
                OnAuthError?.Invoke(errorMessage);
                callback?.Invoke(false);
                yield break;
            }
            
            // 发送登录请求
            bool requestCompleted = false;
            bool loginSuccess = false;
            
            _serviceManager.SendPostRequest<LoginResponse>(
                ServiceType.Auth, 
                "/auth/login", 
                loginRequest, 
                false, 
                response =>
                {
                    requestCompleted = true;
                    if (response.IsSuccess)
                    {
                        var expiresIn = response.data.GetExpiresIn(); // 使用兼容性方法
                        SetTokens(response.data.access_token, response.data.refresh_token, expiresIn);
                        _currentUser = response.data.user;
                        OnAuthStateChanged?.Invoke(true);
                        OnUserUpdated?.Invoke(_currentUser);
                        loginSuccess = true;
                        Log($"Login successful for user: {email}");
                    }
                    else
                    {
                        LogError($"Login failed: {response.message}");
                        OnAuthError?.Invoke(response.message);
                    }
                }
            );
            
            // 等待请求完成
            yield return new WaitUntil(() => requestCompleted);
            callback?.Invoke(loginSuccess);
        }
        
        /// <summary>
        /// 注册协程
        /// </summary>
        private IEnumerator RegisterCoroutine(string username, string email, string password, string confirmPassword, Action<bool> callback)
        {
            Log($"Attempting registration for user: {username}");
            
            // 验证输入
            var registerRequest = new RegisterRequest
            {
                username = username,
                email = email,
                password = password,
                confirm_password = confirmPassword,
                display_name = username,
                accept_terms = true
            };
            
            var validation = ModelValidator.ValidateRegisterRequest(registerRequest);
            if (!validation.IsValid)
            {
                var errorMessage = validation.GetErrorMessage();
                LogError($"Registration validation failed: {errorMessage}");
                OnAuthError?.Invoke(errorMessage);
                callback?.Invoke(false);
                yield break;
            }
            
            // 发送注册请求
            bool requestCompleted = false;
            bool registerSuccess = false;
            
            _serviceManager.SendPostRequest<RegisterResponse>(
                ServiceType.Auth, 
                "/auth/register", 
                registerRequest, 
                false, 
                response =>
                {
                    requestCompleted = true;
                    if (response.IsSuccess)
                    {
                        Log($"Registration successful for user: {username}");
                        registerSuccess = true;
                        
                        // 注册成功后自动登录
                        StartCoroutine(LoginCoroutine(username, password, null));
                    }
                    else
                    {
                        LogError($"Registration failed: {response.message}");
                        OnAuthError?.Invoke(response.message);
                    }
                }
            );
            
            // 等待请求完成
            yield return new WaitUntil(() => requestCompleted);
            callback?.Invoke(registerSuccess);
        }
        
        /// <summary>
        /// 刷新Token协程
        /// </summary>
        private IEnumerator RefreshTokenCoroutine(Action<bool> callback)
        {
            if (string.IsNullOrEmpty(_refreshToken))
            {
                LogError("No refresh token available");
                callback?.Invoke(false);
                yield break;
            }
            
            Log("Refreshing access token...");
            
            var refreshRequest = new RefreshTokenRequest
            {
                refresh_token = _refreshToken
            };
            
            bool requestCompleted = false;
            bool refreshSuccess = false;
            
            _serviceManager.SendPostRequest<LoginResponse>(
                ServiceType.Auth, 
                "/auth/refresh", 
                refreshRequest, 
                false, 
                response =>
                {
                    requestCompleted = true;
                    if (response.IsSuccess)
                    {
                        var expiresIn = response.data.GetExpiresIn(); // 使用兼容性方法
                        SetTokens(response.data.access_token, response.data.refresh_token, expiresIn);
                        refreshSuccess = true;
                        Log("Token refresh successful");
                    }
                    else
                    {
                        LogError($"Token refresh failed: {response.message}");
                        ClearTokens();
                        OnAuthStateChanged?.Invoke(false);
                    }
                }
            );
            
            // 等待请求完成
            yield return new WaitUntil(() => requestCompleted);
            callback?.Invoke(refreshSuccess);
        }
        
        /// <summary>
        /// 登出协程
        /// </summary>
        private IEnumerator LogoutCoroutine(Action callback)
        {
            Log("Logging out...");
            
            if (!string.IsNullOrEmpty(_accessToken))
            {
                bool requestCompleted = false;
                
                _serviceManager.SendPostRequest<object>(
                    ServiceType.Auth, 
                    "/auth/logout", 
                    null, 
                    true, 
                    response =>
                    {
                        requestCompleted = true;
                        if (response.IsSuccess)
                        {
                            Log("Logout successful");
                        }
                        else
                        {
                            LogError($"Logout request failed: {response.message}");
                        }
                    }
                );
                
                // 等待请求完成
                yield return new WaitUntil(() => requestCompleted);
            }
            
            ClearTokens();
            _currentUser = null;
            OnAuthStateChanged?.Invoke(false);
            OnUserUpdated?.Invoke(null);
            callback?.Invoke();
        }
        
        /// <summary>
        /// 修改密码协程
        /// </summary>
        private IEnumerator ChangePasswordCoroutine(string currentPassword, string newPassword, string confirmPassword, Action<bool> callback)
        {
            if (!IsAuthenticated)
            {
                LogError("User not authenticated");
                OnAuthError?.Invoke("用户未登录");
                callback?.Invoke(false);
                yield break;
            }

            Log("Attempting to change password...");

            var changePasswordRequest = new ChangePasswordRequest
            {
                old_password = currentPassword,     // 修正：使用old_password字段
                new_password = newPassword,
                confirm_password = confirmPassword  // 前台验证用
            };

            bool requestCompleted = false;
            bool changeSuccess = false;

            _serviceManager.SendPostRequest<object>(
                ServiceType.Auth,
                "/auth/change-password",
                changePasswordRequest,
                true,
                response =>
                {
                    requestCompleted = true;
                    if (response.IsSuccess)
                    {
                        Log("Password changed successfully");
                        changeSuccess = true;
                    }
                    else
                    {
                        LogError($"Password change failed: {response.message}");
                        OnAuthError?.Invoke(response.message);
                    }
                }
            );

            yield return new WaitUntil(() => requestCompleted);
            callback?.Invoke(changeSuccess);
        }

        /// <summary>
        /// 忘记密码协程
        /// </summary>
        private IEnumerator ForgotPasswordCoroutine(string email, Action<bool> callback)
        {
            Log($"Sending password reset email to: {email}");

            var forgotPasswordRequest = new ForgotPasswordRequest
            {
                email = email
            };

            bool requestCompleted = false;
            bool requestSuccess = false;

            _serviceManager.SendPostRequest<object>(
                ServiceType.Auth,
                "/auth/forgot-password",
                forgotPasswordRequest,
                false,
                response =>
                {
                    requestCompleted = true;
                    if (response.IsSuccess)
                    {
                        Log("Password reset email sent successfully");
                        requestSuccess = true;
                    }
                    else
                    {
                        LogError($"Failed to send password reset email: {response.message}");
                        OnAuthError?.Invoke(response.message);
                    }
                }
            );

            yield return new WaitUntil(() => requestCompleted);
            callback?.Invoke(requestSuccess);
        }

        /// <summary>
        /// 重置密码协程
        /// </summary>
        private IEnumerator ResetPasswordCoroutine(string token, string newPassword, string confirmPassword, Action<bool> callback)
        {
            Log("Attempting to reset password...");

            var resetPasswordRequest = new ResetPasswordRequest
            {
                token = token,
                new_password = newPassword,
                confirm_password = confirmPassword
            };

            bool requestCompleted = false;
            bool resetSuccess = false;

            _serviceManager.SendPostRequest<object>(
                ServiceType.Auth,
                "/auth/reset-password",
                resetPasswordRequest,
                false,
                response =>
                {
                    requestCompleted = true;
                    if (response.IsSuccess)
                    {
                        Log("Password reset successfully");
                        resetSuccess = true;
                    }
                    else
                    {
                        LogError($"Password reset failed: {response.message}");
                        OnAuthError?.Invoke(response.message);
                    }
                }
            );

            yield return new WaitUntil(() => requestCompleted);
            callback?.Invoke(resetSuccess);
        }

        /// <summary>
        /// 更新用户信息协程
        /// </summary>
        private IEnumerator UpdateUserProfileCoroutine(UserProfile profile, Action<bool> callback)
        {
            if (!IsAuthenticated)
            {
                LogError("User not authenticated");
                OnAuthError?.Invoke("用户未登录");
                callback?.Invoke(false);
                yield break;
            }

            Log("Updating user profile...");

            bool requestCompleted = false;
            bool updateSuccess = false;

            _serviceManager.SendPutRequest<User>(
                ServiceType.Auth,
                "/auth/profile",
                profile,
                true,
                response =>
                {
                    requestCompleted = true;
                    if (response.IsSuccess)
                    {
                        _currentUser = response.data;
                        OnUserUpdated?.Invoke(_currentUser);
                        updateSuccess = true;
                        Log("User profile updated successfully");
                    }
                    else
                    {
                        LogError($"Failed to update user profile: {response.message}");
                        OnAuthError?.Invoke(response.message);
                    }
                }
            );

            yield return new WaitUntil(() => requestCompleted);
            callback?.Invoke(updateSuccess);
        }

        /// <summary>
        /// 获取用户信息协程
        /// </summary>
        private IEnumerator GetUserProfileCoroutine(Action<User> callback)
        {
            if (!IsAuthenticated)
            {
                LogError("User not authenticated");
                OnAuthError?.Invoke("用户未登录");
                callback?.Invoke(null);
                yield break;
            }

            Log("Getting user profile...");

            bool requestCompleted = false;
            User userProfile = null;

            _serviceManager.SendGetRequest<User>(
                ServiceType.Auth,
                "/auth/profile",
                true,
                response =>
                {
                    requestCompleted = true;
                    if (response.IsSuccess)
                    {
                        _currentUser = response.data;
                        userProfile = response.data;
                        OnUserUpdated?.Invoke(_currentUser);
                        Log("User profile retrieved successfully");
                    }
                    else
                    {
                        LogError($"Failed to get user profile: {response.message}");
                        OnAuthError?.Invoke(response.message);
                    }
                }
            );

            yield return new WaitUntil(() => requestCompleted);
            callback?.Invoke(userProfile);
        }

        /// <summary>
        /// 验证Token协程
        /// </summary>
        private IEnumerator ValidateTokenCoroutine(Action<bool> callback)
        {
            if (!IsAuthenticated)
            {
                callback?.Invoke(false);
                yield break;
            }

            Log("Validating token...");

            bool requestCompleted = false;
            bool isValid = false;

            _serviceManager.SendGetRequest<object>(
                ServiceType.Auth,
                "/auth/validate",
                true,
                response =>
                {
                    requestCompleted = true;
                    if (response.IsSuccess)
                    {
                        isValid = true;
                        Log("Token is valid");
                    }
                    else
                    {
                        LogError($"Token validation failed: {response.message}");
                        ClearTokens();
                        OnAuthStateChanged?.Invoke(false);
                    }
                }
            );

            yield return new WaitUntil(() => requestCompleted);
            callback?.Invoke(isValid);
        }

        /// <summary>
        /// 设置Token
        /// </summary>
        private void SetTokens(string accessToken, string refreshToken, int expiresIn)
        {
            _accessToken = accessToken;
            _refreshToken = refreshToken;
            _tokenExpireTime = DateTime.Now.AddSeconds(expiresIn);
            
            SaveTokenToPlayerPrefs();
            Log("Tokens updated successfully");
        }
        
        /// <summary>
        /// 清除Token
        /// </summary>
        private void ClearTokens()
        {
            _accessToken = "";
            _refreshToken = "";
            _tokenExpireTime = DateTime.MinValue;
            
            PlayerPrefs.DeleteKey("AccessToken");
            PlayerPrefs.DeleteKey("RefreshToken");
            PlayerPrefs.DeleteKey("TokenExpireTime");
            PlayerPrefs.Save();
            
            Log("Tokens cleared");
        }
        
        /// <summary>
        /// 保存Token到PlayerPrefs
        /// </summary>
        private void SaveTokenToPlayerPrefs()
        {
            PlayerPrefs.SetString("AccessToken", _accessToken);
            PlayerPrefs.SetString("RefreshToken", _refreshToken);
            PlayerPrefs.SetString("TokenExpireTime", _tokenExpireTime.ToBinary().ToString());
            PlayerPrefs.Save();
        }
        
        /// <summary>
        /// 从PlayerPrefs加载Token
        /// </summary>
        private void LoadTokenFromPlayerPrefs()
        {
            _accessToken = PlayerPrefs.GetString("AccessToken", "");
            _refreshToken = PlayerPrefs.GetString("RefreshToken", "");
            
            var expireTimeString = PlayerPrefs.GetString("TokenExpireTime", "");
            if (!string.IsNullOrEmpty(expireTimeString) && long.TryParse(expireTimeString, out var expireTimeBinary))
            {
                _tokenExpireTime = DateTime.FromBinary(expireTimeBinary);
            }
            
            if (IsAuthenticated)
            {
                Log("Loaded valid tokens from PlayerPrefs");
            }
        }
        
        /// <summary>
        /// 自动刷新Token协程
        /// </summary>
        private IEnumerator AutoRefreshTokenCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(60f); // 每分钟检查一次
                
                if (IsAuthenticated && !string.IsNullOrEmpty(_refreshToken))
                {
                    var timeToExpire = (_tokenExpireTime - DateTime.Now).TotalSeconds;
                    if (timeToExpire <= refreshThreshold)
                    {
                        Log("Auto-refreshing token...");
                        yield return StartCoroutine(RefreshTokenCoroutine(null));
                    }
                }
            }
        }
        
        /// <summary>
        /// 记录日志
        /// </summary>
        private void Log(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[AuthManager] {message}");
            }
        }
        
        /// <summary>
        /// 记录错误日志
        /// </summary>
        private void LogError(string message)
        {
            Debug.LogError($"[AuthManager] {message}");
        }
        
        private void OnDestroy()
        {
            // 清理资源
            StopAllCoroutines();
        }
    }
}
