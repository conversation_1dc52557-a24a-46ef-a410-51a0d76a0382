using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using GooseDuckKill.Network.Models;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// WebSocket消息队列系统
    /// 提供消息优先级处理、批处理和流量控制
    /// </summary>
    public class WebSocketMessageQueue : MonoBehaviour
    {
        [Header("队列配置")]
        [SerializeField] private int maxQueueSize = 1000;
        [SerializeField] private int maxProcessPerFrame = 10;
        [SerializeField] private float batchProcessInterval = 0.016f; // 60fps
        [SerializeField] private bool enablePriorityProcessing = true;
        [SerializeField] private bool enableDebugLogs = false;
        
        // 消息队列
        private Queue<QueuedMessage> _incomingQueue = new Queue<QueuedMessage>();
        private Queue<QueuedMessage> _outgoingQueue = new Queue<QueuedMessage>();
        private List<QueuedMessage> _priorityQueue = new List<QueuedMessage>();
        
        // 处理统计
        private int _processedCount = 0;
        private int _droppedCount = 0;
        private float _lastProcessTime = 0f;
        
        // 事件
        public event Action<WebSocketMessage> OnMessageProcessed;
        public event Action<WebSocketMessage> OnMessageDropped;
        public event Action<QueueStats> OnStatsUpdated;
        
        private void Update()
        {
            ProcessQueues();
            UpdateStats();
        }
        
        /// <summary>
        /// 添加传入消息到队列
        /// </summary>
        public void EnqueueIncoming(WebSocketMessage message, MessagePriority priority = MessagePriority.Normal)
        {
            if (_incomingQueue.Count >= maxQueueSize)
            {
                // 队列满了，丢弃最旧的消息
                var dropped = _incomingQueue.Dequeue();
                _droppedCount++;
                OnMessageDropped?.Invoke(dropped.Message);
                
                if (enableDebugLogs)
                    Debug.LogWarning($"[WebSocketMessageQueue] Dropped message due to queue overflow: {dropped.Message.type}");
            }
            
            var queuedMessage = new QueuedMessage
            {
                Message = message,
                Priority = priority,
                EnqueueTime = Time.time,
                Direction = MessageDirection.Incoming
            };
            
            if (enablePriorityProcessing && priority == MessagePriority.High)
            {
                _priorityQueue.Add(queuedMessage);
                _priorityQueue.Sort((a, b) => b.Priority.CompareTo(a.Priority));
            }
            else
            {
                _incomingQueue.Enqueue(queuedMessage);
            }
            
            if (enableDebugLogs)
                Debug.Log($"[WebSocketMessageQueue] Enqueued incoming message: {message.type} (Priority: {priority})");
        }
        
        /// <summary>
        /// 添加传出消息到队列
        /// </summary>
        public void EnqueueOutgoing(WebSocketMessage message, MessagePriority priority = MessagePriority.Normal)
        {
            if (_outgoingQueue.Count >= maxQueueSize)
            {
                // 队列满了，丢弃最旧的消息
                var dropped = _outgoingQueue.Dequeue();
                _droppedCount++;
                OnMessageDropped?.Invoke(dropped.Message);
                
                if (enableDebugLogs)
                    Debug.LogWarning($"[WebSocketMessageQueue] Dropped outgoing message due to queue overflow: {dropped.Message.type}");
            }
            
            var queuedMessage = new QueuedMessage
            {
                Message = message,
                Priority = priority,
                EnqueueTime = Time.time,
                Direction = MessageDirection.Outgoing
            };
            
            if (enablePriorityProcessing && priority == MessagePriority.High)
            {
                _priorityQueue.Add(queuedMessage);
                _priorityQueue.Sort((a, b) => b.Priority.CompareTo(a.Priority));
            }
            else
            {
                _outgoingQueue.Enqueue(queuedMessage);
            }
            
            if (enableDebugLogs)
                Debug.Log($"[WebSocketMessageQueue] Enqueued outgoing message: {message.type} (Priority: {priority})");
        }
        
        /// <summary>
        /// 获取下一个传入消息
        /// </summary>
        public WebSocketMessage DequeueIncoming()
        {
            // 优先处理高优先级消息
            if (_priorityQueue.Count > 0)
            {
                var priorityMessage = _priorityQueue.FirstOrDefault(m => m.Direction == MessageDirection.Incoming);
                if (priorityMessage != null)
                {
                    _priorityQueue.Remove(priorityMessage);
                    return priorityMessage.Message;
                }
            }
            
            // 处理普通队列
            if (_incomingQueue.Count > 0)
            {
                var queuedMessage = _incomingQueue.Dequeue();
                return queuedMessage.Message;
            }
            
            return null;
        }
        
        /// <summary>
        /// 获取下一个传出消息
        /// </summary>
        public WebSocketMessage DequeueOutgoing()
        {
            // 优先处理高优先级消息
            if (_priorityQueue.Count > 0)
            {
                var priorityMessage = _priorityQueue.FirstOrDefault(m => m.Direction == MessageDirection.Outgoing);
                if (priorityMessage != null)
                {
                    _priorityQueue.Remove(priorityMessage);
                    return priorityMessage.Message;
                }
            }
            
            // 处理普通队列
            if (_outgoingQueue.Count > 0)
            {
                var queuedMessage = _outgoingQueue.Dequeue();
                return queuedMessage.Message;
            }
            
            return null;
        }
        
        /// <summary>
        /// 批量获取传入消息
        /// </summary>
        public List<WebSocketMessage> DequeueBatchIncoming(int maxCount)
        {
            var messages = new List<WebSocketMessage>();
            
            for (int i = 0; i < maxCount; i++)
            {
                var message = DequeueIncoming();
                if (message == null) break;
                
                messages.Add(message);
            }
            
            return messages;
        }
        
        /// <summary>
        /// 批量获取传出消息
        /// </summary>
        public List<WebSocketMessage> DequeueBatchOutgoing(int maxCount)
        {
            var messages = new List<WebSocketMessage>();
            
            for (int i = 0; i < maxCount; i++)
            {
                var message = DequeueOutgoing();
                if (message == null) break;
                
                messages.Add(message);
            }
            
            return messages;
        }
        
        /// <summary>
        /// 处理队列
        /// </summary>
        private void ProcessQueues()
        {
            if (Time.time - _lastProcessTime < batchProcessInterval)
                return;
                
            _lastProcessTime = Time.time;
            
            // 处理传入消息
            var incomingMessages = DequeueBatchIncoming(maxProcessPerFrame / 2);
            foreach (var message in incomingMessages)
            {
                OnMessageProcessed?.Invoke(message);
                _processedCount++;
            }
            
            // 处理传出消息的逻辑由WebSocketManager处理
            // 这里只是为了统计
            var outgoingCount = Math.Min(_outgoingQueue.Count + _priorityQueue.Count(m => m.Direction == MessageDirection.Outgoing), maxProcessPerFrame / 2);
            _processedCount += outgoingCount;
        }
        
        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStats()
        {
            var stats = GetQueueStats();
            OnStatsUpdated?.Invoke(stats);
        }
        
        /// <summary>
        /// 获取队列统计信息
        /// </summary>
        public QueueStats GetQueueStats()
        {
            return new QueueStats
            {
                IncomingQueueSize = _incomingQueue.Count,
                OutgoingQueueSize = _outgoingQueue.Count,
                PriorityQueueSize = _priorityQueue.Count,
                TotalProcessed = _processedCount,
                TotalDropped = _droppedCount,
                ProcessingRate = _processedCount / (Time.time + 0.001f), // 避免除零
                QueueUtilization = (float)(_incomingQueue.Count + _outgoingQueue.Count + _priorityQueue.Count) / (maxQueueSize * 2)
            };
        }
        
        /// <summary>
        /// 清空所有队列
        /// </summary>
        public void ClearAllQueues()
        {
            _incomingQueue.Clear();
            _outgoingQueue.Clear();
            _priorityQueue.Clear();
            
            Debug.Log("[WebSocketMessageQueue] All queues cleared.");
        }
        
        /// <summary>
        /// 获取消息优先级
        /// </summary>
        public static MessagePriority GetMessagePriority(string messageType)
        {
            switch (messageType)
            {
                case WebSocketMessageTypes.HEARTBEAT:
                case WebSocketMessageTypes.ERROR:
                    return MessagePriority.High;
                    
                case WebSocketMessageTypes.PLAYER_MOVE:
                case WebSocketMessageTypes.TICK_SYNC:
                case WebSocketMessageTypes.TICK_INPUT:
                    return MessagePriority.Low;
                    
                default:
                    return MessagePriority.Normal;
            }
        }
        
        /// <summary>
        /// 打印队列状态
        /// </summary>
        [ContextMenu("Print Queue Stats")]
        public void PrintQueueStats()
        {
            var stats = GetQueueStats();
            Debug.Log($"[WebSocketMessageQueue] Stats:\n" +
                     $"Incoming Queue: {stats.IncomingQueueSize}\n" +
                     $"Outgoing Queue: {stats.OutgoingQueueSize}\n" +
                     $"Priority Queue: {stats.PriorityQueueSize}\n" +
                     $"Processed: {stats.TotalProcessed}\n" +
                     $"Dropped: {stats.TotalDropped}\n" +
                     $"Processing Rate: {stats.ProcessingRate:F2}/s\n" +
                     $"Queue Utilization: {stats.QueueUtilization:P1}");
        }
    }
    
    /// <summary>
    /// 队列中的消息
    /// </summary>
    [Serializable]
    public class QueuedMessage
    {
        public WebSocketMessage Message;
        public MessagePriority Priority;
        public float EnqueueTime;
        public MessageDirection Direction;
        
        public float WaitTime => Time.time - EnqueueTime;
    }
    
    /// <summary>
    /// 消息优先级
    /// </summary>
    public enum MessagePriority
    {
        Low = 0,
        Normal = 1,
        High = 2
    }
    
    /// <summary>
    /// 消息方向
    /// </summary>
    public enum MessageDirection
    {
        Incoming,
        Outgoing
    }
    
    /// <summary>
    /// 队列统计信息
    /// </summary>
    [Serializable]
    public class QueueStats
    {
        public int IncomingQueueSize;
        public int OutgoingQueueSize;
        public int PriorityQueueSize;
        public int TotalProcessed;
        public int TotalDropped;
        public float ProcessingRate;
        public float QueueUtilization;
        
        public override string ToString()
        {
            return $"Queue Stats - In:{IncomingQueueSize}, Out:{OutgoingQueueSize}, Priority:{PriorityQueueSize}, " +
                   $"Processed:{TotalProcessed}, Dropped:{TotalDropped}, Rate:{ProcessingRate:F1}/s, Util:{QueueUtilization:P1}";
        }
    }
}
