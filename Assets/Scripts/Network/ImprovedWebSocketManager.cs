﻿﻿﻿﻿﻿﻿﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GooseDuckKill.Network.Models;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 改进的WebSocket管理器 - 使用Unity兼容的数据模型
    /// 提供与后台服务的实时通信功能
    /// </summary>
    public class ImprovedWebSocketManager : MonoBehaviour
    {
        [Header("WebSocket配置")]
        [SerializeField] private bool autoConnect = false;
        [SerializeField] private bool enableHeartbeat = true;
        [SerializeField] private float heartbeatInterval = 30f;
        [SerializeField] private int maxReconnectAttempts = 5;
        [SerializeField] private float reconnectDelay = 2f;
        [SerializeField] private bool enableDebugLogs = true;
        
        // 连接状态
        public bool IsConnected { get; private set; }
        public bool IsConnecting { get; private set; }
        public string CurrentRoomId { get; private set; }
        
        // WebSocket连接（这里使用模拟实现，实际项目中需要真实的WebSocket库）
        private WebSocketConnection _connection;
        private Queue<WebSocketMessage> _outgoingMessages = new Queue<WebSocketMessage>();
        private Queue<WebSocketMessage> _incomingMessages = new Queue<WebSocketMessage>();
        
        // 重连机制
        private int _reconnectAttempts = 0;
        private float _lastHeartbeat = 0f;
        private Coroutine _heartbeatCoroutine;
        private Coroutine _reconnectCoroutine;
        
        // 网络组件
        private NetworkConfig _config;
        private AuthManager _authManager;
        private WebSocketEventSystem _eventSystem;
        
        // 事件
        public event Action OnConnected;
        public event Action OnDisconnected;
        #pragma warning disable CS0067 // Event is never used
        public event Action<string> OnRoomJoined;
        public event Action<string> OnRoomLeft;
        #pragma warning restore CS0067
        public event Action<WebSocketMessage> OnMessageReceived;
        public event Action<string> OnError;
        
        private void Start()
        {
            Initialize();
            
            if (autoConnect)
            {
                ConnectToServer();
            }
        }
        
        private void Update()
        {
            ProcessIncomingMessages();
            ProcessOutgoingMessages();
        }
        
        private void OnDestroy()
        {
            Disconnect();
        }
        
        /// <summary>
        /// 初始化WebSocket管理器
        /// </summary>
        private void Initialize()
        {
            _config = NetworkConfig.Instance;
            _authManager = AuthManager.Instance;
            _eventSystem = WebSocketEventSystem.Instance;

            if (_config == null)
            {
                LogError("NetworkConfig not found!");
                return;
            }

            Log("ImprovedWebSocketManager initialized.");
        }
        
        /// <summary>
        /// 连接到WebSocket服务器
        /// </summary>
        public void ConnectToServer()
        {
            if (IsConnected || IsConnecting) return;
            
            StartCoroutine(ConnectCoroutine());
        }
        
        /// <summary>
        /// 断开连接
        /// </summary>
        public void Disconnect()
        {
            if (_heartbeatCoroutine != null)
            {
                StopCoroutine(_heartbeatCoroutine);
                _heartbeatCoroutine = null;
            }
            
            if (_reconnectCoroutine != null)
            {
                StopCoroutine(_reconnectCoroutine);
                _reconnectCoroutine = null;
            }
            
            if (_connection != null)
            {
                _connection.Close();
                _connection = null;
            }
            
            IsConnected = false;
            IsConnecting = false;
            
            Log("Disconnected from WebSocket server.");
        }
        
        /// <summary>
        /// 加入房间
        /// </summary>
        public void JoinRoom(string roomId, string password = "")
        {
            if (!IsConnected)
            {
                OnError?.Invoke("未连接到服务器");
                return;
            }
            
            var playerInfo = new PlayerInfo
            {
                username = _authManager?.CurrentUser?.username ?? "Guest",
                color = "red",
                hat = "none",
                pet = "none"
            };
            
            var message = WebSocketMessageHelper.CreateJoinRoomMessage(roomId, password, playerInfo);
            SendMessage(message);
            
            Log($"Joining room: {roomId}");
        }
        
        /// <summary>
        /// 离开房间
        /// </summary>
        public void LeaveRoom()
        {
            if (!IsConnected || string.IsNullOrEmpty(CurrentRoomId))
                return;
                
            var message = new WebSocketMessage(WebSocketMessageTypes.LEAVE_ROOM, null, CurrentRoomId);
            SendMessage(message);
            
            Log($"Leaving room: {CurrentRoomId}");
        }
        
        /// <summary>
        /// 发送玩家移动
        /// </summary>
        public void SendPlayerMove(Vector3 position, Vector3 velocity, float rotation)
        {
            if (!IsConnected || string.IsNullOrEmpty(CurrentRoomId))
                return;
                
            var message = WebSocketMessageHelper.CreatePlayerMoveMessage(position, velocity, rotation, CurrentRoomId, null);
            SendMessage(message);
        }
        
        /// <summary>
        /// 发送聊天消息
        /// </summary>
        public void SendChatMessage(string text, string channel = "all")
        {
            if (!IsConnected || string.IsNullOrEmpty(CurrentRoomId))
                return;
                
            var senderName = _authManager?.CurrentUser?.username ?? "Guest";
            var message = WebSocketMessageHelper.CreateChatMessage(text, channel, senderName, false, CurrentRoomId);
            SendMessage(message);
            
            Log($"Sending chat message: {text}");
        }
        
        /// <summary>
        /// 发送投票
        /// </summary>
        public void SendVote(string targetId, bool isSkip = false)
        {
            if (!IsConnected || string.IsNullOrEmpty(CurrentRoomId))
                return;
                
            var message = WebSocketMessageHelper.CreateVoteMessage(targetId, isSkip, CurrentRoomId, null);
            SendMessage(message);
            
            Log($"Sending vote: {(isSkip ? "Skip" : targetId)}");
        }
        
        /// <summary>
        /// 发送消息
        /// </summary>
        public void SendMessage(WebSocketMessage message)
        {
            if (!IsConnected)
            {
                LogWarning("Cannot send message: not connected to server");
                return;
            }
            
            // 设置发送者ID
            if (_authManager != null && _authManager.IsAuthenticated)
            {
                message.sender_id = _authManager.CurrentUser?.id ?? "";
            }
            
            // 验证消息
            if (!WebSocketMessageHelper.ValidateMessage(message))
            {
                LogError("Invalid message format");
                return;
            }
            
            _outgoingMessages.Enqueue(message);
        }
        
        /// <summary>
        /// 连接协程
        /// </summary>
        private IEnumerator ConnectCoroutine()
        {
            IsConnecting = true;
            
            // 构建WebSocket URL，包含认证Token
            var wsUrl = _config.GetWebSocketUrl(ServiceType.Room);
            if (_authManager != null && _authManager.IsAuthenticated)
            {
                wsUrl = _config.GetWebSocketUrl(ServiceType.Room, _authManager.AccessToken);
            }
            
            Log($"Connecting to WebSocket server: {wsUrl}");
            
            // 创建WebSocket连接（这里使用模拟实现）
            _connection = new WebSocketConnection(wsUrl);
            _connection.OnConnected += HandleConnectionEstablished;
            _connection.OnDisconnected += HandleConnectionLost;
            _connection.OnMessageReceived += HandleMessageReceived;
            _connection.OnError += HandleConnectionError;
            
            yield return _connection.Connect();
            
            if (_connection.IsConnected)
            {
                IsConnected = true;
                IsConnecting = false;
                _reconnectAttempts = 0;
                
                // 启动心跳
                if (enableHeartbeat)
                {
                    _heartbeatCoroutine = StartCoroutine(HeartbeatCoroutine());
                }
                
                OnConnected?.Invoke();
                Log("Successfully connected to WebSocket server");
            }
            else
            {
                IsConnecting = false;
                OnError?.Invoke("连接失败");
            }
        }
        
        /// <summary>
        /// 处理传入消息
        /// </summary>
        private void ProcessIncomingMessages()
        {
            while (_incomingMessages.Count > 0)
            {
                var message = _incomingMessages.Dequeue();
                HandleWebSocketMessage(message);
            }
        }
        
        /// <summary>
        /// 处理传出消息
        /// </summary>
        private void ProcessOutgoingMessages()
        {
            while (_outgoingMessages.Count > 0 && IsConnected)
            {
                var message = _outgoingMessages.Dequeue();
                var json = JsonHelper.ToJson(message);
                _connection.Send(json);
                
                if (enableDebugLogs)
                {
                    Log($"Sent message: {message.type}");
                }
            }
        }
        
        /// <summary>
        /// 处理WebSocket消息
        /// </summary>
        private void HandleWebSocketMessage(WebSocketMessage message)
        {
            Log($"Received message: {message.type}");

            try
            {
                // 首先处理本地逻辑
                HandleLocalMessage(message);

                // 然后通过事件系统分发消息
                _eventSystem?.HandleMessage(message);

                // 最后转发给其他系统
                OnMessageReceived?.Invoke(message);
            }
            catch (System.Exception ex)
            {
                LogError($"Error handling message {message.type}: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理本地消息逻辑
        /// </summary>
        private void HandleLocalMessage(WebSocketMessage message)
        {
            switch (message.type)
            {
                case WebSocketMessageTypes.JOIN_ROOM:
                    HandleJoinRoomMessage(message);
                    break;

                case WebSocketMessageTypes.LEAVE_ROOM:
                    HandleLeaveRoomMessage(message);
                    break;

                case WebSocketMessageTypes.HEARTBEAT:
                    HandleHeartbeatMessage(message);
                    break;

                case WebSocketMessageTypes.ERROR:
                    HandleErrorMessage(message);
                    break;

                // 其他消息类型主要通过事件系统处理
                default:
                    break;
            }
        }
        
        /// <summary>
        /// 心跳协程
        /// </summary>
        private IEnumerator HeartbeatCoroutine()
        {
            while (IsConnected)
            {
                yield return new WaitForSeconds(heartbeatInterval);
                
                if (IsConnected)
                {
                    var ping = Time.time - _lastHeartbeat;
                    var heartbeat = WebSocketMessageHelper.CreateHeartbeatMessage(ping);
                    SendMessage(heartbeat);
                    _lastHeartbeat = Time.time;
                }
            }
        }
        
        /// <summary>
        /// 连接建立处理
        /// </summary>
        private void HandleConnectionEstablished()
        {
            _lastHeartbeat = Time.time;
            Log("WebSocket connection established");
        }
        
        /// <summary>
        /// 连接丢失处理
        /// </summary>
        private void HandleConnectionLost()
        {
            IsConnected = false;
            OnDisconnected?.Invoke();
            Log("WebSocket connection lost");
            
            // 尝试重连
            if (_reconnectAttempts < maxReconnectAttempts)
            {
                _reconnectCoroutine = StartCoroutine(ReconnectCoroutine());
            }
            else
            {
                LogError("Max reconnection attempts reached");
                OnError?.Invoke("连接断开，无法重连");
            }
        }
        
        /// <summary>
        /// 消息接收处理
        /// </summary>
        private void HandleMessageReceived(string messageText)
        {
            try
            {
                var message = JsonHelper.FromJson<WebSocketMessage>(messageText);
                if (message != null)
                {
                    _incomingMessages.Enqueue(message);
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to parse WebSocket message: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 连接错误处理
        /// </summary>
        private void HandleConnectionError(string error)
        {
            LogError($"WebSocket error: {error}");
            OnError?.Invoke($"WebSocket错误: {error}");
        }
        
        /// <summary>
        /// 重连协程
        /// </summary>
        private IEnumerator ReconnectCoroutine()
        {
            _reconnectAttempts++;
            Log($"Attempting to reconnect ({_reconnectAttempts}/{maxReconnectAttempts})...");
            
            yield return new WaitForSeconds(reconnectDelay);
            
            ConnectToServer();
        }
        
        /// <summary>
        /// 记录日志
        /// </summary>
        private void Log(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[ImprovedWebSocketManager] {message}");
            }
        }
        
        /// <summary>
        /// 记录警告日志
        /// </summary>
        private void LogWarning(string message)
        {
            if (enableDebugLogs)
            {
                Debug.LogWarning($"[ImprovedWebSocketManager] {message}");
            }
        }
        
        /// <summary>
        /// 记录错误日志
        /// </summary>
        private void LogError(string message)
        {
            Debug.LogError($"[ImprovedWebSocketManager] {message}");
        }

        #region 消息处理方法

        /// <summary>
        /// 处理加入房间消息
        /// </summary>
        private void HandleJoinRoomMessage(WebSocketMessage message)
        {
            var data = message.GetData<JoinRoomData>();
            if (data != null)
            {
                CurrentRoomId = data.room_id;
                Log($"Successfully joined room: {data.room_id}");
                OnRoomJoined?.Invoke(data.room_id);
            }
        }

        /// <summary>
        /// 处理离开房间消息
        /// </summary>
        private void HandleLeaveRoomMessage(WebSocketMessage message)
        {
            var oldRoomId = CurrentRoomId;
            CurrentRoomId = null;
            Log($"Left room: {oldRoomId}");
            OnRoomLeft?.Invoke(oldRoomId);
        }

        /// <summary>
        /// 处理房间更新消息
        /// </summary>
        private void HandleRoomUpdatedMessage(WebSocketMessage message)
        {
            var data = message.GetData<RoomUpdatedData>();
            if (data?.room != null)
            {
                Log($"Room updated: {data.room.name} ({data.players?.Count ?? 0} players)");
                // 可以触发房间状态更新事件
            }
        }

        /// <summary>
        /// 处理玩家加入消息
        /// </summary>
        private void HandlePlayerJoinedMessage(WebSocketMessage message)
        {
            var data = message.GetData<PlayerJoinedData>();
            if (data?.player != null)
            {
                Log($"Player joined: {data.player.username}");
                // 可以触发玩家加入事件
            }
        }

        /// <summary>
        /// 处理玩家离开消息
        /// </summary>
        private void HandlePlayerLeftMessage(WebSocketMessage message)
        {
            var data = message.GetData<PlayerLeftData>();
            if (data != null)
            {
                Log($"Player left: {data.username} (Reason: {data.reason})");
                // 可以触发玩家离开事件
            }
        }

        /// <summary>
        /// 处理玩家准备消息
        /// </summary>
        private void HandlePlayerReadyMessage(WebSocketMessage message)
        {
            Log($"Player ready state changed");
            // 可以触发玩家准备状态变化事件
        }

        /// <summary>
        /// 处理游戏开始消息
        /// </summary>
        private void HandleGameStartedMessage(WebSocketMessage message)
        {
            Log("Game started!");
            // 可以触发游戏开始事件
        }

        /// <summary>
        /// 处理游戏状态更新消息
        /// </summary>
        private void HandleGameStateUpdateMessage(WebSocketMessage message)
        {
            var data = message.GetData<GameStateUpdateData>();
            if (data?.game != null)
            {
                Log($"Game state updated: Phase={data.game.phase}, Round={data.game.round}");
                // 可以触发游戏状态更新事件
            }
        }

        /// <summary>
        /// 处理玩家移动消息
        /// </summary>
        private void HandlePlayerMoveMessage(WebSocketMessage message)
        {
            var data = message.GetData<PlayerMoveData>();
            if (data?.position != null)
            {
                // 处理玩家移动，通常会更新其他玩家的位置
                // Log($"Player moved to: {data.position}");
            }
        }

        /// <summary>
        /// 处理玩家动作消息
        /// </summary>
        private void HandlePlayerActionMessage(WebSocketMessage message)
        {
            var data = message.GetData<PlayerActionData>();
            if (data != null)
            {
                Log($"Player action: {data.action} at {data.location}");
                // 可以触发玩家动作事件
            }
        }

        /// <summary>
        /// 处理聊天消息
        /// </summary>
        private void HandleChatMessage(WebSocketMessage message)
        {
            var data = message.GetData<ChatMessageData>();
            if (data != null)
            {
                Log($"Chat [{data.channel}] {data.sender_name}: {data.message}");
                // 可以触发聊天消息事件
            }
        }

        /// <summary>
        /// 处理投票开始消息
        /// </summary>
        private void HandleVotingStartedMessage(WebSocketMessage message)
        {
            var data = message.GetData<VotingStartedData>();
            if (data?.voting_session != null)
            {
                Log($"Voting started: {data.voting_session.type} by {data.voting_session.caller_id}");
                // 可以触发投票开始事件
            }
        }

        /// <summary>
        /// 处理投票消息
        /// </summary>
        private void HandleVoteCastMessage(WebSocketMessage message)
        {
            var data = message.GetData<VoteCastData>();
            if (data != null)
            {
                var target = data.is_skip ? "Skip" : data.target_id;
                Log($"Vote cast by {data.voter_id} for {target}");
                // 可以触发投票事件
            }
        }

        /// <summary>
        /// 处理投票结束消息
        /// </summary>
        private void HandleVotingEndedMessage(WebSocketMessage message)
        {
            var data = message.GetData<VotingEndedData>();
            if (data?.result != null)
            {
                var ejected = string.IsNullOrEmpty(data.result.ejected_player_id) ? "No one" : data.result.ejected_player_id;
                Log($"Voting ended: {ejected} was ejected");
                // 可以触发投票结束事件
            }
        }

        /// <summary>
        /// 处理任务开始消息
        /// </summary>
        private void HandleTaskStartedMessage(WebSocketMessage message)
        {
            var data = message.GetData<TaskStartedData>();
            if (data?.task != null)
            {
                Log($"Task started: {data.task.name} by {data.player_id}");
                // 可以触发任务开始事件
            }
        }

        /// <summary>
        /// 处理任务进度消息
        /// </summary>
        private void HandleTaskProgressMessage(WebSocketMessage message)
        {
            var data = message.GetData<TaskProgressData>();
            if (data != null)
            {
                Log($"Task progress: {data.task_id} - {data.progress:P0}");
                // 可以触发任务进度事件
            }
        }

        /// <summary>
        /// 处理任务完成消息
        /// </summary>
        private void HandleTaskCompletedMessage(WebSocketMessage message)
        {
            var data = message.GetData<TaskCompletedData>();
            if (data?.task != null)
            {
                Log($"Task completed: {data.task.name} by {data.player_id} in {data.completion_time:F1}s");
                // 可以触发任务完成事件
            }
        }

        /// <summary>
        /// 处理玩家被杀消息
        /// </summary>
        private void HandlePlayerKilledMessage(WebSocketMessage message)
        {
            var data = message.GetData<PlayerKilledData>();
            if (data != null)
            {
                Log($"Player killed: {data.victim_id} by {data.killer_id} at {data.location}");
                // 可以触发玩家被杀事件
            }
        }

        /// <summary>
        /// 处理尸体报告消息
        /// </summary>
        private void HandleBodyReportedMessage(WebSocketMessage message)
        {
            var data = message.GetData<BodyReportedData>();
            if (data != null)
            {
                Log($"Body reported: {data.victim_id} by {data.reporter_id} at {data.location}");
                // 可以触发尸体报告事件
            }
        }

        /// <summary>
        /// 处理紧急会议消息
        /// </summary>
        private void HandleEmergencyMeetingMessage(WebSocketMessage message)
        {
            var data = message.GetData<EmergencyMeetingData>();
            if (data != null)
            {
                Log($"Emergency meeting called by {data.caller_id}: {data.reason}");
                // 可以触发紧急会议事件
            }
        }

        /// <summary>
        /// 处理破坏消息
        /// </summary>
        private void HandleSabotageMessage(WebSocketMessage message)
        {
            var data = message.GetData<SabotageData>();
            if (data != null)
            {
                Log($"Sabotage: {data.sabotage_type} at {data.target} by {data.saboteur_id}");
                // 可以触发破坏事件
            }
        }

        /// <summary>
        /// 处理心跳消息
        /// </summary>
        private void HandleHeartbeatMessage(WebSocketMessage message)
        {
            var data = message.GetData<HeartbeatData>();
            if (data != null)
            {
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var ping = (currentTime - data.client_time) * 1000; // 转换为毫秒
                Log($"Heartbeat received, ping: {ping:F0}ms");

                // 更新ping值
                _lastHeartbeat = Time.time;
            }
        }

        /// <summary>
        /// 处理错误消息
        /// </summary>
        private void HandleErrorMessage(WebSocketMessage message)
        {
            var data = message.GetData<ErrorMessageData>();
            if (data != null)
            {
                LogError($"Server error [{data.code}]: {data.message}");
                if (!string.IsNullOrEmpty(data.details))
                {
                    LogError($"Error details: {data.details}");
                }

                OnError?.Invoke(data.message ?? "未知服务器错误");

                // 如果有重试时间，可以安排重试
                if (data.retry_after > 0)
                {
                    Log($"Server requested retry after {data.retry_after} seconds");
                }
            }
        }

        #endregion
    }
    
    /// <summary>
    /// WebSocket连接类（模拟实现）
    /// 实际项目中需要使用真实的WebSocket库
    /// </summary>
    public class WebSocketConnection
    {
        public bool IsConnected { get; private set; }
        
        public event Action OnConnected;
        public event Action OnDisconnected;
        #pragma warning disable CS0067 // Event is never used
        public event Action<string> OnMessageReceived;
        public event Action<string> OnError;
        #pragma warning restore CS0067
        
        private string _url;
        
        public WebSocketConnection(string url)
        {
            _url = url;
        }
        
        public IEnumerator Connect()
        {
            // 模拟连接过程
            yield return new WaitForSeconds(1f);
            
            // 模拟连接成功
            IsConnected = true;
            OnConnected?.Invoke();
        }
        
        public void Send(string message)
        {
            if (!IsConnected) return;
            
            // 模拟发送消息
            Debug.Log($"[WebSocket] Sending: {message}");
        }
        
        public void Close()
        {
            if (!IsConnected) return;
            
            IsConnected = false;
            OnDisconnected?.Invoke();
        }
    }
}
