using UnityEngine;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 认证配置
    /// 管理认证相关的设置和参数
    /// </summary>
    [CreateAssetMenu(fileName = "AuthConfig", menuName = "GooseDuckKill/Auth Config")]
    public class AuthConfig : ScriptableObject
    {
        [Header("Token配置")]
        [SerializeField] public bool autoRefreshToken = true;
        [SerializeField] public float refreshThreshold = 300f; // 5分钟
        [SerializeField] public int maxRefreshRetries = 3;
        [SerializeField] public float refreshRetryDelay = 2f;
        
        [Header("会话配置")]
        [SerializeField] public bool rememberLogin = true;
        [SerializeField] public bool autoLogin = false;
        [SerializeField] public int sessionTimeout = 86400; // 24小时
        
        [Header("安全配置")]
        [SerializeField] public bool requireStrongPassword = true;
        [SerializeField] public int minPasswordLength = 8;
        [SerializeField] public bool requireEmailVerification = false;
        [SerializeField] public bool enableTwoFactorAuth = false;
        
        [Header("UI配置")]
        [SerializeField] public bool showPasswordStrength = true;
        [SerializeField] public bool enableBiometricAuth = false;
        [SerializeField] public bool showLoginHistory = false;
        
        [Header("调试配置")]
        [SerializeField] public bool enableDebugLogs = true;
        [SerializeField] public bool logTokenEvents = false;
        [SerializeField] public bool logNetworkRequests = false;
        
        // 单例实例
        private static AuthConfig _instance;
        
        /// <summary>
        /// 获取认证配置实例
        /// </summary>
        public static AuthConfig Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = Resources.Load<AuthConfig>("AuthConfig");
                    
                    if (_instance == null)
                    {
                        Debug.LogWarning("AuthConfig not found in Resources folder. Creating default config.");
                        _instance = CreateDefaultConfig();
                    }
                }
                
                return _instance;
            }
        }
        
        /// <summary>
        /// 创建默认配置
        /// </summary>
        private static AuthConfig CreateDefaultConfig()
        {
            var config = CreateInstance<AuthConfig>();
            
            // 设置默认值
            config.autoRefreshToken = true;
            config.refreshThreshold = 300f;
            config.maxRefreshRetries = 3;
            config.refreshRetryDelay = 2f;
            
            config.rememberLogin = true;
            config.autoLogin = false;
            config.sessionTimeout = 86400;
            
            config.requireStrongPassword = true;
            config.minPasswordLength = 8;
            config.requireEmailVerification = false;
            config.enableTwoFactorAuth = false;
            
            config.showPasswordStrength = true;
            config.enableBiometricAuth = false;
            config.showLoginHistory = false;
            
            config.enableDebugLogs = true;
            config.logTokenEvents = false;
            config.logNetworkRequests = false;
            
            return config;
        }
        
        /// <summary>
        /// 验证密码强度
        /// </summary>
        public PasswordStrength ValidatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
                return PasswordStrength.VeryWeak;
                
            var strength = PasswordStrength.VeryWeak;
            var score = 0;
            
            // 长度检查
            if (password.Length >= minPasswordLength)
                score += 1;
            if (password.Length >= 12)
                score += 1;
                
            // 字符类型检查
            bool hasLower = false, hasUpper = false, hasDigit = false, hasSpecial = false;
            
            foreach (char c in password)
            {
                if (char.IsLower(c)) hasLower = true;
                else if (char.IsUpper(c)) hasUpper = true;
                else if (char.IsDigit(c)) hasDigit = true;
                else if (!char.IsLetterOrDigit(c)) hasSpecial = true;
            }
            
            if (hasLower) score += 1;
            if (hasUpper) score += 1;
            if (hasDigit) score += 1;
            if (hasSpecial) score += 1;
            
            // 复杂度检查
            if (password.Length >= 16 && hasLower && hasUpper && hasDigit && hasSpecial)
                score += 1;
                
            // 确定强度等级
            switch (score)
            {
                case 0:
                case 1:
                    strength = PasswordStrength.VeryWeak;
                    break;
                case 2:
                case 3:
                    strength = PasswordStrength.Weak;
                    break;
                case 4:
                case 5:
                    strength = PasswordStrength.Medium;
                    break;
                case 6:
                    strength = PasswordStrength.Strong;
                    break;
                case 7:
                case 8:
                    strength = PasswordStrength.VeryStrong;
                    break;
            }
            
            return strength;
        }
        
        /// <summary>
        /// 检查密码是否符合要求
        /// </summary>
        public bool IsPasswordValid(string password)
        {
            if (!requireStrongPassword)
                return !string.IsNullOrEmpty(password) && password.Length >= minPasswordLength;
                
            var strength = ValidatePasswordStrength(password);
            return strength >= PasswordStrength.Medium;
        }
        
        /// <summary>
        /// 获取密码强度描述
        /// </summary>
        public string GetPasswordStrengthDescription(PasswordStrength strength)
        {
            switch (strength)
            {
                case PasswordStrength.VeryWeak:
                    return "非常弱";
                case PasswordStrength.Weak:
                    return "弱";
                case PasswordStrength.Medium:
                    return "中等";
                case PasswordStrength.Strong:
                    return "强";
                case PasswordStrength.VeryStrong:
                    return "非常强";
                default:
                    return "未知";
            }
        }
        
        /// <summary>
        /// 获取密码强度颜色
        /// </summary>
        public Color GetPasswordStrengthColor(PasswordStrength strength)
        {
            switch (strength)
            {
                case PasswordStrength.VeryWeak:
                    return Color.red;
                case PasswordStrength.Weak:
                    return new Color(1f, 0.5f, 0f); // 橙色
                case PasswordStrength.Medium:
                    return Color.yellow;
                case PasswordStrength.Strong:
                    return new Color(0.5f, 1f, 0f); // 浅绿色
                case PasswordStrength.VeryStrong:
                    return Color.green;
                default:
                    return Color.gray;
            }
        }
        
        /// <summary>
        /// 验证邮箱格式
        /// </summary>
        public bool IsEmailValid(string email)
        {
            if (string.IsNullOrEmpty(email))
                return false;
                
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 验证用户名格式
        /// </summary>
        public bool IsUsernameValid(string username)
        {
            if (string.IsNullOrEmpty(username))
                return false;
                
            // 用户名长度3-20，只能包含字母、数字、下划线
            if (username.Length < 3 || username.Length > 20)
                return false;
                
            foreach (char c in username)
            {
                if (!char.IsLetterOrDigit(c) && c != '_')
                    return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 获取配置摘要
        /// </summary>
        public string GetConfigSummary()
        {
            return $"AuthConfig Summary:\n" +
                   $"- Auto Refresh Token: {autoRefreshToken}\n" +
                   $"- Refresh Threshold: {refreshThreshold}s\n" +
                   $"- Remember Login: {rememberLogin}\n" +
                   $"- Strong Password Required: {requireStrongPassword}\n" +
                   $"- Min Password Length: {minPasswordLength}\n" +
                   $"- Email Verification: {requireEmailVerification}\n" +
                   $"- Two Factor Auth: {enableTwoFactorAuth}\n" +
                   $"- Debug Logs: {enableDebugLogs}";
        }
        
        /// <summary>
        /// 打印当前配置
        /// </summary>
        [ContextMenu("Print Config")]
        public void PrintCurrentConfig()
        {
            Debug.Log(GetConfigSummary());
        }
        
        /// <summary>
        /// 重置为默认配置
        /// </summary>
        [ContextMenu("Reset to Defaults")]
        public void ResetToDefaults()
        {
            var defaultConfig = CreateDefaultConfig();
            
            autoRefreshToken = defaultConfig.autoRefreshToken;
            refreshThreshold = defaultConfig.refreshThreshold;
            maxRefreshRetries = defaultConfig.maxRefreshRetries;
            refreshRetryDelay = defaultConfig.refreshRetryDelay;
            
            rememberLogin = defaultConfig.rememberLogin;
            autoLogin = defaultConfig.autoLogin;
            sessionTimeout = defaultConfig.sessionTimeout;
            
            requireStrongPassword = defaultConfig.requireStrongPassword;
            minPasswordLength = defaultConfig.minPasswordLength;
            requireEmailVerification = defaultConfig.requireEmailVerification;
            enableTwoFactorAuth = defaultConfig.enableTwoFactorAuth;
            
            showPasswordStrength = defaultConfig.showPasswordStrength;
            enableBiometricAuth = defaultConfig.enableBiometricAuth;
            showLoginHistory = defaultConfig.showLoginHistory;
            
            enableDebugLogs = defaultConfig.enableDebugLogs;
            logTokenEvents = defaultConfig.logTokenEvents;
            logNetworkRequests = defaultConfig.logNetworkRequests;
            
            Debug.Log("AuthConfig reset to defaults.");
        }
    }
    
    /// <summary>
    /// 密码强度枚举
    /// </summary>
    public enum PasswordStrength
    {
        VeryWeak = 0,
        Weak = 1,
        Medium = 2,
        Strong = 3,
        VeryStrong = 4
    }
}
