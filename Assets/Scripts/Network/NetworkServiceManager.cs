﻿﻿﻿﻿﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using GooseDuckKill.Network.Models;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 网络服务管理器 - 统一管理所有网络服务的调用
    /// 提供统一的API调用接口，确保与后台服务的一致性
    /// </summary>
    public class NetworkServiceManager : MonoBehaviour
    {
        [Header("配置")]
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private bool autoInitialize = true;
        
        // 单例实例
        private static NetworkServiceManager _instance;
        public static NetworkServiceManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("NetworkServiceManager");
                    _instance = go.AddComponent<NetworkServiceManager>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }
        
        // 网络配置
        private NetworkConfig _config;
        
        // 认证管理器引用
        private AuthManager _authManager;
        
        // 事件
        public event Action<string> OnNetworkError;
        public event Action<ServiceType, bool> OnServiceStatusChanged;
        
        // 服务状态
        private Dictionary<ServiceType, bool> _serviceStatus = new Dictionary<ServiceType, bool>();
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                
                if (autoInitialize)
                {
                    Initialize();
                }
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }
        
        /// <summary>
        /// 初始化网络服务管理器
        /// </summary>
        public void Initialize()
        {
            _config = NetworkConfig.Instance;
            if (_config == null)
            {
                LogError("NetworkConfig not found! Please create a NetworkConfig asset.");
                return;
            }
            
            // 验证配置
            if (!_config.ValidateConfig())
            {
                LogError("NetworkConfig validation failed!");
                return;
            }
            
            // 获取认证管理器
            _authManager = FindFirstObjectByType<AuthManager>();
            if (_authManager == null)
            {
                LogWarning("AuthManager not found. Some features may not work properly.");
            }
            
            // 初始化服务状态
            _serviceStatus[ServiceType.Auth] = false;
            _serviceStatus[ServiceType.Game] = false;
            _serviceStatus[ServiceType.Room] = false;
            
            // 打印配置信息
            if (enableDebugLogs)
            {
                _config.PrintCurrentConfig();
            }
            
            // 开始健康检查
            StartCoroutine(HealthCheckCoroutine());
            
            Log("NetworkServiceManager initialized successfully.");
        }
        
        /// <summary>
        /// 发送GET请求
        /// </summary>
        /// <typeparam name="T">响应数据类型</typeparam>
        /// <param name="service">服务类型</param>
        /// <param name="endpoint">API端点</param>
        /// <param name="requireAuth">是否需要认证</param>
        /// <param name="callback">回调函数</param>
        public void SendGetRequest<T>(ServiceType service, string endpoint, bool requireAuth, Action<APIResponse<T>> callback)
        {
            StartCoroutine(SendRequestCoroutine("GET", service, endpoint, null, requireAuth, callback));
        }
        
        /// <summary>
        /// 发送POST请求
        /// </summary>
        /// <typeparam name="T">响应数据类型</typeparam>
        /// <param name="service">服务类型</param>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">请求数据</param>
        /// <param name="requireAuth">是否需要认证</param>
        /// <param name="callback">回调函数</param>
        public void SendPostRequest<T>(ServiceType service, string endpoint, object data, bool requireAuth, Action<APIResponse<T>> callback)
        {
            StartCoroutine(SendRequestCoroutine("POST", service, endpoint, data, requireAuth, callback));
        }
        
        /// <summary>
        /// 发送PUT请求
        /// </summary>
        /// <typeparam name="T">响应数据类型</typeparam>
        /// <param name="service">服务类型</param>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">请求数据</param>
        /// <param name="requireAuth">是否需要认证</param>
        /// <param name="callback">回调函数</param>
        public void SendPutRequest<T>(ServiceType service, string endpoint, object data, bool requireAuth, Action<APIResponse<T>> callback)
        {
            StartCoroutine(SendRequestCoroutine("PUT", service, endpoint, data, requireAuth, callback));
        }
        
        /// <summary>
        /// 发送DELETE请求
        /// </summary>
        /// <typeparam name="T">响应数据类型</typeparam>
        /// <param name="service">服务类型</param>
        /// <param name="endpoint">API端点</param>
        /// <param name="requireAuth">是否需要认证</param>
        /// <param name="callback">回调函数</param>
        public void SendDeleteRequest<T>(ServiceType service, string endpoint, bool requireAuth, Action<APIResponse<T>> callback)
        {
            StartCoroutine(SendRequestCoroutine("DELETE", service, endpoint, null, requireAuth, callback));
        }
        
        /// <summary>
        /// 检查服务健康状态
        /// </summary>
        /// <param name="service">服务类型</param>
        /// <param name="callback">回调函数</param>
        public void CheckServiceHealth(ServiceType service, Action<bool> callback)
        {
            var healthEndpoint = "/health";
            SendGetRequest<object>(service, healthEndpoint, false, response =>
            {
                bool isHealthy = response != null && response.IsSuccess;
                UpdateServiceStatus(service, isHealthy);
                callback?.Invoke(isHealthy);
            });
        }
        
        /// <summary>
        /// 获取服务状态
        /// </summary>
        /// <param name="service">服务类型</param>
        /// <returns>服务是否健康</returns>
        public bool GetServiceStatus(ServiceType service)
        {
            return _serviceStatus.ContainsKey(service) && _serviceStatus[service];
        }
        
        /// <summary>
        /// 获取WebSocket URL
        /// </summary>
        /// <param name="service">服务类型</param>
        /// <returns>WebSocket URL</returns>
        public string GetWebSocketUrl(ServiceType service)
        {
            string token = null;
            if (_authManager != null && _authManager.IsAuthenticated)
            {
                token = _authManager.AccessToken;
            }
            
            return _config.GetWebSocketUrl(service, token);
        }
        
        /// <summary>
        /// 发送HTTP请求的协程
        /// </summary>
        private IEnumerator SendRequestCoroutine<T>(string method, ServiceType service, string endpoint, object data, bool requireAuth, Action<APIResponse<T>> callback)
        {
            var url = _config.GetApiUrl(service, endpoint);
            
            Log($"Sending {method} request to: {url}");
            
            using (var request = new UnityWebRequest(url, method))
            {
                // 设置请求体
                if (data != null && (method == "POST" || method == "PUT"))
                {
                    var jsonData = JsonHelper.ToJson(data);
                    var bodyRaw = System.Text.Encoding.UTF8.GetBytes(jsonData);
                    request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                    request.SetRequestHeader("Content-Type", "application/json");
                }
                
                request.downloadHandler = new DownloadHandlerBuffer();
                request.timeout = _config.RequestTimeout;
                
                // 添加认证头
                if (requireAuth && _authManager != null && _authManager.IsAuthenticated)
                {
                    request.SetRequestHeader("Authorization", _authManager.GetAuthHeader());
                }
                
                // 添加其他通用头
                request.SetRequestHeader("Accept", "application/json");
                request.SetRequestHeader("User-Agent", "GooseDuckKill-Unity-Client/1.0");
                
                yield return request.SendWebRequest();
                
                APIResponse<T> response = null;
                
                if (request.result == UnityWebRequest.Result.Success)
                {
                    try
                    {
                        var responseText = request.downloadHandler.text;
                        Log($"Response received: {responseText}");

                        response = JsonHelper.FromJson<APIResponse<T>>(responseText);
                        UpdateServiceStatus(service, true);
                    }
                    catch (Exception ex)
                    {
                        LogError($"Failed to parse response: {ex.Message}");
                        response = new APIResponse<T>
                        {
                            code = 500,
                            message = "Failed to parse response",
                            data = default(T)
                        };
                    }
                }
                else
                {
                    LogError($"Request failed: {request.responseCode} - {request.error}");
                    UpdateServiceStatus(service, false);
                    
                    response = new APIResponse<T>
                    {
                        code = (int)request.responseCode,
                        message = request.error,
                        data = default(T)
                    };
                    
                    OnNetworkError?.Invoke($"Request to {service} failed: {request.error}");
                }
                
                callback?.Invoke(response);
            }
        }
        
        /// <summary>
        /// 健康检查协程
        /// </summary>
        private IEnumerator HealthCheckCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(60f); // 每分钟检查一次
                
                // 检查所有服务的健康状态
                CheckServiceHealth(ServiceType.Auth, null);
                CheckServiceHealth(ServiceType.Game, null);
                CheckServiceHealth(ServiceType.Room, null);
            }
        }
        
        /// <summary>
        /// 更新服务状态
        /// </summary>
        private void UpdateServiceStatus(ServiceType service, bool isHealthy)
        {
            bool statusChanged = !_serviceStatus.ContainsKey(service) || _serviceStatus[service] != isHealthy;
            
            _serviceStatus[service] = isHealthy;
            
            if (statusChanged)
            {
                Log($"Service {service} status changed to: {(isHealthy ? "Healthy" : "Unhealthy")}");
                OnServiceStatusChanged?.Invoke(service, isHealthy);
            }
        }
        
        /// <summary>
        /// 记录日志
        /// </summary>
        private void Log(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[NetworkServiceManager] {message}");
            }
        }
        
        /// <summary>
        /// 记录警告日志
        /// </summary>
        private void LogWarning(string message)
        {
            if (enableDebugLogs)
            {
                Debug.LogWarning($"[NetworkServiceManager] {message}");
            }
        }
        
        /// <summary>
        /// 记录错误日志
        /// </summary>
        private void LogError(string message)
        {
            Debug.LogError($"[NetworkServiceManager] {message}");
        }
    }
    

}
