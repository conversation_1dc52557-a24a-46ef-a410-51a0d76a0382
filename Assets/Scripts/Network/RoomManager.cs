using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 房间管理器 - 管理房间创建、加入和列表等功能
    /// </summary>
    public class RoomManager : MonoBehaviour
    {
        [Header("Room Settings")]
        [SerializeField] private int defaultMaxPlayers = 16;
        [SerializeField] private bool defaultIsPublic = true;
        [SerializeField] private bool defaultIsOpen = true;
        [SerializeField] private float roomListRefreshRate = 5f;
        
        // 当前房间会话信息
        private RoomInfo _currentRoom;
        private List<RoomInfo> _availableRooms = new List<RoomInfo>();
        private float _lastRefreshTime;
        
        // 事件
        public event Action<RoomInfo> OnJoinedRoom;
        public event Action<RoomInfo> OnLeftRoom;
        public event Action<List<RoomInfo>> OnRoomListUpdated;
        
        // 属性
        public RoomInfo CurrentRoom => _currentRoom;
        public List<RoomInfo> AvailableRooms => _availableRooms;
        public bool IsInRoom => _currentRoom != null;
        public int RoomCount => _availableRooms.Count;
        
        private void Awake()
        {
            _lastRefreshTime = -roomListRefreshRate; // 确保首次更新立即发生
        }
        
        private void OnEnable()
        {
            // 订阅网络管理器事件
            if (NetworkManager.Instance != null)
            {
                NetworkManager.Instance.OnRoomListUpdated += HandleRoomListUpdated;
                NetworkManager.Instance.OnConnectedToServerEvent += HandleConnectedToServer;
                NetworkManager.Instance.OnDisconnectedFromServerEvent += HandleDisconnectedFromServer;
            }
        }
        
        private void OnDisable()
        {
            // 取消订阅网络管理器事件
            if (NetworkManager.Instance != null)
            {
                NetworkManager.Instance.OnRoomListUpdated -= HandleRoomListUpdated;
                NetworkManager.Instance.OnConnectedToServerEvent -= HandleConnectedToServer;
                NetworkManager.Instance.OnDisconnectedFromServerEvent -= HandleDisconnectedFromServer;
            }
        }
        
        private void Update()
        {
            // 定期刷新房间列表
            if (Time.time - _lastRefreshTime >= roomListRefreshRate)
            {
                RefreshRoomList();
                _lastRefreshTime = Time.time;
            }
        }
        
        /// <summary>
        /// 刷新房间列表
        /// </summary>
        public void RefreshRoomList()
        {
            if (NetworkManager.Instance != null && NetworkManager.Instance.Runner != null)
            {
                // 重新获取会话列表
                NetworkManager.Instance.StartRoomList();
            }
        }
        
        /// <summary>
        /// 创建新房间
        /// </summary>
        public void CreateRoom(string roomName, int maxPlayers = 0, bool? isPublic = null, bool? isOpen = null)
        {
            if (NetworkManager.Instance != null)
            {
                if (maxPlayers <= 0) maxPlayers = defaultMaxPlayers;
                
                // 使用提供的值或默认值
                bool roomIsOpen = isOpen ?? defaultIsOpen;
                bool roomIsVisible = isPublic ?? defaultIsPublic;
                
                // 创建房间，传递isOpen和isVisible(isPublic)
                NetworkManager.Instance.CreateRoom(roomName, roomIsOpen, roomIsVisible);
                
                // 创建房间信息
                _currentRoom = new RoomInfo(roomName, maxPlayers, roomIsOpen, roomIsVisible);
                
                // 通知房间创建
                OnJoinedRoom?.Invoke(_currentRoom);
            }
        }
        
        /// <summary>
        /// 加入房间
        /// </summary>
        public void JoinRoom(string roomName)
        {
            if (NetworkManager.Instance != null)
            {
                // 查找房间信息
                RoomInfo roomInfo = _availableRooms.Find(r => r.Name == roomName);
                
                // 加入房间
                NetworkManager.Instance.JoinRoom(roomName);
                
                // 更新当前房间
                _currentRoom = roomInfo;
                
                // 通知房间加入
                if (roomInfo != null)
                {
                    OnJoinedRoom?.Invoke(roomInfo);
                }
            }
        }
        
        /// <summary>
        /// 加入指定房间
        /// </summary>
        public void JoinRoom(RoomInfo roomInfo)
        {
            if (roomInfo != null && NetworkManager.Instance != null)
            {
                // 加入房间
                NetworkManager.Instance.JoinRoom(roomInfo.Name);
                
                // 更新当前房间
                _currentRoom = roomInfo;
                
                // 通知房间加入
                OnJoinedRoom?.Invoke(roomInfo);
            }
        }
        
        /// <summary>
        /// 离开当前房间
        /// </summary>
        public void LeaveRoom()
        {
            if (NetworkManager.Instance != null)
            {
                // 断开连接
                NetworkManager.Instance.DisconnectFromServer();
                
                // 通知房间离开
                if (_currentRoom != null)
                {
                    OnLeftRoom?.Invoke(_currentRoom);
                    _currentRoom = null;
                }
            }
        }
        
        /// <summary>
        /// 获取指定名称的房间
        /// </summary>
        public RoomInfo GetRoom(string roomName)
        {
            return _availableRooms.Find(r => r.Name == roomName);
        }
        
        #region 事件处理器
        
        private void HandleRoomListUpdated(List<SessionInfo> sessionList)
        {
            // 将SessionInfo列表转换为RoomInfo列表
            _availableRooms = RoomInfo.FromSessionInfoList(sessionList);
            
            // 通知房间列表更新
            OnRoomListUpdated?.Invoke(_availableRooms);
        }
        
        private void HandleConnectedToServer()
        {
            // 连接成功后，检查当前房间信息
            if (NetworkManager.Instance != null && NetworkManager.Instance.Runner != null)
            {
                var sessionInfo = NetworkManager.Instance.Runner.SessionInfo;
                // 检查SessionInfo是否有效
                if (sessionInfo != null && !string.IsNullOrEmpty(sessionInfo.Name))
                {
                    _currentRoom = new RoomInfo(sessionInfo);
                    OnJoinedRoom?.Invoke(_currentRoom);
                }
            }
        }
        
        private void HandleDisconnectedFromServer(NetDisconnectReason reason)
        {
            // 断开连接后，清除当前房间信息
            if (_currentRoom != null)
            {
                OnLeftRoom?.Invoke(_currentRoom);
                _currentRoom = null;
            }
        }
        
        #endregion
    }
} 