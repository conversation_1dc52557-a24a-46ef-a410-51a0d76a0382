using UnityEngine;
using CustomNetworking.Core;
using CustomNetworking.Core.RPC;
using System.Collections;

namespace GooseDuckKill.Examples
{
    /// <summary>
    /// RPC 测试管理器
    /// 用于测试和演示 RPC 系统的功能
    /// </summary>
    public class RpcTestManager : MonoBehaviour
    {
        [Header("Test Settings")]
        public GameObject rpcExamplePrefab;
        public bool autoStartTest = true;
        public float testInterval = 5f;
        
        [Header("Network Settings")]
        public NetworkRunner.GameMode gameMode = NetworkRunner.GameMode.Host;
        public string sessionName = "RPC Test Session";
        public int maxPlayers = 4;
        
        private NetworkRunner _runner;
        private RpcExampleBehaviour _testBehaviour;
        private bool _isTestRunning = false;
        
        private void Start()
        {
            if (autoStartTest)
            {
                StartCoroutine(InitializeNetworkAndTest());
            }
        }
        
        private IEnumerator InitializeNetworkAndTest()
        {
            Debug.Log("Starting RPC Test Manager...");

            // 创建网络运行器
            var runnerObject = new GameObject("NetworkRunner");
            _runner = runnerObject.AddComponent<NetworkRunner>();

            // 启动网络
            var args = new StartGameArgs
            {
                GameMode = gameMode,
                SessionName = sessionName,
                MaxPlayers = maxPlayers
            };

            // 使用协程来处理异步操作
            yield return StartCoroutine(StartNetworkCoroutine(args));
        }

        private IEnumerator StartNetworkCoroutine(StartGameArgs args)
        {
            var startTask = _runner.StartGame(args);

            // 等待任务完成
            while (!startTask.IsCompleted)
            {
                yield return null;
            }

            var result = startTask.Result;

            if (result.Ok)
            {
                Debug.Log("Network started successfully!");

                // 等待一秒让网络稳定
                yield return new WaitForSeconds(1f);

                // 生成测试对象
                SpawnTestObject();

                // 开始测试
                if (_testBehaviour != null)
                {
                    StartCoroutine(RunRpcTests());
                }
            }
            else
            {
                Debug.LogError($"Failed to start network: {result.ErrorMessage}");
            }
        }
        
        private void SpawnTestObject()
        {
            if (_runner == null || !_runner.IsServer)
            {
                Debug.LogWarning("Cannot spawn test object: not server");
                return;
            }
            
            GameObject testObject;
            
            if (rpcExamplePrefab != null)
            {
                // 使用预制体
                var networkObject = _runner.Spawn(rpcExamplePrefab, Vector3.zero, Quaternion.identity, _runner.LocalPlayer);
                testObject = networkObject.gameObject;
            }
            else
            {
                // 创建简单的测试对象
                testObject = new GameObject("RPC Test Object");
                var networkObject = testObject.AddComponent<NetworkObject>();
                _testBehaviour = testObject.AddComponent<RpcExampleBehaviour>();
                
                // 手动初始化网络对象
                networkObject.Initialize(_runner, new NetworkId(1), _runner.LocalPlayer);
            }
            
            _testBehaviour = testObject.GetComponent<RpcExampleBehaviour>();
            if (_testBehaviour != null)
            {
                _testBehaviour.playerName = $"TestPlayer_{_runner.LocalPlayer.PlayerId}";
                Debug.Log($"Spawned test object with RpcExampleBehaviour: {_testBehaviour.playerName}");
            }
        }
        
        private IEnumerator RunRpcTests()
        {
            _isTestRunning = true;
            Debug.Log("Starting RPC tests...");
            
            yield return new WaitForSeconds(2f);
            
            int testCount = 0;
            
            while (_isTestRunning && _testBehaviour != null)
            {
                testCount++;
                Debug.Log($"--- RPC Test #{testCount} ---");
                
                // 测试 1: 基本聊天消息
                _testBehaviour.SendChatMessage($"Test message #{testCount}");
                yield return new WaitForSeconds(1f);
                
                // 测试 2: 玩家行动
                string[] actions = { "Jump", "Attack", "Defend", "Use Item", "Cast Spell" };
                string randomAction = actions[Random.Range(0, actions.Length)];
                _testBehaviour.PerformAction(randomAction);
                yield return new WaitForSeconds(1f);
                
                // 测试 3: 复杂状态同步
                _testBehaviour.SyncComplexState(Random.Range(0f, 100f), Random.value > 0.3f);
                yield return new WaitForSeconds(1f);
                
                // 测试 4: 紧急消息（每5次测试一次）
                if (testCount % 5 == 0)
                {
                    _testBehaviour.SendEmergencyMessage($"Emergency test #{testCount / 5}");
                    yield return new WaitForSeconds(1f);
                }
                
                Debug.Log($"RPC Test #{testCount} completed");
                yield return new WaitForSeconds(testInterval);
            }
        }
        
        #region 公共控制方法
        
        [ContextMenu("Start Manual Test")]
        public void StartManualTest()
        {
            if (!_isTestRunning)
            {
                StartCoroutine(InitializeNetworkAndTest());
            }
        }
        
        [ContextMenu("Stop Test")]
        public void StopTest()
        {
            _isTestRunning = false;
            StopAllCoroutines();
            
            if (_runner != null)
            {
                _runner.Shutdown();
            }
        }
        
        [ContextMenu("Send Test Chat")]
        public void SendTestChat()
        {
            if (_testBehaviour != null)
            {
                _testBehaviour.SendChatMessage("Manual test message");
            }
        }
        
        [ContextMenu("Send Test Action")]
        public void SendTestAction()
        {
            if (_testBehaviour != null)
            {
                _testBehaviour.PerformAction("Manual Action");
            }
        }
        
        [ContextMenu("Send Emergency")]
        public void SendTestEmergency()
        {
            if (_testBehaviour != null)
            {
                _testBehaviour.SendEmergencyMessage("Manual emergency test");
            }
        }
        
        #endregion
        
        #region Unity 事件
        
        private void Update()
        {
            // 键盘快捷键
            if (Input.GetKeyDown(KeyCode.F1))
            {
                StartManualTest();
            }
            
            if (Input.GetKeyDown(KeyCode.F2))
            {
                StopTest();
            }
            
            if (Input.GetKeyDown(KeyCode.F3))
            {
                SendTestChat();
            }
            
            if (Input.GetKeyDown(KeyCode.F4))
            {
                SendTestAction();
            }
            
            if (Input.GetKeyDown(KeyCode.F5))
            {
                SendTestEmergency();
            }
        }
        
        private void OnDestroy()
        {
            StopTest();
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("RPC Test Manager", GUI.skin.box);
            
            if (_runner != null)
            {
                GUILayout.Label($"Network State: {_runner.State}");
                GUILayout.Label($"Game Mode: {_runner.Mode}");
                GUILayout.Label($"Local Player: {_runner.LocalPlayer}");
                GUILayout.Label($"Is Server: {_runner.IsServer}");
            }
            else
            {
                GUILayout.Label("Network: Not Started");
            }
            
            GUILayout.Label($"Test Running: {_isTestRunning}");
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("Start Test (F1)"))
            {
                StartManualTest();
            }
            
            if (GUILayout.Button("Stop Test (F2)"))
            {
                StopTest();
            }
            
            if (_testBehaviour != null)
            {
                if (GUILayout.Button("Send Chat (F3)"))
                {
                    SendTestChat();
                }
                
                if (GUILayout.Button("Send Action (F4)"))
                {
                    SendTestAction();
                }
                
                if (GUILayout.Button("Emergency (F5)"))
                {
                    SendTestEmergency();
                }
            }
            
            GUILayout.EndArea();
        }
        
        #endregion
    }
}
