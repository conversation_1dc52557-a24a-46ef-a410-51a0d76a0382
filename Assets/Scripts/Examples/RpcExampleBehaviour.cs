using UnityEngine;
using CustomNetworking.Core;
using CustomNetworking.Core.RPC;

namespace GooseDuckKill.Examples
{
    /// <summary>
    /// RPC 系统使用示例
    /// 演示如何在 NetworkBehaviour 中使用 RPC 调用
    /// </summary>
    public class RpcExampleBehaviour : NetworkBehaviour
    {
        [Header("RPC Example Settings")]
        public float messageInterval = 2f;
        public string playerName = "Player";
        
        private float _lastMessageTime;
        private int _messageCounter = 0;
        
        public override void Spawned()
        {
            base.Spawned();
            Debug.Log($"RpcExampleBehaviour spawned for {playerName}");
            
            // 如果有状态权限，开始发送消息
            if (HasStateAuthority)
            {
                _lastMessageTime = Time.time;
            }
        }
        
        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();
            
            // 只有有状态权限的客户端才能发送定期消息
            if (HasStateAuthority && Time.time - _lastMessageTime >= messageInterval)
            {
                _lastMessageTime = Time.time;
                _messageCounter++;
                
                // 发送 RPC 到所有客户端
                RPC(nameof(OnPeriodicMessage), $"Hello from {playerName}! Message #{_messageCounter}");
            }
        }
        
        #region RPC 方法示例
        
        /// <summary>
        /// 发送消息到所有客户端的 RPC
        /// </summary>
        [Rpc(RpcTargets.All, reliable: true)]
        public void OnPeriodicMessage(string message)
        {
            Debug.Log($"[RPC] Received periodic message: {message}");
            
            // 在 UI 中显示消息（如果有的话）
            if (HasInputAuthority)
            {
                ShowMessageOnUI($"📢 {message}");
            }
        }
        
        /// <summary>
        /// 发送私人消息到指定玩家的 RPC
        /// </summary>
        [Rpc(RpcTargets.Player, reliable: true)]
        public void OnPrivateMessage(string message, string fromPlayer)
        {
            Debug.Log($"[RPC] Received private message from {fromPlayer}: {message}");
            ShowMessageOnUI($"💬 {fromPlayer}: {message}");
        }
        
        /// <summary>
        /// 发送到服务器的 RPC
        /// </summary>
        [Rpc(RpcTargets.Server, reliable: true)]
        public void OnPlayerAction(string actionName, Vector3 position)
        {
            Debug.Log($"[RPC] Player {playerName} performed action '{actionName}' at {position}");
            
            // 服务器验证并广播给其他玩家
            if (HasStateAuthority)
            {
                RPC(nameof(OnPlayerActionBroadcast), playerName, actionName, position, RpcTargets.Others);
            }
        }
        
        /// <summary>
        /// 广播玩家行动到其他客户端的 RPC
        /// </summary>
        [Rpc(RpcTargets.Others, reliable: false)]
        public void OnPlayerActionBroadcast(string playerName, string actionName, Vector3 position)
        {
            Debug.Log($"[RPC] {playerName} performed {actionName} at {position}");
            
            // 播放动画或效果
            PlayActionEffect(actionName, position);
        }
        
        /// <summary>
        /// 高优先级紧急消息 RPC
        /// </summary>
        [Rpc(RpcTargets.All, reliable: true)]
        public void OnEmergencyMessage(string message)
        {
            Debug.LogWarning($"[RPC] EMERGENCY: {message}");
            ShowMessageOnUI($"🚨 EMERGENCY: {message}");
        }
        
        /// <summary>
        /// 发送复杂数据结构的 RPC
        /// </summary>
        [Rpc(RpcTargets.All, reliable: true)]
        public void OnComplexDataMessage(Vector3 position, Quaternion rotation, float health, bool isAlive)
        {
            Debug.Log($"[RPC] Complex data - Pos: {position}, Rot: {rotation}, Health: {health}, Alive: {isAlive}");
            
            // 更新游戏对象状态
            UpdateGameObjectState(position, rotation, health, isAlive);
        }
        
        #endregion
        
        #region 公共方法 - 供外部调用
        
        /// <summary>
        /// 发送聊天消息
        /// </summary>
        public void SendChatMessage(string message)
        {
            if (HasInputAuthority)
            {
                RPC(nameof(OnPeriodicMessage), $"{playerName}: {message}");
            }
        }
        
        /// <summary>
        /// 发送私人消息给指定玩家
        /// </summary>
        public void SendPrivateMessage(PlayerRef targetPlayer, string message)
        {
            if (HasInputAuthority)
            {
                RPC(nameof(OnPrivateMessage), targetPlayer, message, playerName);
            }
        }
        
        /// <summary>
        /// 执行玩家行动
        /// </summary>
        public void PerformAction(string actionName)
        {
            if (HasInputAuthority)
            {
                RPC(nameof(OnPlayerAction), actionName, transform.position);
            }
        }
        
        /// <summary>
        /// 发送紧急消息（仅服务器可调用）
        /// </summary>
        public void SendEmergencyMessage(string message)
        {
            if (HasStateAuthority)
            {
                RPC(nameof(OnEmergencyMessage), message);
            }
        }
        
        /// <summary>
        /// 同步复杂状态
        /// </summary>
        public void SyncComplexState(float health, bool isAlive)
        {
            if (HasStateAuthority)
            {
                RPC(nameof(OnComplexDataMessage), transform.position, transform.rotation, health, isAlive);
            }
        }
        
        #endregion
        
        #region 辅助方法
        
        private void ShowMessageOnUI(string message)
        {
            // 这里可以集成实际的 UI 系统
            Debug.Log($"[UI] {message}");
        }
        
        private void PlayActionEffect(string actionName, Vector3 position)
        {
            // 这里可以播放粒子效果、声音等
            Debug.Log($"[Effect] Playing {actionName} effect at {position}");
        }
        
        private void UpdateGameObjectState(Vector3 position, Quaternion rotation, float health, bool isAlive)
        {
            // 更新游戏对象的状态
            if (!HasStateAuthority) // 只有非权威客户端才需要更新
            {
                transform.position = position;
                transform.rotation = rotation;
                // 更新健康值和存活状态
            }
        }
        
        #endregion
        
        #region Unity 事件 - 用于测试
        
        private void Update()
        {
            // 测试按键
            if (HasInputAuthority)
            {
                if (Input.GetKeyDown(KeyCode.Alpha1))
                {
                    SendChatMessage("Hello everyone!");
                }
                
                if (Input.GetKeyDown(KeyCode.Alpha2))
                {
                    PerformAction("Jump");
                }
                
                if (Input.GetKeyDown(KeyCode.Alpha3) && HasStateAuthority)
                {
                    SendEmergencyMessage("Server emergency test!");
                }
                
                if (Input.GetKeyDown(KeyCode.Alpha4))
                {
                    SyncComplexState(Random.Range(0f, 100f), Random.value > 0.5f);
                }
            }
        }
        
        #endregion
    }
}
