using UnityEngine;
using CustomNetworking.Core;
using System.Collections.Generic;
using System;

namespace CustomNetworking.Examples
{
    /// <summary>
    /// 自定义网络框架使用示例
    /// </summary>
    public class CustomNetworkExample : MonoBehaviour
    {
        [Header("网络设置")]
        [SerializeField] private string serverAddress = "127.0.0.1";
        [SerializeField] private int serverPort = 7777;
        [SerializeField] private string roomName = "ExampleRoom";
        
        [Header("游戏对象")]
        [SerializeField] private GameObject playerPrefab;
        [SerializeField] private Transform spawnPoint;
        
        private NetworkRunner _runner;
        private bool _isConnected = false;
        
        #region Unity 生命周期
        
        private void Start()
        {
            // 初始化网络运行器
            InitializeNetworkRunner();
        }
        
        private void OnGUI()
        {
            DrawNetworkGUI();
        }
        
        private void OnDestroy()
        {
            if (_runner != null && _runner.IsRunning)
            {
                _runner.Shutdown();
            }
        }
        
        #endregion
        
        #region 网络初始化
        
        private void InitializeNetworkRunner()
        {
            // 创建网络运行器
            _runner = gameObject.AddComponent<NetworkRunner>();
            
            // 添加回调
            _runner.AddCallbacks(new ExampleNetworkCallbacks());
            
            // 注册事件 - 通过回调系统处理
            // NetworkRunner 使用回调系统而不是事件系统
        }
        
        #endregion
        
        #region 网络操作
        
        public async void StartAsHost()
        {
            if (_runner.IsRunning) return;
            
            UnityEngine.Debug.Log("启动主机...");
            
            var args = new StartGameArgs
            {
                GameMode = NetworkRunner.GameMode.Host,
                Address = new NetAddress(serverAddress, serverPort),
                SessionName = roomName,
                MaxPlayers = 10
            };
            
            var result = await _runner.StartGame(args);
            
            if (result.Ok)
            {
                UnityEngine.Debug.Log("✅ 主机启动成功");
                _isConnected = true;
                
                // 生成本地玩家
                SpawnLocalPlayer();
            }
            else
            {
                UnityEngine.Debug.LogError($"❌ 主机启动失败: {result.ErrorMessage}");
            }
        }
        
        public async void StartAsClient()
        {
            if (_runner.IsRunning) return;
            
            UnityEngine.Debug.Log("连接到服务器...");
            
            var args = new StartGameArgs
            {
                GameMode = NetworkRunner.GameMode.Client,
                Address = new NetAddress(serverAddress, serverPort),
                SessionName = roomName
            };
            
            var result = await _runner.StartGame(args);
            
            if (result.Ok)
            {
                UnityEngine.Debug.Log("✅ 客户端连接成功");
                _isConnected = true;
            }
            else
            {
                UnityEngine.Debug.LogError($"❌ 客户端连接失败: {result.ErrorMessage}");
            }
        }
        
        public async void Disconnect()
        {
            if (!_runner.IsRunning) return;
            
            UnityEngine.Debug.Log("断开连接...");
            await _runner.Shutdown();
        }
        
        #endregion
        
        #region 玩家管理
        
        private void SpawnLocalPlayer()
        {
            if (_runner.IsServer && playerPrefab != null)
            {
                var spawnPosition = spawnPoint != null ? spawnPoint.position : Vector3.zero;
                var spawnRotation = spawnPoint != null ? spawnPoint.rotation : Quaternion.identity;
                
                var player = _runner.Spawn(playerPrefab, spawnPosition, spawnRotation, _runner.LocalPlayer);
                
                if (player != null)
                {
                    UnityEngine.Debug.Log($"✅ 玩家生成成功: {player.Id}");
                }
                else
                {
                    UnityEngine.Debug.LogError("❌ 玩家生成失败");
                }
            }
        }
        
        #endregion
        
        #region 网络事件处理

        // 网络事件通过 INetworkRunnerCallbacks 接口处理
        // 参见 ExampleNetworkCallbacks 类的实现

        #endregion
        
        #region GUI 界面
        
        private void DrawNetworkGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            
            GUILayout.Label("自定义网络框架示例", GUI.skin.box);
            GUILayout.Space(10);
            
            // 连接状态
            var statusColor = _isConnected ? Color.green : Color.red;
            var statusText = _isConnected ? "已连接" : "未连接";
            
            GUI.color = statusColor;
            GUILayout.Label($"状态: {statusText}", GUI.skin.box);
            GUI.color = Color.white;
            
            GUILayout.Space(10);
            
            // 网络设置
            GUILayout.Label("网络设置:");
            serverAddress = GUILayout.TextField(serverAddress);
            
            GUILayout.BeginHorizontal();
            GUILayout.Label("端口:", GUILayout.Width(50));
            if (int.TryParse(GUILayout.TextField(serverPort.ToString()), out int port))
            {
                serverPort = port;
            }
            GUILayout.EndHorizontal();
            
            roomName = GUILayout.TextField(roomName);
            
            GUILayout.Space(10);
            
            // 连接按钮
            GUI.enabled = !_runner.IsRunning;
            
            if (GUILayout.Button("启动主机"))
            {
                StartAsHost();
            }
            
            if (GUILayout.Button("连接客户端"))
            {
                StartAsClient();
            }
            
            GUI.enabled = _runner.IsRunning;
            
            if (GUILayout.Button("断开连接"))
            {
                Disconnect();
            }
            
            GUI.enabled = true;
            
            GUILayout.Space(10);
            
            // 网络信息
            if (_runner.IsRunning)
            {
                GUILayout.Label("网络信息:", GUI.skin.box);
                GUILayout.Label($"模式: {_runner.Mode}");
                GUILayout.Label($"Tick: {_runner.Tick}");
                GUILayout.Label($"模拟时间: {_runner.SimulationTime:F2}s");
                GUILayout.Label($"活跃玩家: {_runner.ActivePlayers.Count}");
                
                if (_runner.IsServer)
                {
                    GUILayout.Space(5);
                    if (GUILayout.Button("生成测试对象"))
                    {
                        SpawnLocalPlayer();
                    }
                }
            }
            
            GUILayout.EndArea();
        }
        
        #endregion
    }
    
    /// <summary>
    /// 示例网络回调处理器
    /// </summary>
    public class ExampleNetworkCallbacks : INetworkRunnerCallbacks
    {
        public void OnConnectedToServer(NetworkRunner runner)
        {
            UnityEngine.Debug.Log("🔗 已连接到服务器");
        }
        
        public void OnDisconnectedFromServer(NetworkRunner runner, NetDisconnectReason reason)
        {
            UnityEngine.Debug.Log($"🔗 与服务器断开连接: {reason}");
        }
        
        public void OnPlayerJoined(NetworkRunner runner, PlayerRef player)
        {
            UnityEngine.Debug.Log($"👥 玩家加入房间: {player}");
        }
        
        public void OnPlayerLeft(NetworkRunner runner, PlayerRef player)
        {
            UnityEngine.Debug.Log($"👥 玩家离开房间: {player}");
        }
        
        public void OnInput(NetworkRunner runner, NetworkInput input)
        {
            // 收集本地玩家输入
            var exampleInput = new ExampleNetworkInput();
            
            // 收集键盘输入
            exampleInput.MovementInput = new Vector2(
                Input.GetAxis("Horizontal"),
                Input.GetAxis("Vertical")
            );
            
            exampleInput.JumpPressed = Input.GetKey(KeyCode.Space);
            exampleInput.ActionPressed = Input.GetKey(KeyCode.E);
            
            input.Set(exampleInput);
        }
        
        public void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInput input)
        {
            UnityEngine.Debug.LogWarning($"⚠️ 玩家 {player} 输入丢失");
        }
        
        public void OnSessionListUpdated(NetworkRunner runner, List<SessionInfo> sessionList)
        {
            UnityEngine.Debug.Log($"📋 会话列表更新: {sessionList.Count} 个会话");
        }
    }
    
    /// <summary>
    /// 示例网络输入结构
    /// </summary>
    [System.Serializable]
    public struct ExampleNetworkInput : INetworkInput
    {
        public Vector2 MovementInput;
        public bool JumpPressed;
        public bool ActionPressed;
        public bool InteractPressed;
    }
    
    /// <summary>
    /// 示例网络玩家控制器
    /// </summary>
    public class ExampleNetworkPlayer : NetworkBehaviour
    {
        [Header("移动设置")]
        [SerializeField] private float moveSpeed = 5f;
        [SerializeField] private float jumpForce = 10f;
        
        [Header("网络属性")]
        [Networked] public Vector3 NetworkPosition { get; set; }
        [Networked] public Quaternion NetworkRotation { get; set; }
        [Networked] public bool IsGrounded { get; set; }
        
        private Rigidbody _rigidbody;
        private bool _isInitialized = false;
        
        public override void Spawned()
        {
            base.Spawned();
            
            _rigidbody = GetComponent<Rigidbody>();
            if (_rigidbody == null)
            {
                _rigidbody = gameObject.AddComponent<Rigidbody>();
            }
            
            // 初始化网络位置
            NetworkPosition = transform.position;
            NetworkRotation = transform.rotation;
            
            _isInitialized = true;
            
            UnityEngine.Debug.Log($"🎮 玩家控制器已生成: {Object.Id} (权限: {Object.HasInputAuthority})");
        }
        
        public override void FixedUpdateNetwork()
        {
            if (!_isInitialized) return;
            
            // 处理输入（仅限有输入权限的客户端）
            if (Object.HasInputAuthority && GetInput(out ExampleNetworkInput input))
            {
                ProcessMovement(input);
                ProcessActions(input);
            }
            
            // 更新网络位置（仅限有状态权限的客户端）
            if (Object.HasStateAuthority)
            {
                NetworkPosition = transform.position;
                NetworkRotation = transform.rotation;
            }
        }
        
        public override void Render()
        {
            base.Render();
            
            // 插值到网络位置（非权威客户端）
            if (!Object.HasStateAuthority && _isInitialized)
            {
                transform.position = Vector3.Lerp(transform.position, NetworkPosition, Time.deltaTime * 15f);
                transform.rotation = Quaternion.Lerp(transform.rotation, NetworkRotation, Time.deltaTime * 15f);
            }
        }
        
        private void ProcessMovement(ExampleNetworkInput input)
        {
            if (_rigidbody == null) return;
            
            // 移动处理
            var movement = new Vector3(input.MovementInput.x, 0, input.MovementInput.y);
            movement = movement.normalized * moveSpeed * Runner.DeltaTime;
            
            if (movement.magnitude > 0)
            {
                _rigidbody.MovePosition(transform.position + movement);
                
                // 旋转朝向移动方向
                var lookDirection = movement.normalized;
                if (lookDirection != Vector3.zero)
                {
                    var targetRotation = Quaternion.LookRotation(lookDirection);
                    transform.rotation = Quaternion.Lerp(transform.rotation, targetRotation, Time.deltaTime * 10f);
                }
            }
            
            // 跳跃处理
            if (input.JumpPressed && IsGrounded)
            {
                _rigidbody.AddForce(Vector3.up * jumpForce, ForceMode.Impulse);
                IsGrounded = false;
            }
        }
        
        private void ProcessActions(ExampleNetworkInput input)
        {
            if (input.ActionPressed)
            {
                // 执行动作
                UnityEngine.Debug.Log($"🎯 玩家 {Object.Id} 执行动作");
            }
            
            if (input.InteractPressed)
            {
                // 执行交互
                UnityEngine.Debug.Log($"🤝 玩家 {Object.Id} 执行交互");
            }
        }
        
        private void OnCollisionEnter(Collision collision)
        {
            if (collision.gameObject.CompareTag("Ground"))
            {
                IsGrounded = true;
            }
        }
    }
}
