using UnityEngine;
using UnityEditor;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using System;

namespace CustomNetworking.Tests
{
    /// <summary>
    /// 测试运行器 - 提供手动运行测试的功能
    /// </summary>
    public class TestRunner : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool runOnStart = false;
        [SerializeField] private bool showDetailedResults = true;
        [SerializeField] private bool logToConsole = true;

        [Header("测试结果")]
        [SerializeField] private int totalTests = 0;
        [SerializeField] private int passedTests = 0;
        [SerializeField] private int failedTests = 0;

        private List<TestResult> testResults = new List<TestResult>();

        private void Start()
        {
            if (runOnStart)
            {
                RunAllTests();
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        [ContextMenu("Run All Tests")]
        public void RunAllTests()
        {
            Debug.Log("[TestRunner] 开始运行所有测试...");

            testResults.Clear();
            totalTests = 0;
            passedTests = 0;
            failedTests = 0;

            // 运行NetworkDebugManager测试
            RunNetworkDebugManagerTests();

            // 显示结果
            DisplayResults();
        }

        /// <summary>
        /// 运行NetworkDebugManager测试
        /// </summary>
        private void RunNetworkDebugManagerTests()
        {
            Debug.Log("[TestRunner] 运行NetworkDebugManager测试...");

            try
            {
                // 基础功能测试
                RunBasicFunctionalityTests();

                // UI控制测试
                RunUIControlTests();

                // 日志功能测试
                RunLoggingTests();

                // 性能监控测试
                RunPerformanceTests();

            }
            catch (Exception ex)
            {
                Debug.LogError($"[TestRunner] 测试运行失败: {ex.Message}");
                AddTestResult("TestRunner", "RunNetworkDebugManagerTests", false, ex.Message);
            }
        }

        /// <summary>
        /// 运行基础功能测试
        /// </summary>
        private void RunBasicFunctionalityTests()
        {
            // 测试单例模式
            try
            {
                var instance1 = CustomNetworking.Debug.NetworkDebugManager.Instance;
                var instance2 = CustomNetworking.Debug.NetworkDebugManager.Instance;

                bool passed = instance1 != null && instance1 == instance2;
                AddTestResult("BasicFunctionality", "SingletonPattern", passed,
                    passed ? "单例模式工作正常" : "单例模式失败");
            }
            catch (Exception ex)
            {
                AddTestResult("BasicFunctionality", "SingletonPattern", false, ex.Message);
            }
        }

        /// <summary>
        /// 运行UI控制测试
        /// </summary>
        private void RunUIControlTests()
        {
            try
            {
                var debugManager = CustomNetworking.Debug.NetworkDebugManager.Instance;

                // 测试显示UI
                debugManager.ShowDebugUI();
                bool showPassed = GetDebugUIState(debugManager);
                AddTestResult("UIControl", "ShowDebugUI", showPassed,
                    showPassed ? "显示UI成功" : "显示UI失败");

                // 测试隐藏UI
                debugManager.HideDebugUI();
                bool hidePassed = !GetDebugUIState(debugManager);
                AddTestResult("UIControl", "HideDebugUI", hidePassed,
                    hidePassed ? "隐藏UI成功" : "隐藏UI失败");

                // 测试切换UI
                bool initialState = GetDebugUIState(debugManager);
                debugManager.ToggleDebugUI();
                bool togglePassed = GetDebugUIState(debugManager) != initialState;
                AddTestResult("UIControl", "ToggleDebugUI", togglePassed,
                    togglePassed ? "切换UI成功" : "切换UI失败");

            }
            catch (Exception ex)
            {
                AddTestResult("UIControl", "UIControlTests", false, ex.Message);
            }
        }

        /// <summary>
        /// 运行日志功能测试
        /// </summary>
        private void RunLoggingTests()
        {
            try
            {
                var debugManager = CustomNetworking.Debug.NetworkDebugManager.Instance;

                // 清除现有日志
                debugManager.ClearLogs();
                int initialCount = GetLogEntryCount(debugManager);

                // 测试添加日志
                debugManager.LogDebug("Test message", CustomNetworking.Debug.DebugLogType.Info);
                int newCount = GetLogEntryCount(debugManager);

                bool logPassed = newCount > initialCount;
                AddTestResult("Logging", "LogDebug", logPassed,
                    logPassed ? "日志记录成功" : "日志记录失败");

                // 测试清除日志
                debugManager.ClearLogs();
                int clearedCount = GetLogEntryCount(debugManager);

                // 注意：ClearLogs会添加一条"日志已清除"的消息
                bool clearPassed = clearedCount <= 1;
                AddTestResult("Logging", "ClearLogs", clearPassed,
                    clearPassed ? "清除日志成功" : "清除日志失败");

            }
            catch (Exception ex)
            {
                AddTestResult("Logging", "LoggingTests", false, ex.Message);
            }
        }

        /// <summary>
        /// 运行性能监控测试
        /// </summary>
        private void RunPerformanceTests()
        {
            try
            {
                var debugManager = CustomNetworking.Debug.NetworkDebugManager.Instance;

                // 测试获取调试统计
                var stats = debugManager.GetDebugStats();
                bool statsPassed = stats.CurrentFPS >= 0 && stats.TotalLogEntries >= 0;
                AddTestResult("Performance", "GetDebugStats", statsPassed,
                    statsPassed ? "获取统计信息成功" : "获取统计信息失败");

            }
            catch (Exception ex)
            {
                AddTestResult("Performance", "PerformanceTests", false, ex.Message);
            }
        }

        /// <summary>
        /// 添加测试结果
        /// </summary>
        private void AddTestResult(string category, string testName, bool passed, string message)
        {
            var result = new TestResult
            {
                Category = category,
                TestName = testName,
                Passed = passed,
                Message = message,
                Timestamp = DateTime.Now
            };

            testResults.Add(result);
            totalTests++;

            if (passed)
            {
                passedTests++;
                if (logToConsole)
                    Debug.Log($"[PASS] {category}.{testName}: {message}");
            }
            else
            {
                failedTests++;
                if (logToConsole)
                    Debug.LogError($"[FAIL] {category}.{testName}: {message}");
            }
        }

        /// <summary>
        /// 显示测试结果
        /// </summary>
        private void DisplayResults()
        {
            Debug.Log($"[TestRunner] 测试完成! 总计: {totalTests}, 通过: {passedTests}, 失败: {failedTests}");

            if (showDetailedResults)
            {
                Debug.Log("=== 详细测试结果 ===");
                foreach (var result in testResults)
                {
                    string status = result.Passed ? "PASS" : "FAIL";
                    Debug.Log($"[{status}] {result.Category}.{result.TestName}: {result.Message}");
                }
            }

            float successRate = totalTests > 0 ? (float)passedTests / totalTests * 100f : 0f;
            Debug.Log($"[TestRunner] 成功率: {successRate:F1}%");

            // 生成测试报告
            GenerateTestReport();
        }

        /// <summary>
        /// 生成测试报告
        /// </summary>
        [ContextMenu("Generate Test Report")]
        public void GenerateTestReport()
        {
            try
            {
                string reportPath = Application.dataPath + "/TestReports/";
                if (!System.IO.Directory.Exists(reportPath))
                {
                    System.IO.Directory.CreateDirectory(reportPath);
                }

                string fileName = $"TestReport_{DateTime.Now:yyyyMMdd_HHmmss}.html";
                string fullPath = reportPath + fileName;

                var report = GenerateHTMLReport();
                System.IO.File.WriteAllText(fullPath, report);

                Debug.Log($"[TestRunner] 测试报告已生成: {fullPath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[TestRunner] 生成测试报告失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成HTML测试报告
        /// </summary>
        private string GenerateHTMLReport()
        {
            var html = new System.Text.StringBuilder();

            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine("<title>NetworkDebugManager 测试报告</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: Arial, sans-serif; margin: 20px; }");
            html.AppendLine(".header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }");
            html.AppendLine(".summary { margin: 20px 0; }");
            html.AppendLine(".pass { color: green; }");
            html.AppendLine(".fail { color: red; }");
            html.AppendLine("table { border-collapse: collapse; width: 100%; }");
            html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
            html.AppendLine("th { background-color: #f2f2f2; }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");

            // 标题
            html.AppendLine("<div class='header'>");
            html.AppendLine("<h1>NetworkDebugManager 测试报告</h1>");
            html.AppendLine($"<p>生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>");
            html.AppendLine("</div>");

            // 摘要
            html.AppendLine("<div class='summary'>");
            html.AppendLine("<h2>测试摘要</h2>");
            html.AppendLine($"<p>总测试数: <strong>{totalTests}</strong></p>");
            html.AppendLine($"<p>通过测试: <strong class='pass'>{passedTests}</strong></p>");
            html.AppendLine($"<p>失败测试: <strong class='fail'>{failedTests}</strong></p>");

            float successRate = totalTests > 0 ? (float)passedTests / totalTests * 100f : 0f;
            html.AppendLine($"<p>成功率: <strong>{successRate:F1}%</strong></p>");
            html.AppendLine("</div>");

            // 详细结果
            html.AppendLine("<h2>详细测试结果</h2>");
            html.AppendLine("<table>");
            html.AppendLine("<tr><th>类别</th><th>测试名称</th><th>状态</th><th>消息</th><th>时间</th></tr>");

            foreach (var result in testResults)
            {
                string statusClass = result.Passed ? "pass" : "fail";
                string status = result.Passed ? "通过" : "失败";

                html.AppendLine("<tr>");
                html.AppendLine($"<td>{result.Category}</td>");
                html.AppendLine($"<td>{result.TestName}</td>");
                html.AppendLine($"<td class='{statusClass}'>{status}</td>");
                html.AppendLine($"<td>{result.Message}</td>");
                html.AppendLine($"<td>{result.Timestamp:HH:mm:ss}</td>");
                html.AppendLine("</tr>");
            }

            html.AppendLine("</table>");
            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }

        #region 辅助方法

        /// <summary>
        /// 获取调试UI状态
        /// </summary>
        private bool GetDebugUIState(CustomNetworking.Debug.NetworkDebugManager debugManager)
        {
            var field = typeof(CustomNetworking.Debug.NetworkDebugManager).GetField("_showDebugUI",
                BindingFlags.NonPublic | BindingFlags.Instance);
            return (bool)field.GetValue(debugManager);
        }

        /// <summary>
        /// 获取日志条目数量
        /// </summary>
        private int GetLogEntryCount(CustomNetworking.Debug.NetworkDebugManager debugManager)
        {
            var field = typeof(CustomNetworking.Debug.NetworkDebugManager).GetField("_logEntries",
                BindingFlags.NonPublic | BindingFlags.Instance);
            var logEntries = field.GetValue(debugManager) as System.Collections.IList;
            return logEntries?.Count ?? 0;
        }

        #endregion

        /// <summary>
        /// 测试结果数据结构
        /// </summary>
        [System.Serializable]
        public struct TestResult
        {
            public string Category;
            public string TestName;
            public bool Passed;
            public string Message;
            public DateTime Timestamp;
        }
    }
}
