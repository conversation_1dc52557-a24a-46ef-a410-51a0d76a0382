using System;
using System.Collections.Generic;

namespace CustomNetworking.Core
{
    /// <summary>
    /// 网络运行器回调接口
    /// </summary>
    public interface INetworkRunnerCallbacks
    {
        void OnPlayerJoined(NetworkRunner runner, PlayerRef player) { }
        void OnPlayerLeft(NetworkRunner runner, PlayerRef player) { }
        void OnInput(NetworkRunner runner, NetworkInput input) { }
        void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInput input) { }
        void OnShutdown(NetworkRunner runner, NetDisconnectReason shutdownReason) { }
        void OnConnectedToServer(NetworkRunner runner) { }
        void OnDisconnectedFromServer(NetworkRunner runner, NetDisconnectReason reason) { }
        void OnConnectRequest(NetworkRunner runner, NetworkRunnerCallbackArgs.ConnectRequest request, byte[] token) { }
        void OnConnectFailed(NetworkRunner runner, NetAddress remoteAddress, NetConnectFailedReason reason) { }
        void OnUserSimulationMessage(NetworkRunner runner, SimulationMessagePtr message) { }
        void OnSessionListUpdated(NetworkRunner runner, List<SessionInfo> sessionList) { }
        void OnCustomAuthenticationResponse(NetworkRunner runner, Dictionary<string, object> data) { }
        void OnHostMigration(NetworkRunner runner, HostMigrationToken hostMigrationToken) { }
        void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data) { }
        void OnReliableDataProgress(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress) { }
        void OnSceneLoadDone(NetworkRunner runner) { }
        void OnSceneLoadStart(NetworkRunner runner) { }
        void OnObjectExitAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
        void OnObjectEnterAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
    }
    
    /// <summary>
    /// 网络运行器回调参数
    /// </summary>
    public static class NetworkRunnerCallbackArgs
    {
        public class ConnectRequest
        {
            public void Accept() { }
            public void Refuse() { }
        }
    }
    
    /// <summary>
    /// 连接失败原因
    /// </summary>
    public enum NetConnectFailedReason
    {
        None,
        Timeout,
        ServerFull,
        InvalidData,
        Refused
    }
    
    /// <summary>
    /// 模拟消息指针
    /// </summary>
    public struct SimulationMessagePtr
    {
        // 占位符结构
    }
    
    /// <summary>
    /// 主机迁移令牌
    /// </summary>
    public struct HostMigrationToken
    {
        // 占位符结构
    }
    
    /// <summary>
    /// 可靠数据键
    /// </summary>
    public struct ReliableKey
    {
        // 占位符结构
    }
    
    /// <summary>
    /// 关闭原因
    /// </summary>
    public enum ShutdownReason
    {
        Ok,
        Error,
        IncompatibleConfiguration,
        ServerInRoom,
        DisconnectedByPluginLogic,
        GameClosed,
        GameNotFound,
        MaxCcuReached,
        InvalidRegion,
        CustomAuthenticationFailed,
        AuthenticationTicketExpired,
        PluginReportedError,
        MasterClientSwitched,
        ServerTimeout,
        ClientTimeout
    }
}
