using System;
using System.Collections.Generic;
using UnityEngine;

namespace CustomNetworking.Core
{
    /// <summary>
    /// 自定义网络对象 - 替代 Photon Fusion NetworkObject
    /// </summary>
    public class NetworkObject : MonoBehaviour
    {
        #region 属性
        
        public NetworkId Id { get; private set; }
        public PlayerRef InputAuthority { get; set; }
        public PlayerRef StateAuthority { get; set; }
        public NetworkRunner Runner { get; private set; }
        
        // 权限检查
        public bool HasStateAuthority => Runner != null && StateAuthority.Equals(Runner.LocalPlayer);
        public bool HasInputAuthority => Runner != null && InputAuthority.Equals(Runner.LocalPlayer);
        
        // 网络行为管理
        private List<NetworkBehaviour> _networkBehaviours = new List<NetworkBehaviour>();
        
        // 状态管理
        public bool IsSpawned { get; private set; }
        
        #endregion
        
        #region 事件
        
        public event Action<NetworkObject> OnSpawned;
        public event Action<NetworkObject> OnDespawned;
        public event Action<NetworkObject, PlayerRef, PlayerRef> OnAuthorityChanged;
        
        #endregion
        
        #region 生命周期
        
        private void Awake()
        {
            // 收集所有网络行为组件
            GetComponents(_networkBehaviours);
        }
        
        #endregion
        
        #region 网络生命周期
        
        /// <summary>
        /// 初始化网络对象
        /// </summary>
        public void Initialize(NetworkRunner runner, NetworkId id, PlayerRef inputAuthority = default)
        {
            Runner = runner;
            Id = id;
            InputAuthority = inputAuthority;
            StateAuthority = runner.IsServer ? runner.LocalPlayer : PlayerRef.None;
            
            IsSpawned = true;
            
            // 通知所有网络行为
            foreach (var behaviour in _networkBehaviours)
            {
                behaviour.Spawned();
            }
            
            OnSpawned?.Invoke(this);
            
            UnityEngine.Debug.Log($"NetworkObject {Id} spawned with InputAuthority: {InputAuthority}");
        }
        
        /// <summary>
        /// 销毁网络对象
        /// </summary>
        public void Despawn()
        {
            if (!IsSpawned)
                return;
                
            IsSpawned = false;
            
            // 通知所有网络行为
            foreach (var behaviour in _networkBehaviours)
            {
                behaviour.Despawned();
            }
            
            OnDespawned?.Invoke(this);
            
            UnityEngine.Debug.Log($"NetworkObject {Id} despawned");
        }
        
        /// <summary>
        /// 固定网络更新
        /// </summary>
        public void FixedUpdateNetwork()
        {
            if (!IsSpawned)
                return;
                
            foreach (var behaviour in _networkBehaviours)
            {
                behaviour.FixedUpdateNetwork();
            }
        }
        
        /// <summary>
        /// 渲染更新
        /// </summary>
        public void Render()
        {
            if (!IsSpawned)
                return;
                
            foreach (var behaviour in _networkBehaviours)
            {
                behaviour.Render();
            }
        }
        
        #endregion
        
        #region 网络行为管理
        
        /// <summary>
        /// 注册网络行为
        /// </summary>
        public void RegisterBehaviour(NetworkBehaviour behaviour)
        {
            if (!_networkBehaviours.Contains(behaviour))
            {
                _networkBehaviours.Add(behaviour);
            }
        }
        
        /// <summary>
        /// 取消注册网络行为
        /// </summary>
        public void UnregisterBehaviour(NetworkBehaviour behaviour)
        {
            _networkBehaviours.Remove(behaviour);
        }
        
        /// <summary>
        /// 获取网络行为
        /// </summary>
        public T GetBehaviour<T>() where T : NetworkBehaviour
        {
            foreach (var behaviour in _networkBehaviours)
            {
                if (behaviour is T)
                    return behaviour as T;
            }
            return null;
        }
        
        #endregion
        
        #region 权限管理
        
        /// <summary>
        /// 设置输入权限
        /// </summary>
        public void SetInputAuthority(PlayerRef player)
        {
            var oldAuthority = InputAuthority;
            InputAuthority = player;
            
            OnAuthorityChanged?.Invoke(this, oldAuthority, player);
            
            UnityEngine.Debug.Log($"NetworkObject {Id} InputAuthority changed from {oldAuthority} to {player}");
        }
        
        /// <summary>
        /// 设置状态权限
        /// </summary>
        public void SetStateAuthority(PlayerRef player)
        {
            var oldAuthority = StateAuthority;
            StateAuthority = player;
            
            OnAuthorityChanged?.Invoke(this, oldAuthority, player);
            
            UnityEngine.Debug.Log($"NetworkObject {Id} StateAuthority changed from {oldAuthority} to {player}");
        }
        
        /// <summary>
        /// 释放状态权限
        /// </summary>
        public void ReleaseStateAuthority()
        {
            SetStateAuthority(PlayerRef.None);
        }
        
        /// <summary>
        /// 请求状态权限
        /// </summary>
        public void RequestStateAuthority()
        {
            if (Runner != null && Runner.IsClient)
            {
                SetStateAuthority(Runner.LocalPlayer);
            }
        }
        
        #endregion
        
        #region Unity生命周期
        
        private void OnDestroy()
        {
            if (IsSpawned)
            {
                Despawn();
            }
        }
        
        #endregion
        
        #region 调试
        
        public override string ToString()
        {
            return $"NetworkObject(Id: {Id}, InputAuth: {InputAuthority}, StateAuth: {StateAuthority})";
        }
        
        #endregion
    }
    
    /// <summary>
    /// 网络预制体引用
    /// </summary>
    [Serializable]
    public struct NetworkPrefabRef
    {
        [SerializeField] private GameObject _prefab;
        
        public GameObject Prefab => _prefab;
        
        public NetworkPrefabRef(GameObject prefab)
        {
            _prefab = prefab;
        }
        
        public static implicit operator GameObject(NetworkPrefabRef prefabRef)
        {
            return prefabRef._prefab;
        }
        
        public static implicit operator NetworkPrefabRef(GameObject prefab)
        {
            return new NetworkPrefabRef(prefab);
        }
    }
}
