using System.Threading.Tasks;
using CustomNetworking.ErrorHandling;

namespace CustomNetworking.Core
{
    /// <summary>
    /// 网络管理器接口 - 定义网络管理器的基本功能
    /// 用于避免循环依赖问题
    /// </summary>
    public interface INetworkManager
    {
        /// <summary>
        /// 获取当前连接状态
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 获取正在连接状态
        /// </summary>
        bool IsConnecting { get; }

        /// <summary>
        /// 发送心跳
        /// </summary>
        Task SendHeartbeat();

        /// <summary>
        /// 异步重连
        /// </summary>
        Task ReconnectAsync();

        /// <summary>
        /// 请求重新同步
        /// </summary>
        void RequestResync();

        /// <summary>
        /// 请求完全重新同步
        /// </summary>
        Task RequestFullResync();

        /// <summary>
        /// 获取网络诊断信息
        /// </summary>
        CustomNetworking.ErrorHandling.NetworkDiagnostics GetNetworkDiagnostics();
    }
}
