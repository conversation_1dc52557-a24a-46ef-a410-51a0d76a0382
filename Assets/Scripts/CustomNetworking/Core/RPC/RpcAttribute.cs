using System;

namespace CustomNetworking.Core.RPC
{
    /// <summary>
    /// RPC 方法标记属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class RpcAttribute : Attribute
    {
        /// <summary>
        /// RPC 调用目标
        /// </summary>
        public RpcTargets Target { get; set; } = RpcTargets.All;
        
        /// <summary>
        /// 是否需要可靠传输
        /// </summary>
        public bool Reliable { get; set; } = true;
        
        /// <summary>
        /// 是否包含发送者
        /// </summary>
        public bool IncludeSender { get; set; } = false;
        
        /// <summary>
        /// RPC 优先级
        /// </summary>
        public RpcPriority Priority { get; set; } = RpcPriority.Normal;
        
        public RpcAttribute()
        {
        }
        
        public RpcAttribute(RpcTargets target)
        {
            Target = target;
        }
        
        public RpcAttribute(RpcTargets target, bool reliable)
        {
            Target = target;
            Reliable = reliable;
        }
    }
    
    /// <summary>
    /// RPC 调用目标枚举
    /// </summary>
    public enum RpcTargets
    {
        /// <summary>
        /// 发送给所有客户端
        /// </summary>
        All,
        
        /// <summary>
        /// 发送给除发送者外的所有客户端
        /// </summary>
        Others,
        
        /// <summary>
        /// 发送给服务器
        /// </summary>
        Server,
        
        /// <summary>
        /// 发送给指定玩家
        /// </summary>
        Player,
        
        /// <summary>
        /// 发送给有状态权限的客户端
        /// </summary>
        StateAuthority,
        
        /// <summary>
        /// 发送给有输入权限的客户端
        /// </summary>
        InputAuthority
    }
    
    /// <summary>
    /// RPC 优先级枚举
    /// </summary>
    public enum RpcPriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        Low = 0,
        
        /// <summary>
        /// 普通优先级
        /// </summary>
        Normal = 1,
        
        /// <summary>
        /// 高优先级
        /// </summary>
        High = 2,
        
        /// <summary>
        /// 紧急优先级
        /// </summary>
        Critical = 3
    }
    
    /// <summary>
    /// RPC 信息类 - 存储 RPC 方法的元数据
    /// </summary>
    public class RpcInfo
    {
        public string MethodName { get; set; }
        public RpcTargets Target { get; set; }
        public bool Reliable { get; set; }
        public bool IncludeSender { get; set; }
        public RpcPriority Priority { get; set; }
        public Type[] ParameterTypes { get; set; }
        public int MethodHash { get; set; }
        
        public RpcInfo(string methodName, RpcAttribute attribute, Type[] parameterTypes)
        {
            MethodName = methodName;
            Target = attribute.Target;
            Reliable = attribute.Reliable;
            IncludeSender = attribute.IncludeSender;
            Priority = attribute.Priority;
            ParameterTypes = parameterTypes;
            MethodHash = GenerateMethodHash(methodName, parameterTypes);
        }
        
        private int GenerateMethodHash(string methodName, Type[] parameterTypes)
        {
            int hash = methodName.GetHashCode();
            foreach (var type in parameterTypes)
            {
                hash = hash * 31 + type.GetHashCode();
            }
            return hash;
        }
    }
}
