using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine;

namespace CustomNetworking.Core.RPC
{
    /// <summary>
    /// RPC 消息数据结构
    /// </summary>
    [Serializable]
    public class RpcMessage : NetworkData, IPriorityQueueItem
    {
        // Sender 属性继承自 NetworkData

        /// <summary>
        /// 目标网络对象ID
        /// </summary>
        public NetworkId TargetObjectId { get; set; }

        /// <summary>
        /// 方法哈希值
        /// </summary>
        public int MethodHash { get; set; }

        /// <summary>
        /// 方法名称（用于调试）
        /// </summary>
        public string MethodName { get; set; }

        /// <summary>
        /// RPC 目标
        /// </summary>
        public RpcTargets Target { get; set; }

        /// <summary>
        /// 是否可靠传输
        /// </summary>
        public bool Reliable { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public RpcPriority Priority { get; set; }

        /// <summary>
        /// 是否包含发送者
        /// </summary>
        public bool IncludeSender { get; set; }

        /// <summary>
        /// 序列化的参数数据
        /// </summary>
        public byte[] ParameterData { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public new float Timestamp { get; set; }

        /// <summary>
        /// 目标玩家（当 Target 为 Player 时使用）
        /// </summary>
        public PlayerRef TargetPlayer { get; set; }

        public RpcMessage()
        {
            Timestamp = Time.time;
        }

        public RpcMessage(PlayerRef sender, NetworkId targetObjectId, int methodHash, string methodName,
                         RpcTargets target, bool reliable, RpcPriority priority, byte[] parameterData,
                         PlayerRef targetPlayer = default, bool includeSender = false)
        {
            Sender = sender;
            TargetObjectId = targetObjectId;
            MethodHash = methodHash;
            MethodName = methodName;
            Target = target;
            Reliable = reliable;
            Priority = priority;
            ParameterData = parameterData;
            TargetPlayer = targetPlayer;
            IncludeSender = includeSender;
            Timestamp = Time.time;
        }

        /// <summary>
        /// 序列化消息到字节数组
        /// </summary>
        public byte[] Serialize()
        {
            using (var stream = new MemoryStream())
            using (var writer = new BinaryWriter(stream))
            {
                // 写入基本信息
                writer.Write(Sender.PlayerId);
                writer.Write(TargetObjectId.Raw);
                writer.Write(MethodHash);
                writer.Write(MethodName ?? string.Empty);
                writer.Write((int)Target);
                writer.Write(Reliable);
                writer.Write((int)Priority);
                writer.Write(IncludeSender);
                writer.Write(Timestamp);
                writer.Write(TargetPlayer.PlayerId);

                // 写入参数数据
                if (ParameterData != null && ParameterData.Length > 0)
                {
                    writer.Write(ParameterData.Length);
                    writer.Write(ParameterData);
                }
                else
                {
                    writer.Write(0);
                }

                return stream.ToArray();
            }
        }

        /// <summary>
        /// 从字节数组反序列化消息
        /// </summary>
        public static RpcMessage Deserialize(byte[] data)
        {
            using (var stream = new MemoryStream(data))
            using (var reader = new BinaryReader(stream))
            {
                var message = new RpcMessage();

                // 读取基本信息
                message.Sender = new PlayerRef(reader.ReadInt32());
                message.TargetObjectId = new NetworkId(reader.ReadUInt32());
                message.MethodHash = reader.ReadInt32();
                message.MethodName = reader.ReadString();
                message.Target = (RpcTargets)reader.ReadInt32();
                message.Reliable = reader.ReadBoolean();
                message.Priority = (RpcPriority)reader.ReadInt32();
                message.IncludeSender = reader.ReadBoolean();
                message.Timestamp = reader.ReadSingle();
                message.TargetPlayer = new PlayerRef(reader.ReadInt32());

                // 读取参数数据
                int parameterDataLength = reader.ReadInt32();
                if (parameterDataLength > 0)
                {
                    message.ParameterData = reader.ReadBytes(parameterDataLength);
                }

                return message;
            }
        }

        public override string ToString()
        {
            return $"RpcMessage[{MethodName}] From:{Sender} To:{Target} Object:{TargetObjectId} Reliable:{Reliable}";
        }

        #region IPriorityQueueItem实现

        /// <summary>
        /// 比较优先级 - 用于优先级队列排序
        /// 优先级高的消息排在前面，相同优先级按时间戳排序
        /// </summary>
        public int CompareTo(IPriorityQueueItem other)
        {
            if (other == null) return -1;

            // 首先按优先级比较（数值越大优先级越高，但队列中优先级高的要排在前面）
            int priorityComparison = other.Priority.CompareTo(this.Priority);
            if (priorityComparison != 0)
                return priorityComparison;

            // 优先级相同时，按时间戳比较（早的消息优先）
            return this.Timestamp.CompareTo(other.Timestamp);
        }

        #endregion
    }

    /// <summary>
    /// RPC 参数序列化器
    /// </summary>
    public static class RpcParameterSerializer
    {
        /// <summary>
        /// 序列化参数数组
        /// </summary>
        public static byte[] SerializeParameters(object[] parameters)
        {
            if (parameters == null || parameters.Length == 0)
                return new byte[0];

            using (var stream = new MemoryStream())
            using (var writer = new BinaryWriter(stream))
            {
                writer.Write(parameters.Length);

                foreach (var param in parameters)
                {
                    SerializeParameter(writer, param);
                }

                return stream.ToArray();
            }
        }

        /// <summary>
        /// 反序列化参数数组
        /// </summary>
        public static object[] DeserializeParameters(byte[] data, Type[] parameterTypes)
        {
            if (data == null || data.Length == 0 || parameterTypes == null || parameterTypes.Length == 0)
                return new object[0];

            using (var stream = new MemoryStream(data))
            using (var reader = new BinaryReader(stream))
            {
                int paramCount = reader.ReadInt32();
                var parameters = new object[paramCount];

                for (int i = 0; i < paramCount && i < parameterTypes.Length; i++)
                {
                    parameters[i] = DeserializeParameter(reader, parameterTypes[i]);
                }

                return parameters;
            }
        }

        private static void SerializeParameter(BinaryWriter writer, object parameter)
        {
            if (parameter == null)
            {
                writer.Write((byte)0); // null marker
                return;
            }

            writer.Write((byte)1); // non-null marker

            Type type = parameter.GetType();

            // 基本类型
            if (type == typeof(bool))
                writer.Write((bool)parameter);
            else if (type == typeof(byte))
                writer.Write((byte)parameter);
            else if (type == typeof(int))
                writer.Write((int)parameter);
            else if (type == typeof(float))
                writer.Write((float)parameter);
            else if (type == typeof(double))
                writer.Write((double)parameter);
            else if (type == typeof(string))
                writer.Write((string)parameter);
            else if (type == typeof(Vector3))
            {
                var v = (Vector3)parameter;
                writer.Write(v.x);
                writer.Write(v.y);
                writer.Write(v.z);
            }
            else if (type == typeof(Vector2))
            {
                var v = (Vector2)parameter;
                writer.Write(v.x);
                writer.Write(v.y);
            }
            else if (type == typeof(Quaternion))
            {
                var q = (Quaternion)parameter;
                writer.Write(q.x);
                writer.Write(q.y);
                writer.Write(q.z);
                writer.Write(q.w);
            }
            else if (type == typeof(PlayerRef))
            {
                writer.Write(((PlayerRef)parameter).PlayerId);
            }
            else
            {
                // 对于复杂类型，使用 JSON 序列化
                string json = JsonUtility.ToJson(parameter);
                writer.Write(json);
            }
        }

        private static object DeserializeParameter(BinaryReader reader, Type type)
        {
            byte nullMarker = reader.ReadByte();
            if (nullMarker == 0)
                return null;

            // 基本类型
            if (type == typeof(bool))
                return reader.ReadBoolean();
            else if (type == typeof(byte))
                return reader.ReadByte();
            else if (type == typeof(int))
                return reader.ReadInt32();
            else if (type == typeof(float))
                return reader.ReadSingle();
            else if (type == typeof(double))
                return reader.ReadDouble();
            else if (type == typeof(string))
                return reader.ReadString();
            else if (type == typeof(Vector3))
                return new Vector3(reader.ReadSingle(), reader.ReadSingle(), reader.ReadSingle());
            else if (type == typeof(Vector2))
                return new Vector2(reader.ReadSingle(), reader.ReadSingle());
            else if (type == typeof(Quaternion))
                return new Quaternion(reader.ReadSingle(), reader.ReadSingle(), reader.ReadSingle(), reader.ReadSingle());
            else if (type == typeof(PlayerRef))
                return new PlayerRef(reader.ReadInt32());
            else
            {
                // 对于复杂类型，使用 JSON 反序列化
                string json = reader.ReadString();
                return JsonUtility.FromJson(json, type);
            }
        }
    }
}
