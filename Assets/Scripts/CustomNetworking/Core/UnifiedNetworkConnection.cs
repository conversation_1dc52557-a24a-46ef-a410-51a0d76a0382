using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using System.IO;

namespace CustomNetworking.Core
{
    /// <summary>
    /// 统一网络连接实现
    /// 使用INetworkTransport接口，支持多种传输协议
    /// </summary>
    public class UnifiedNetworkConnection : NetworkConnection
    {
        #region 私有字段

        private INetworkTransport _transport;
        private Dictionary<PlayerRef, string> _playerToClientId = new Dictionary<PlayerRef, string>();
        private Dictionary<string, PlayerRef> _clientIdToPlayer = new Dictionary<string, PlayerRef>();
        private int _nextPlayerId = 1;
        private bool _isServer = false;

        // 数据序列化
        private NetworkDataSerializer _serializer = new NetworkDataSerializer();

        #endregion

        #region 构造函数

        public UnifiedNetworkConnection(INetworkTransport transport)
        {
            _transport = transport ?? throw new ArgumentNullException(nameof(transport));

            // 订阅传输层事件
            _transport.OnConnected += HandleTransportConnected;
            _transport.OnDisconnected += HandleTransportDisconnected;
            _transport.OnDataReceived += HandleTransportDataReceived;
            _transport.OnError += HandleTransportError;
        }

        #endregion

        #region NetworkConnection实现

        public override async Task StartServer(NetAddress address, int port)
        {
            if (_isConnected)
            {
                UnityEngine.Debug.LogWarning("[UnifiedNetworkConnection] Already connected");
                return;
            }

            _isServer = true;

            try
            {
                bool success = await _transport.StartServerAsync(address.Address, port);
                if (success)
                {
                    _isConnected = true;
                    UnityEngine.Debug.Log($"[UnifiedNetworkConnection] Server started on {address}:{port}");
                }
                else
                {
                    throw new Exception("Failed to start server");
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[UnifiedNetworkConnection] Start server failed: {ex.Message}");
                throw;
            }
        }

        public override async Task ConnectToServer(NetAddress address, int port)
        {
            if (_isConnected)
            {
                UnityEngine.Debug.LogWarning("[UnifiedNetworkConnection] Already connected");
                return;
            }

            _isServer = false;

            try
            {
                bool success = await _transport.ConnectAsync(address.Address, port);
                if (success)
                {
                    _isConnected = true;

                    // 客户端连接成功，创建本地玩家
                    var localPlayer = new PlayerRef(_nextPlayerId++);
                    _playerToClientId[localPlayer] = "local";
                    _clientIdToPlayer["local"] = localPlayer;
                    NotifyPlayerConnected(localPlayer);

                    UnityEngine.Debug.Log($"[UnifiedNetworkConnection] Connected to server at {address}:{port}");
                }
                else
                {
                    throw new Exception("Failed to connect to server");
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[UnifiedNetworkConnection] Connect failed: {ex.Message}");
                throw;
            }
        }

        public override async Task Disconnect()
        {
            if (!_isConnected)
                return;

            try
            {
                // 通知所有玩家断开连接
                var playersToDisconnect = new List<PlayerRef>(_connectedPlayers);
                foreach (var player in playersToDisconnect)
                {
                    NotifyPlayerDisconnected(player);
                }

                // 清理映射
                _playerToClientId.Clear();
                _clientIdToPlayer.Clear();
                _nextPlayerId = 1;

                await _transport.DisconnectAsync("User requested");

                _isConnected = false;
                UnityEngine.Debug.Log("[UnifiedNetworkConnection] Disconnected");
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[UnifiedNetworkConnection] Disconnect error: {ex.Message}");
            }
        }

        public override void SendToAll(NetworkData data)
        {
            if (!_isConnected)
            {
                UnityEngine.Debug.LogWarning("[UnifiedNetworkConnection] Cannot send: not connected");
                return;
            }

            try
            {
                byte[] serializedData = _serializer.Serialize(data);
                _transport.Broadcast(serializedData, reliable: true);
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[UnifiedNetworkConnection] Send to all failed: {ex.Message}");
            }
        }

        public override void SendToPlayer(PlayerRef player, NetworkData data)
        {
            if (!_isConnected)
            {
                UnityEngine.Debug.LogWarning("[UnifiedNetworkConnection] Cannot send: not connected");
                return;
            }

            if (!_playerToClientId.TryGetValue(player, out string clientId))
            {
                UnityEngine.Debug.LogWarning($"[UnifiedNetworkConnection] Player {player} not found");
                return;
            }

            try
            {
                byte[] serializedData = _serializer.Serialize(data);
                _transport.Send(serializedData, clientId, reliable: true);
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[UnifiedNetworkConnection] Send to player failed: {ex.Message}");
            }
        }

        #endregion

        #region 传输层事件处理

        private void HandleTransportConnected()
        {
            UnityEngine.Debug.Log("[UnifiedNetworkConnection] Transport connected");
        }

        private void HandleTransportDisconnected(string reason)
        {
            UnityEngine.Debug.Log($"[UnifiedNetworkConnection] Transport disconnected: {reason}");
            _isConnected = false;

            // 通知所有玩家断开连接
            var playersToDisconnect = new List<PlayerRef>(_connectedPlayers);
            foreach (var player in playersToDisconnect)
            {
                NotifyPlayerDisconnected(player);
            }
        }

        private void HandleTransportDataReceived(byte[] data, string senderId)
        {
            try
            {
                NetworkData networkData = _serializer.Deserialize(data);

                // 如果是服务器，处理客户端连接
                if (_isServer && !_clientIdToPlayer.ContainsKey(senderId))
                {
                    var newPlayer = new PlayerRef(_nextPlayerId++);
                    _playerToClientId[newPlayer] = senderId;
                    _clientIdToPlayer[senderId] = newPlayer;
                    NotifyPlayerConnected(newPlayer);
                }

                // 设置发送者
                if (_clientIdToPlayer.TryGetValue(senderId, out PlayerRef sender))
                {
                    networkData.Sender = sender;
                }

                // 使用基类的受保护方法来触发事件
                NotifyDataReceived(networkData);
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[UnifiedNetworkConnection] Data processing failed: {ex.Message}");
            }
        }

        private void HandleTransportError(string error)
        {
            UnityEngine.Debug.LogError($"[UnifiedNetworkConnection] Transport error: {error}");
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取传输层统计信息
        /// </summary>
        public NetworkTransportStats GetTransportStats()
        {
            return _transport?.GetStats() ?? default;
        }

        /// <summary>
        /// 断开指定玩家连接
        /// </summary>
        public void DisconnectPlayer(PlayerRef player, string reason = "Server requested")
        {
            if (!_isServer)
            {
                UnityEngine.Debug.LogWarning("[UnifiedNetworkConnection] Only server can disconnect players");
                return;
            }

            if (_playerToClientId.TryGetValue(player, out string clientId))
            {
                _transport.DisconnectClient(clientId, reason);

                // 清理映射
                _playerToClientId.Remove(player);
                _clientIdToPlayer.Remove(clientId);

                NotifyPlayerDisconnected(player);
            }
        }

        #endregion

        #region 清理

        ~UnifiedNetworkConnection()
        {
            if (_transport != null)
            {
                _transport.OnConnected -= HandleTransportConnected;
                _transport.OnDisconnected -= HandleTransportDisconnected;
                _transport.OnDataReceived -= HandleTransportDataReceived;
                _transport.OnError -= HandleTransportError;
            }
        }

        #endregion
    }

    /// <summary>
    /// 网络数据序列化器
    /// 处理NetworkData的序列化和反序列化
    /// </summary>
    public class NetworkDataSerializer
    {
        /// <summary>
        /// 序列化网络数据
        /// </summary>
        public byte[] Serialize(NetworkData data)
        {
            using (var stream = new MemoryStream())
            using (var writer = new BinaryWriter(stream))
            {
                // 写入数据类型
                writer.Write(data.GetType().AssemblyQualifiedName);

                // 写入基本属性
                writer.Write(data.Timestamp);
                writer.Write(data.Sender.PlayerId);

                // 写入具体数据（使用JSON序列化）
                string json = JsonUtility.ToJson(data);
                writer.Write(json);

                return stream.ToArray();
            }
        }

        /// <summary>
        /// 反序列化网络数据
        /// </summary>
        public NetworkData Deserialize(byte[] data)
        {
            using (var stream = new MemoryStream(data))
            using (var reader = new BinaryReader(stream))
            {
                // 读取数据类型
                string typeName = reader.ReadString();
                Type dataType = Type.GetType(typeName);

                if (dataType == null)
                {
                    throw new Exception($"Unknown network data type: {typeName}");
                }

                // 读取基本属性
                uint timestamp = reader.ReadUInt32();
                int senderId = reader.ReadInt32();

                // 读取具体数据
                string json = reader.ReadString();
                NetworkData networkData = (NetworkData)JsonUtility.FromJson(json, dataType);

                // 设置基本属性
                networkData.Timestamp = timestamp;
                networkData.Sender = new PlayerRef(senderId);

                return networkData;
            }
        }
    }
}
