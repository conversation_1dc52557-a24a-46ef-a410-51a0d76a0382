using System;
using System.Threading.Tasks;

namespace CustomNetworking.Core
{
    /// <summary>
    /// 网络传输层接口 - 统一不同传输协议的抽象
    /// 支持WebSocket、UDP、TCP等多种传输方式
    /// </summary>
    public interface INetworkTransport
    {
        #region 属性
        
        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// 是否正在连接
        /// </summary>
        bool IsConnecting { get; }
        
        /// <summary>
        /// 传输类型
        /// </summary>
        NetworkTransportType TransportType { get; }
        
        /// <summary>
        /// 连接地址
        /// </summary>
        string Address { get; }
        
        /// <summary>
        /// 连接端口
        /// </summary>
        int Port { get; }
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 连接建立事件
        /// </summary>
        event Action OnConnected;
        
        /// <summary>
        /// 连接断开事件
        /// </summary>
        event Action<string> OnDisconnected; // 参数为断开原因
        
        /// <summary>
        /// 数据接收事件
        /// </summary>
        event Action<byte[], string> OnDataReceived; // 数据和发送者ID
        
        /// <summary>
        /// 错误事件
        /// </summary>
        event Action<string> OnError;
        
        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event Action<NetworkTransportState> OnStateChanged;
        
        #endregion
        
        #region 连接管理
        
        /// <summary>
        /// 作为客户端连接到服务器
        /// </summary>
        /// <param name="address">服务器地址</param>
        /// <param name="port">服务器端口</param>
        /// <param name="timeout">连接超时时间(毫秒)</param>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectAsync(string address, int port, int timeout = 5000);
        
        /// <summary>
        /// 作为服务器启动监听
        /// </summary>
        /// <param name="address">监听地址</param>
        /// <param name="port">监听端口</param>
        /// <param name="maxConnections">最大连接数</param>
        /// <returns>启动是否成功</returns>
        Task<bool> StartServerAsync(string address, int port, int maxConnections = 100);
        
        /// <summary>
        /// 断开连接
        /// </summary>
        /// <param name="reason">断开原因</param>
        /// <returns>断开操作完成</returns>
        Task DisconnectAsync(string reason = "User requested");
        
        #endregion
        
        #region 数据传输
        
        /// <summary>
        /// 发送数据到指定目标
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="targetId">目标ID，null表示发送给所有连接</param>
        /// <param name="reliable">是否可靠传输</param>
        /// <param name="priority">消息优先级</param>
        /// <returns>发送是否成功</returns>
        bool Send(byte[] data, string targetId = null, bool reliable = true, MessagePriority priority = MessagePriority.Normal);
        
        /// <summary>
        /// 广播数据到所有连接
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="reliable">是否可靠传输</param>
        /// <param name="excludeId">排除的连接ID</param>
        /// <returns>发送是否成功</returns>
        bool Broadcast(byte[] data, bool reliable = true, string excludeId = null);
        
        #endregion
        
        #region 连接管理 (服务器端)
        
        /// <summary>
        /// 获取所有连接的客户端ID
        /// </summary>
        /// <returns>客户端ID列表</returns>
        string[] GetConnectedClients();
        
        /// <summary>
        /// 断开指定客户端
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="reason">断开原因</param>
        void DisconnectClient(string clientId, string reason = "Server requested");
        
        /// <summary>
        /// 获取连接信息
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>连接信息</returns>
        NetworkConnectionInfo GetConnectionInfo(string clientId);
        
        #endregion
        
        #region 诊断和监控
        
        /// <summary>
        /// 获取网络统计信息
        /// </summary>
        /// <returns>网络统计</returns>
        NetworkTransportStats GetStats();
        
        /// <summary>
        /// 重置统计信息
        /// </summary>
        void ResetStats();
        
        #endregion
    }
    
    /// <summary>
    /// 网络传输类型
    /// </summary>
    public enum NetworkTransportType
    {
        WebSocket,
        UDP,
        TCP,
        Custom
    }
    
    /// <summary>
    /// 网络传输状态
    /// </summary>
    public enum NetworkTransportState
    {
        Disconnected,
        Connecting,
        Connected,
        Disconnecting,
        Error
    }
    
    /// <summary>
    /// 消息优先级
    /// </summary>
    public enum MessagePriority
    {
        Low = 0,
        Normal = 1,
        High = 2,
        Critical = 3
    }
    
    /// <summary>
    /// 网络连接信息
    /// </summary>
    [Serializable]
    public struct NetworkConnectionInfo
    {
        public string ClientId;
        public string RemoteAddress;
        public int RemotePort;
        public float ConnectedTime;
        public long BytesSent;
        public long BytesReceived;
        public float Latency;
        public float PacketLoss;
        
        public override string ToString()
        {
            return $"Client[{ClientId}] {RemoteAddress}:{RemotePort} (Latency: {Latency:F1}ms, Loss: {PacketLoss:P1})";
        }
    }
    
    /// <summary>
    /// 网络传输统计信息
    /// </summary>
    [Serializable]
    public struct NetworkTransportStats
    {
        public long TotalBytesSent;
        public long TotalBytesReceived;
        public long TotalMessagesSent;
        public long TotalMessagesReceived;
        public long DroppedMessages;
        public float AverageLatency;
        public float PacketLossRate;
        public int ActiveConnections;
        public float Uptime;
        
        public override string ToString()
        {
            return $"Stats[Sent: {TotalBytesSent} bytes, Received: {TotalBytesReceived} bytes, " +
                   $"Latency: {AverageLatency:F1}ms, Loss: {PacketLossRate:P1}, Connections: {ActiveConnections}]";
        }
    }
}
