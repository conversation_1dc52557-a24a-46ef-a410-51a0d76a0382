using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using System.Collections;

namespace CustomNetworking.Core
{
    /// <summary>
    /// WebSocket传输层实现
    /// 将WebSocket包装为统一的网络传输接口
    /// </summary>
    public class WebSocketTransport : MonoBehaviour, INetworkTransport
    {
        #region 属性

        public bool IsConnected { get; private set; }
        public bool IsConnecting { get; private set; }
        public NetworkTransportType TransportType => NetworkTransportType.WebSocket;
        public string Address { get; private set; }
        public int Port { get; private set; }

        #endregion

        #region 事件

        public event Action OnConnected;
        public event Action<string> OnDisconnected;
        public event Action<byte[], string> OnDataReceived;
        public event Action<string> OnError;
        public event Action<NetworkTransportState> OnStateChanged;

        #endregion

        #region 私有字段

        private NetworkTransportState _currentState = NetworkTransportState.Disconnected;
        private WebSocketConnection _connection;
        private Dictionary<string, ClientConnection> _clients = new Dictionary<string, ClientConnection>();
        private bool _isServer = false;
        private NetworkTransportStats _stats;
        private float _startTime;

        // 消息队列
        private Queue<QueuedMessage> _outgoingMessages = new Queue<QueuedMessage>();
        private Queue<IncomingMessage> _incomingMessages = new Queue<IncomingMessage>();

        #endregion

        #region Unity生命周期

        private void Start()
        {
            _startTime = Time.time;
            ResetStats();
        }

        private void Update()
        {
            ProcessOutgoingMessages();
            ProcessIncomingMessages();
        }

        private void OnDestroy()
        {
            if (IsConnected || IsConnecting)
            {
                _ = DisconnectAsync("Component destroyed");
            }
        }

        #endregion

        #region INetworkTransport实现

        public async Task<bool> ConnectAsync(string address, int port, int timeout = 5000)
        {
            if (IsConnected || IsConnecting)
            {
                UnityEngine.Debug.LogWarning("[WebSocketTransport] Already connected or connecting");
                return false;
            }

            Address = address;
            Port = port;
            _isServer = false;

            SetState(NetworkTransportState.Connecting);

            try
            {
                string wsUrl = $"ws://{address}:{port}";
                _connection = new WebSocketConnection(wsUrl);
                _connection.OnConnected += HandleConnectionEstablished;
                _connection.OnDisconnected += HandleConnectionLost;
                _connection.OnMessageReceived += HandleMessageReceived;
                _connection.OnError += HandleConnectionError;

                // 启动连接协程
                StartCoroutine(ConnectCoroutine(timeout));

                // 等待连接完成
                float startTime = Time.time;
                while (IsConnecting && Time.time - startTime < timeout / 1000f)
                {
                    await Task.Yield();
                }

                return IsConnected;
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[WebSocketTransport] Connect failed: {ex.Message}");
                OnError?.Invoke(ex.Message);
                SetState(NetworkTransportState.Error);
                return false;
            }
        }

        public async Task<bool> StartServerAsync(string address, int port, int maxConnections = 100)
        {
            if (IsConnected || IsConnecting)
            {
                UnityEngine.Debug.LogWarning("[WebSocketTransport] Already running");
                return false;
            }

            Address = address;
            Port = port;
            _isServer = true;

            SetState(NetworkTransportState.Connecting);

            try
            {
                // WebSocket服务器启动逻辑
                // 注意：实际项目中需要使用真实的WebSocket服务器实现
                await Task.Delay(100); // 模拟启动延迟

                SetState(NetworkTransportState.Connected);
                UnityEngine.Debug.Log($"[WebSocketTransport] Server started on {address}:{port}");

                return true;
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[WebSocketTransport] Start server failed: {ex.Message}");
                OnError?.Invoke(ex.Message);
                SetState(NetworkTransportState.Error);
                return false;
            }
        }

        public async Task DisconnectAsync(string reason = "User requested")
        {
            if (!IsConnected && !IsConnecting)
                return;

            SetState(NetworkTransportState.Disconnecting);

            try
            {
                if (_connection != null)
                {
                    _connection.Disconnect();
                    _connection = null;
                }

                // 清理客户端连接
                _clients.Clear();

                await Task.Delay(100); // 确保清理完成

                SetState(NetworkTransportState.Disconnected);
                OnDisconnected?.Invoke(reason);

                UnityEngine.Debug.Log($"[WebSocketTransport] Disconnected: {reason}");
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[WebSocketTransport] Disconnect error: {ex.Message}");
                OnError?.Invoke(ex.Message);
            }
        }

        public bool Send(byte[] data, string targetId = null, bool reliable = true, MessagePriority priority = MessagePriority.Normal)
        {
            if (!IsConnected)
            {
                UnityEngine.Debug.LogWarning("[WebSocketTransport] Cannot send: not connected");
                return false;
            }

            var message = new QueuedMessage
            {
                Data = data,
                TargetId = targetId,
                Reliable = reliable,
                Priority = priority,
                Timestamp = Time.time
            };

            _outgoingMessages.Enqueue(message);
            return true;
        }

        public bool Broadcast(byte[] data, bool reliable = true, string excludeId = null)
        {
            if (!IsConnected)
            {
                UnityEngine.Debug.LogWarning("[WebSocketTransport] Cannot broadcast: not connected");
                return false;
            }

            if (_isServer)
            {
                // 服务器广播给所有客户端
                foreach (var clientId in _clients.Keys)
                {
                    if (clientId != excludeId)
                    {
                        Send(data, clientId, reliable);
                    }
                }
            }
            else
            {
                // 客户端发送给服务器
                Send(data, null, reliable);
            }

            return true;
        }

        public string[] GetConnectedClients()
        {
            var clients = new string[_clients.Count];
            _clients.Keys.CopyTo(clients, 0);
            return clients;
        }

        public void DisconnectClient(string clientId, string reason = "Server requested")
        {
            if (!_isServer)
            {
                UnityEngine.Debug.LogWarning("[WebSocketTransport] Only server can disconnect clients");
                return;
            }

            if (_clients.ContainsKey(clientId))
            {
                _clients.Remove(clientId);
                UnityEngine.Debug.Log($"[WebSocketTransport] Disconnected client {clientId}: {reason}");
            }
        }

        public NetworkConnectionInfo GetConnectionInfo(string clientId)
        {
            if (_clients.TryGetValue(clientId, out var client))
            {
                return client.GetConnectionInfo();
            }

            return default;
        }

        public NetworkTransportStats GetStats()
        {
            _stats.Uptime = Time.time - _startTime;
            _stats.ActiveConnections = _clients.Count;
            return _stats;
        }

        public void ResetStats()
        {
            _stats = new NetworkTransportStats
            {
                TotalBytesSent = 0,
                TotalBytesReceived = 0,
                TotalMessagesSent = 0,
                TotalMessagesReceived = 0,
                DroppedMessages = 0,
                AverageLatency = 0,
                PacketLossRate = 0,
                ActiveConnections = 0,
                Uptime = 0
            };
        }

        #endregion

        #region 私有方法

        private void SetState(NetworkTransportState newState)
        {
            if (_currentState != newState)
            {
                _currentState = newState;

                IsConnected = newState == NetworkTransportState.Connected;
                IsConnecting = newState == NetworkTransportState.Connecting;

                OnStateChanged?.Invoke(newState);
            }
        }

        private IEnumerator ConnectCoroutine(int timeout)
        {
            yield return _connection.Connect();

            if (_connection.IsConnected)
            {
                SetState(NetworkTransportState.Connected);
            }
            else
            {
                SetState(NetworkTransportState.Error);
            }
        }

        private void ProcessOutgoingMessages()
        {
            while (_outgoingMessages.Count > 0)
            {
                var message = _outgoingMessages.Dequeue();

                if (_connection != null && _connection.IsConnected)
                {
                    // 将二进制数据转换为Base64字符串发送
                    string base64Data = Convert.ToBase64String(message.Data);
                    _connection.Send(base64Data);

                    _stats.TotalBytesSent += message.Data.Length;
                    _stats.TotalMessagesSent++;
                }
            }
        }

        private void ProcessIncomingMessages()
        {
            while (_incomingMessages.Count > 0)
            {
                var message = _incomingMessages.Dequeue();
                OnDataReceived?.Invoke(message.Data, message.SenderId);

                _stats.TotalBytesReceived += message.Data.Length;
                _stats.TotalMessagesReceived++;
            }
        }


        private void HandleConnectionEstablished()
        {
            UnityEngine.Debug.Log("[WebSocketTransport] Connection established");
            OnConnected?.Invoke();
        }

        private void HandleConnectionLost()
        {
            UnityEngine.Debug.LogWarning("[WebSocketTransport] Connection lost");
            SetState(NetworkTransportState.Disconnected);
            OnDisconnected?.Invoke("Connection lost");
        }

        private void HandleMessageReceived(string message)
        {
            try
            {
                // 将Base64字符串转换回二进制数据
                byte[] data = Convert.FromBase64String(message);

                var incomingMessage = new IncomingMessage
                {
                    Data = data,
                    SenderId = "server", // 简化实现，实际应该从消息中解析
                    Timestamp = Time.time
                };

                _incomingMessages.Enqueue(incomingMessage);
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[WebSocketTransport] Failed to process message: {ex.Message}");
                OnError?.Invoke($"Message processing error: {ex.Message}");
            }
        }

        private void HandleConnectionError(string error)
        {
            UnityEngine.Debug.LogError($"[WebSocketTransport] Connection error: {error}");
            OnError?.Invoke(error);
            SetState(NetworkTransportState.Error);
        }

        #endregion
    }

    #region 辅助类

    /// <summary>
    /// 排队的消息
    /// </summary>
    internal struct QueuedMessage
    {
        public byte[] Data;
        public string TargetId;
        public bool Reliable;
        public MessagePriority Priority;
        public float Timestamp;
    }

    /// <summary>
    /// 传入的消息
    /// </summary>
    internal struct IncomingMessage
    {
        public byte[] Data;
        public string SenderId;
        public float Timestamp;
    }

    /// <summary>
    /// 客户端连接信息
    /// </summary>
    internal class ClientConnection
    {
        public string ClientId { get; set; }
        public string RemoteAddress { get; set; }
        public int RemotePort { get; set; }
        public float ConnectedTime { get; set; }
        public long BytesSent { get; set; }
        public long BytesReceived { get; set; }

        public NetworkConnectionInfo GetConnectionInfo()
        {
            return new NetworkConnectionInfo
            {
                ClientId = ClientId,
                RemoteAddress = RemoteAddress,
                RemotePort = RemotePort,
                ConnectedTime = Time.time - ConnectedTime,
                BytesSent = BytesSent,
                BytesReceived = BytesReceived,
                Latency = 0f, // TODO: 实现延迟测量
                PacketLoss = 0f // TODO: 实现丢包率计算
            };
        }
    }

    /// <summary>
    /// WebSocket连接包装器
    /// 注意：这是一个简化的实现，实际项目中需要使用真实的WebSocket库
    /// </summary>
    internal class WebSocketConnection
    {
        public bool IsConnected { get; private set; }
        public string Url { get; private set; }

        public event Action OnConnected;
        public event Action OnDisconnected;
        public event Action<string> OnMessageReceived;
        public event Action<string> OnError;

        public WebSocketConnection(string url)
        {
            Url = url;
        }

        public IEnumerator Connect()
        {
            UnityEngine.Debug.Log($"[WebSocketConnection] Connecting to {Url}");

            // 模拟连接过程
            yield return new WaitForSeconds(1f);

            // 模拟连接成功
            IsConnected = true;
            OnConnected?.Invoke();

            UnityEngine.Debug.Log($"[WebSocketConnection] Connected to {Url}");
        }

        public void Send(string message)
        {
            if (!IsConnected)
            {
                UnityEngine.Debug.LogWarning("[WebSocketConnection] Cannot send: not connected");
                return;
            }

            // 模拟发送消息
            UnityEngine.Debug.Log($"[WebSocketConnection] Sending: {message.Length} bytes");
        }

        public void Disconnect()
        {
            if (!IsConnected)
                return;

            IsConnected = false;
            OnDisconnected?.Invoke();

            UnityEngine.Debug.Log("[WebSocketConnection] Disconnected");
        }
    }

    #endregion
}
