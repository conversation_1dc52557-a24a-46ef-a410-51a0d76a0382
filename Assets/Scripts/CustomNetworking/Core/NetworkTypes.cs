using System;
using System.Collections.Generic;
using UnityEngine;

namespace CustomNetworking.Core
{
    #region 基础网络类型
    
    /// <summary>
    /// 玩家引用
    /// </summary>
    [Serializable]
    public struct PlayerRef : IEquatable<PlayerRef>
    {
        public static readonly PlayerRef None = new PlayerRef(0);
        public static readonly PlayerRef MasterClient = new PlayerRef(-1);
        
        public int PlayerId { get; private set; }
        
        public PlayerRef(int playerId)
        {
            PlayerId = playerId;
        }
        
        public bool IsValid => PlayerId > 0;
        public bool IsRealPlayer => PlayerId > 0;
        
        public bool Equals(PlayerRef other)
        {
            return PlayerId == other.PlayerId;
        }
        
        public override bool Equals(object obj)
        {
            return obj is PlayerRef other && Equals(other);
        }
        
        public override int GetHashCode()
        {
            return PlayerId.GetHashCode();
        }
        
        public static bool operator ==(PlayerRef left, PlayerRef right)
        {
            return left.Equals(right);
        }
        
        public static bool operator !=(PlayerRef left, PlayerRef right)
        {
            return !left.Equals(right);
        }
        
        public override string ToString()
        {
            if (PlayerId == 0) return "None";
            if (PlayerId == -1) return "MasterClient";
            return $"Player{PlayerId}";
        }
    }
    
    /// <summary>
    /// 网络布尔值
    /// </summary>
    [Serializable]
    public struct NetworkBool : IEquatable<NetworkBool>
    {
        private bool _value;
        
        public NetworkBool(bool value) { _value = value; }
        
        public static implicit operator bool(NetworkBool nb) => nb._value;
        public static implicit operator NetworkBool(bool b) => new NetworkBool(b);
        
        public bool Equals(NetworkBool other) => _value == other._value;
        public override bool Equals(object obj) => obj is NetworkBool other && Equals(other);
        public override int GetHashCode() => _value.GetHashCode();
        
        public static bool operator ==(NetworkBool left, NetworkBool right) => left.Equals(right);
        public static bool operator !=(NetworkBool left, NetworkBool right) => !left.Equals(right);
        
        public override string ToString() => _value.ToString();
    }
    
    /// <summary>
    /// 网络字符串
    /// </summary>
    [Serializable]
    public struct NetworkString<TSize> : IEquatable<NetworkString<TSize>> where TSize : struct
    {
        private string _value;
        
        public NetworkString(string value) { _value = value ?? string.Empty; }
        
        public static implicit operator string(NetworkString<TSize> ns) => ns._value ?? string.Empty;
        public static implicit operator NetworkString<TSize>(string s) => new NetworkString<TSize>(s);
        
        public bool Equals(NetworkString<TSize> other) => string.Equals(_value, other._value);
        public override bool Equals(object obj) => obj is NetworkString<TSize> other && Equals(other);
        public override int GetHashCode() => _value?.GetHashCode() ?? 0;
        
        public override string ToString() => _value ?? string.Empty;
    }
    
    /// <summary>
    /// 网络字符串大小标记
    /// </summary>
    public struct _16 { }
    public struct _32 { }
    public struct _64 { }
    public struct _128 { }
    
    /// <summary>
    /// 网络ID
    /// </summary>
    [Serializable]
    public struct NetworkId : IEquatable<NetworkId>
    {
        public uint Raw { get; private set; }
        
        public NetworkId(uint raw)
        {
            Raw = raw;
        }
        
        public bool IsValid => Raw != 0;
        
        public bool Equals(NetworkId other) => Raw == other.Raw;
        public override bool Equals(object obj) => obj is NetworkId other && Equals(other);
        public override int GetHashCode() => Raw.GetHashCode();
        
        public static bool operator ==(NetworkId left, NetworkId right) => left.Equals(right);
        public static bool operator !=(NetworkId left, NetworkId right) => !left.Equals(right);
        
        public override string ToString() => $"NetworkId({Raw})";
        
        public static NetworkId None => new NetworkId(0);
    }
    
    #endregion
    
    #region 网络计时器
    
    /// <summary>
    /// 网络计时器
    /// </summary>
    [Serializable]
    public struct TickTimer : IEquatable<TickTimer>
    {
        private uint _targetTick;
        private bool _isRunning;
        
        public bool IsRunning => _isRunning;
        public uint TargetTick => _targetTick;
        
        private TickTimer(uint targetTick)
        {
            _targetTick = targetTick;
            _isRunning = true;
        }
        
        /// <summary>
        /// 创建一个基于秒数的计时器
        /// </summary>
        public static TickTimer CreateFromSeconds(NetworkRunner runner, float seconds)
        {
            if (runner == null)
                return new TickTimer();
                
            uint ticksToAdd = (uint)(seconds * runner.TickRate);
            return new TickTimer(runner.Tick + ticksToAdd);
        }
        
        /// <summary>
        /// 创建一个基于Tick数的计时器
        /// </summary>
        public static TickTimer CreateFromTicks(NetworkRunner runner, uint ticks)
        {
            if (runner == null)
                return new TickTimer();
                
            return new TickTimer(runner.Tick + ticks);
        }
        
        /// <summary>
        /// 检查计时器是否已过期
        /// </summary>
        public bool Expired(NetworkRunner runner)
        {
            if (!_isRunning || runner == null)
                return false;
                
            return runner.Tick >= _targetTick;
        }
        
        /// <summary>
        /// 获取剩余时间（秒）
        /// </summary>
        public float? RemainingTime(NetworkRunner runner)
        {
            if (!_isRunning || runner == null)
                return null;
                
            if (runner.Tick >= _targetTick)
                return 0f;
                
            uint remainingTicks = _targetTick - runner.Tick;
            return remainingTicks / (float)runner.TickRate;
        }
        
        /// <summary>
        /// 获取剩余Tick数
        /// </summary>
        public uint? RemainingTicks(NetworkRunner runner)
        {
            if (!_isRunning || runner == null)
                return null;
                
            if (runner.Tick >= _targetTick)
                return 0;
                
            return _targetTick - runner.Tick;
        }
        
        /// <summary>
        /// 停止计时器
        /// </summary>
        public void Stop()
        {
            _isRunning = false;
        }

        /// <summary>
        /// 检查计时器是否已过期或未运行
        /// </summary>
        public bool ExpiredOrNotRunning(NetworkRunner runner)
        {
            if (!_isRunning)
                return true;

            if (runner == null)
                return true;

            return runner.Tick >= _targetTick;
        }
        
        public bool Equals(TickTimer other)
        {
            return _targetTick == other._targetTick && _isRunning == other._isRunning;
        }
        
        public override bool Equals(object obj)
        {
            return obj is TickTimer other && Equals(other);
        }
        
        public override int GetHashCode()
        {
            return HashCode.Combine(_targetTick, _isRunning);
        }
        
        public static bool operator ==(TickTimer left, TickTimer right)
        {
            return left.Equals(right);
        }
        
        public static bool operator !=(TickTimer left, TickTimer right)
        {
            return !left.Equals(right);
        }
        
        public override string ToString()
        {
            return _isRunning ? $"TickTimer(Target: {_targetTick})" : "TickTimer(Stopped)";
        }
    }
    
    #endregion
    
    #region 网络输入系统
    
    /// <summary>
    /// 网络输入接口
    /// </summary>
    public interface INetworkInput
    {
        // 标记接口，具体输入结构由用户定义
    }
    
    /// <summary>
    /// 网络输入包装器
    /// </summary>
    public class NetworkInput
    {
        private INetworkInput _input;
        
        public void Set<T>(T input) where T : struct, INetworkInput
        {
            _input = input;
        }
        
        public T Get<T>() where T : struct, INetworkInput
        {
            if (_input is T)
                return (T)_input;
            return default;
        }
        
        public INetworkInput GetInput()
        {
            return _input;
        }
    }
    
    #endregion

    #region 网络连接和断开原因

    /// <summary>
    /// 网络断开原因
    /// </summary>
    public enum NetDisconnectReason
    {
        None,
        Shutdown,
        Timeout,
        ConnectionLost,
        ServerFull,
        InvalidData,
        Kicked,
        Error
    }



    #endregion

    #region 网络地址

    /// <summary>
    /// 网络地址结构
    /// </summary>
    [Serializable]
    public struct NetAddress
    {
        public string Address { get; private set; }
        public int Port { get; private set; }

        public NetAddress(string address, int port = 0)
        {
            Address = address ?? "127.0.0.1";
            Port = port;
        }

        public static NetAddress Any(int port = 0) => new NetAddress("0.0.0.0", port);
        public static NetAddress Localhost(int port = 0) => new NetAddress("127.0.0.1", port);

        public override string ToString() => Port > 0 ? $"{Address}:{Port}" : Address;

        public bool IsValid => !string.IsNullOrEmpty(Address);
    }

    #endregion

    #region 会话信息

    /// <summary>
    /// 会话信息类
    /// </summary>
    [Serializable]
    public class SessionInfo
    {
        public string Name { get; set; }
        public int PlayerCount { get; set; }
        public int MaxPlayers { get; set; }
        public bool IsOpen { get; set; }
        public bool IsVisible { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

        public SessionInfo(string name)
        {
            Name = name ?? "Unknown";
            PlayerCount = 0;
            MaxPlayers = 10;
            IsOpen = true;
            IsVisible = true;
        }

        public bool IsValid => !string.IsNullOrEmpty(Name);

        public override string ToString() => $"Session({Name}, {PlayerCount}/{MaxPlayers})";
    }

    #endregion

    #region 网络启动参数和结果

    /// <summary>
    /// 启动游戏参数
    /// </summary>
    [Serializable]
    public class StartGameArgs
    {
        public NetworkRunner.GameMode GameMode { get; set; }
        public NetAddress Address { get; set; }
        public string SessionName { get; set; }
        public int MaxPlayers { get; set; } = 10;
        public bool IsOpen { get; set; } = true;
        public bool IsVisible { get; set; } = true;

        public StartGameArgs()
        {
            GameMode = NetworkRunner.GameMode.AutoHostOrClient;
            Address = NetAddress.Any();
        }
    }

    /// <summary>
    /// 启动游戏结果
    /// </summary>
    [Serializable]
    public class StartGameResult
    {
        public bool Ok { get; set; }
        public string ErrorMessage { get; set; }

        public StartGameResult(bool ok, string errorMessage = null)
        {
            Ok = ok;
            ErrorMessage = errorMessage;
        }

        public static StartGameResult Success() => new StartGameResult(true);
        public static StartGameResult Failed(string error) => new StartGameResult(false, error);
    }

    #endregion

    #region 网络属性标记
    
    /// <summary>
    /// 网络属性标记
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
    public class NetworkedAttribute : Attribute
    {
        public bool OnChanged { get; set; } = false;
        public string OnChangedTargets { get; set; } = null;
        
        public NetworkedAttribute() { }
    }
    
    #endregion

    #region 变化检测

    /// <summary>
    /// 变化检测器
    /// </summary>
    public class ChangeDetector
    {
        private Dictionary<string, object> _previousValues = new Dictionary<string, object>();

        public bool DetectChange<T>(string key, T currentValue) where T : IEquatable<T>
        {
            if (_previousValues.TryGetValue(key, out var previousValue))
            {
                if (previousValue is T typedPrevious && !typedPrevious.Equals(currentValue))
                {
                    _previousValues[key] = currentValue;
                    return true;
                }
            }
            else
            {
                _previousValues[key] = currentValue;
                return true; // 第一次检测总是返回变化
            }

            return false;
        }

        /// <summary>
        /// 检测对象变化 - 支持任意类型
        /// </summary>
        public bool DetectChange(string key, object currentValue)
        {
            if (_previousValues.TryGetValue(key, out var previousValue))
            {
                bool hasChanged = !Equals(previousValue, currentValue);
                if (hasChanged)
                {
                    _previousValues[key] = currentValue;
                    return true;
                }
            }
            else
            {
                _previousValues[key] = currentValue;
                return true; // 第一次检测总是返回变化
            }

            return false;
        }

        public void Reset()
        {
            _previousValues.Clear();
        }

        public enum Source
        {
            SimulationState,
            RenderState
        }
    }

    #endregion
}
