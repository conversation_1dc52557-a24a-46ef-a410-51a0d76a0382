using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;
using CustomNetworking.Core.RPC;

namespace CustomNetworking.Core
{
    /// <summary>
    /// 自定义网络行为基类 - 替代 Photon Fusion NetworkBehaviour
    /// </summary>
    public abstract class NetworkBehaviour : MonoBehaviour
    {
        #region 属性
        
        public NetworkObject Object { get; private set; }
        public NetworkRunner Runner { get; private set; }
        
        // 权限检查
        public bool HasStateAuthority => Object?.HasStateAuthority ?? false;
        public bool HasInputAuthority => Object?.HasInputAuthority ?? false;
        
        // 网络属性管理
        private Dictionary<string, INetworkedProperty> _networkedProperties = new Dictionary<string, INetworkedProperty>();
        private ChangeDetector _changeDetector;
        
        #endregion
        
        #region 生命周期
        
        protected virtual void Awake()
        {
            Object = GetComponent<NetworkObject>();
            if (Object == null)
            {
                Object = gameObject.AddComponent<NetworkObject>();
            }
            
            // 初始化网络属性
            InitializeNetworkedProperties();
            _changeDetector = new ChangeDetector();
        }
        
        protected virtual void Start()
        {
            if (Object != null)
            {
                Object.RegisterBehaviour(this);
            }
        }
        
        #endregion
        
        #region 网络生命周期
        
        /// <summary>
        /// 网络对象生成时调用
        /// </summary>
        public virtual void Spawned()
        {
            Runner = Object?.Runner;

            // 注册 RPC 方法
            RpcManager.Instance.RegisterNetworkBehaviour(this);
        }
        
        /// <summary>
        /// 网络对象销毁时调用
        /// </summary>
        public virtual void Despawned()
        {
            // 取消注册 RPC 方法
            RpcManager.Instance.UnregisterNetworkBehaviour(this);

            Runner = null;
        }
        
        /// <summary>
        /// 固定网络更新 - 在服务器上执行
        /// </summary>
        public virtual void FixedUpdateNetwork()
        {
            // 子类重写此方法来实现网络逻辑
        }
        
        /// <summary>
        /// 渲染更新 - 在客户端上执行
        /// </summary>
        public virtual void Render()
        {
            // 检测网络属性变化
            DetectNetworkedPropertyChanges();
        }
        
        #endregion
        
        #region 网络属性管理
        
        /// <summary>
        /// 初始化网络属性
        /// </summary>
        private void InitializeNetworkedProperties()
        {
            var type = GetType();
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            
            foreach (var property in properties)
            {
                var networkedAttr = property.GetCustomAttribute<NetworkedAttribute>();
                if (networkedAttr != null)
                {
                    var networkedProperty = new NetworkedProperty(property, this);
                    _networkedProperties[property.Name] = networkedProperty;
                }
            }
        }
        
        /// <summary>
        /// 检测网络属性变化
        /// </summary>
        private void DetectNetworkedPropertyChanges()
        {
            foreach (var kvp in _networkedProperties)
            {
                var property = kvp.Value;
                var currentValue = property.GetValue();
                
                if (_changeDetector.DetectChange(kvp.Key, currentValue))
                {
                    OnNetworkedPropertyChanged(kvp.Key, currentValue);
                }
            }
        }
        
        /// <summary>
        /// 网络属性变化时调用
        /// </summary>
        protected virtual void OnNetworkedPropertyChanged(string propertyName, object newValue)
        {
            // 子类可以重写此方法来处理属性变化
        }
        
        #endregion
        
        #region 输入处理
        
        /// <summary>
        /// 获取输入 - 用于处理网络输入
        /// </summary>
        protected bool GetInput<T>(out T input) where T : struct, INetworkInput
        {
            input = default;
            
            if (Runner == null || !HasInputAuthority)
                return false;
            
            // 这里应该从输入系统获取输入
            // 暂时返回默认值
            return false;
        }
        
        #endregion
        
        #region 变化检测
        
        /// <summary>
        /// 获取变化检测器
        /// </summary>
        protected ChangeDetector GetChangeDetector(ChangeDetector.Source source)
        {
            return _changeDetector;
        }
        
        /// <summary>
        /// 获取属性读取器
        /// </summary>
        protected PropertyReader<T> GetPropertyReader<T>(string propertyName) where T : struct
        {
            return new PropertyReader<T>(propertyName);
        }
        
        #endregion

        #region RPC 调用方法

        /// <summary>
        /// 发送 RPC 到所有客户端
        /// </summary>
        protected void RPC(string methodName, params object[] parameters)
        {
            RPC(methodName, RpcTargets.All, true, RpcPriority.Normal, PlayerRef.None, parameters);
        }

        /// <summary>
        /// 发送 RPC 到指定目标
        /// </summary>
        protected void RPC(string methodName, RpcTargets target, params object[] parameters)
        {
            RPC(methodName, target, true, RpcPriority.Normal, PlayerRef.None, parameters);
        }

        /// <summary>
        /// 发送 RPC 到指定玩家
        /// </summary>
        protected void RPC(string methodName, PlayerRef targetPlayer, params object[] parameters)
        {
            RPC(methodName, RpcTargets.Player, true, RpcPriority.Normal, targetPlayer, parameters);
        }

        /// <summary>
        /// 发送 RPC（完整参数）
        /// </summary>
        protected void RPC(string methodName, RpcTargets target, bool reliable, RpcPriority priority,
                          PlayerRef targetPlayer, params object[] parameters)
        {
            if (Runner == null)
            {
                UnityEngine.Debug.LogWarning($"Cannot send RPC '{methodName}': NetworkRunner is null");
                return;
            }

            RpcManager.Instance.SendRpc(this, methodName, target, reliable, priority, targetPlayer, false, parameters);
        }

        #endregion
    }

    /// <summary>
    /// 网络属性接口
    /// </summary>
    public interface INetworkedProperty
    {
        object GetValue();
        void SetValue(object value);
        Type PropertyType { get; }
    }
    
    /// <summary>
    /// 网络属性实现
    /// </summary>
    public class NetworkedProperty : INetworkedProperty
    {
        private readonly PropertyInfo _property;
        private readonly object _target;
        
        public Type PropertyType => _property.PropertyType;
        
        public NetworkedProperty(PropertyInfo property, object target)
        {
            _property = property;
            _target = target;
        }
        
        public object GetValue()
        {
            return _property.GetValue(_target);
        }
        
        public void SetValue(object value)
        {
            _property.SetValue(_target, value);
        }
    }
    
    /// <summary>
    /// 属性读取器
    /// </summary>
    public class PropertyReader<T> where T : struct
    {
        private readonly string _propertyName;
        
        public PropertyReader(string propertyName)
        {
            _propertyName = propertyName;
        }
        
        public (T oldValue, T newValue) Read(object previousBuffer, object currentBuffer)
        {
            // 简化实现，实际应该从缓冲区读取
            return (default(T), default(T));
        }
    }
}
