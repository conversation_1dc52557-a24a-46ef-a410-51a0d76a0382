using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using CustomNetworking.Core.RPC;

namespace CustomNetworking.Core
{
    /// <summary>
    /// 自定义网络运行器 - 替代 Photon Fusion NetworkRunner
    /// </summary>
    public class NetworkRunner : MonoBehaviour
    {
        #region 枚举定义

        public enum States
        {
            Shutdown,
            Starting,
            Running,
            Stopping
        }

        public enum GameMode
        {
            Server,
            Host,
            Client,
            AutoHostOrClient,
            Shared,
            Single
        }

        #endregion

        #region 属性和字段

        // 状态
        public States State { get; private set; } = States.Shutdown;
        public GameMode Mode { get; private set; }
        public bool IsRunning => State == States.Running;
        public bool IsServer => Mode == GameMode.Server || Mode == GameMode.Host;
        public bool IsHost => Mode == GameMode.Host;
        public bool IsClient => Mode == GameMode.Client || Mode == GameMode.Host;

        // 网络配置
        public float DeltaTime => Time.fixedDeltaTime;
        public float SimulationTime { get; private set; }
        public int TickRate { get; private set; } = 64;
        public uint Tick { get; private set; }

        // 玩家管理
        private Dictionary<PlayerRef, NetworkObject> _playerObjects = new Dictionary<PlayerRef, NetworkObject>();
        private List<PlayerRef> _activePlayers = new List<PlayerRef>();
        private PlayerRef _localPlayer = PlayerRef.None;

        // 网络对象管理
        private Dictionary<NetworkId, NetworkObject> _networkObjects = new Dictionary<NetworkId, NetworkObject>();
        private uint _nextNetworkId = 1;

        // 回调管理
        private List<INetworkRunnerCallbacks> _callbacks = new List<INetworkRunnerCallbacks>();

        // 会话信息
        public SessionInfo SessionInfo { get; private set; }

        // 输入管理
        public bool ProvideInput { get; set; } = true;

        // 网络连接
        private NetworkConnection _connection;

        #endregion

        #region Unity生命周期

        private void FixedUpdate()
        {
            if (State == States.Running)
            {
                Tick++;
                SimulationTime += DeltaTime;

                // 更新 RPC 管理器
                RpcManager.Instance.Update();

                // 处理网络对象的FixedUpdateNetwork
                foreach (var networkObject in _networkObjects.Values)
                {
                    if (networkObject != null)
                    {
                        networkObject.FixedUpdateNetwork();
                    }
                }
            }
        }

        private void Update()
        {
            if (State == States.Running)
            {
                // 处理网络对象的Render
                foreach (var networkObject in _networkObjects.Values)
                {
                    if (networkObject != null)
                    {
                        networkObject.Render();
                    }
                }
            }
        }

        #endregion

        #region 公共API

        /// <summary>
        /// 启动网络运行器
        /// </summary>
        public async Task<StartGameResult> StartGame(StartGameArgs args)
        {
            if (State != States.Shutdown)
            {
                return StartGameResult.Failed("Runner is already running");
            }

            try
            {
                State = States.Starting;
                Mode = args.GameMode;

                // 初始化会话信息
                SessionInfo = new SessionInfo(args.SessionName)
                {
                    MaxPlayers = args.MaxPlayers,
                    IsOpen = args.IsOpen,
                    IsVisible = args.IsVisible
                };

                // 模拟网络连接
                await Task.Delay(100); // 模拟连接延迟

                State = States.Running;

                // 初始化网络连接 - 使用统一传输层
                INetworkTransport transport = CreateTransport();
                _connection = new UnifiedNetworkConnection(transport);

                // 根据模式启动连接
                if (IsServer)
                {
                    await _connection.StartServer(args.Address, args.Address.Port);
                }
                else
                {
                    await _connection.ConnectToServer(args.Address, args.Address.Port);
                }

                // 初始化 RPC 管理器
                RpcManager.Instance.Initialize(this);

                // 如果是服务器或主机，设置本地玩家
                if (IsServer)
                {
                    _localPlayer = new PlayerRef(1);
                    _activePlayers.Add(_localPlayer);
                }

                // 通知回调
                foreach (var callback in _callbacks)
                {
                    try
                    {
                        callback.OnConnectedToServer(this);
                    }
                    catch (Exception ex)
                    {
                        UnityEngine.Debug.LogError($"Error in OnConnectedToServer callback: {ex}");
                    }
                }

                UnityEngine.Debug.Log($"NetworkRunner started in {Mode} mode");
                return StartGameResult.Success();
            }
            catch (Exception ex)
            {
                State = States.Shutdown;
                UnityEngine.Debug.LogError($"Failed to start NetworkRunner: {ex}");
                return StartGameResult.Failed(ex.Message);
            }
        }

        /// <summary>
        /// 关闭网络运行器
        /// </summary>
        public async Task Shutdown()
        {
            if (State == States.Shutdown)
                return;

            State = States.Stopping;

            // 清理 RPC 管理器
            RpcManager.Instance.Cleanup();

            // 清理网络对象
            var objectsToDestroy = new List<NetworkObject>(_networkObjects.Values);
            foreach (var obj in objectsToDestroy)
            {
                if (obj != null)
                {
                    Destroy(obj.gameObject);
                }
            }
            _networkObjects.Clear();

            // 清理玩家
            _playerObjects.Clear();
            _activePlayers.Clear();
            _localPlayer = PlayerRef.None;

            // 通知回调
            foreach (var callback in _callbacks)
            {
                try
                {
                    callback.OnShutdown(this, NetDisconnectReason.Shutdown);
                }
                catch (Exception ex)
                {
                    UnityEngine.Debug.LogError($"Error in OnShutdown callback: {ex}");
                }
            }

            State = States.Shutdown;

            // 模拟关闭延迟
            await Task.Delay(50);

            UnityEngine.Debug.Log("NetworkRunner shutdown complete");
        }

        /// <summary>
        /// 添加回调
        /// </summary>
        public void AddCallbacks(INetworkRunnerCallbacks callbacks)
        {
            if (callbacks != null && !_callbacks.Contains(callbacks))
            {
                _callbacks.Add(callbacks);
            }
        }

        /// <summary>
        /// 移除回调
        /// </summary>
        public void RemoveCallbacks(INetworkRunnerCallbacks callbacks)
        {
            _callbacks.Remove(callbacks);
        }

        /// <summary>
        /// 生成网络对象
        /// </summary>
        public NetworkObject Spawn(GameObject prefab, Vector3 position = default, Quaternion rotation = default, PlayerRef inputAuthority = default)
        {
            if (!IsServer)
            {
                UnityEngine.Debug.LogWarning("Only server can spawn objects");
                return null;
            }

            var instance = Instantiate(prefab, position, rotation);
            var networkObject = instance.GetComponent<NetworkObject>();

            if (networkObject == null)
            {
                networkObject = instance.AddComponent<NetworkObject>();
            }

            // 分配网络ID
            var networkId = new NetworkId(_nextNetworkId++);
            networkObject.Initialize(this, networkId, inputAuthority);

            _networkObjects[networkId] = networkObject;

            UnityEngine.Debug.Log($"Spawned NetworkObject with ID: {networkId}");
            return networkObject;
        }

        /// <summary>
        /// 销毁网络对象
        /// </summary>
        public void Despawn(NetworkObject networkObject)
        {
            if (!IsServer)
            {
                UnityEngine.Debug.LogWarning("Only server can despawn objects");
                return;
            }

            if (networkObject != null && _networkObjects.ContainsKey(networkObject.Id))
            {
                _networkObjects.Remove(networkObject.Id);
                Destroy(networkObject.gameObject);
                UnityEngine.Debug.Log($"Despawned NetworkObject with ID: {networkObject.Id}");
            }
        }

        /// <summary>
        /// 获取玩家对象
        /// </summary>
        public NetworkObject GetPlayerObject(PlayerRef player)
        {
            _playerObjects.TryGetValue(player, out var playerObject);
            return playerObject;
        }

        /// <summary>
        /// 设置玩家对象
        /// </summary>
        public void SetPlayerObject(PlayerRef player, NetworkObject playerObject)
        {
            if (playerObject != null)
            {
                _playerObjects[player] = playerObject;
            }
            else
            {
                _playerObjects.Remove(player);
            }
        }

        /// <summary>
        /// 获取活跃玩家列表
        /// </summary>
        public List<PlayerRef> ActivePlayers => new List<PlayerRef>(_activePlayers);

        /// <summary>
        /// 本地玩家
        /// </summary>
        public PlayerRef LocalPlayer => _localPlayer;

        #endregion

        #region RPC 消息发送

        /// <summary>
        /// 发送消息到所有客户端
        /// </summary>
        public void SendToAll(NetworkData data)
        {
            if (_connection != null)
            {
                _connection.SendToAll(data);
            }
        }

        /// <summary>
        /// 发送消息到指定玩家
        /// </summary>
        public void SendToPlayer(PlayerRef player, NetworkData data)
        {
            if (_connection != null)
            {
                _connection.SendToPlayer(player, data);
            }
        }

        /// <summary>
        /// 发送消息到服务器
        /// </summary>
        public void SendToServer(NetworkData data)
        {
            if (_connection != null && !IsServer)
            {
                // 客户端发送到服务器的逻辑
                UnityEngine.Debug.Log($"Sending data to server: {data.GetType().Name}");
            }
        }

        /// <summary>
        /// 创建传输层实例
        /// </summary>
        private INetworkTransport CreateTransport()
        {
            // 创建WebSocket传输层
            var transportGO = new GameObject("NetworkTransport");
            transportGO.transform.SetParent(transform);

            var webSocketTransport = transportGO.AddComponent<WebSocketTransport>();
            return webSocketTransport;
        }

        /// <summary>
        /// 获取网络诊断信息
        /// </summary>
        public NetworkDiagnostics GetNetworkDiagnostics()
        {
            var diagnostics = new NetworkDiagnostics();

            if (_connection is UnifiedNetworkConnection unifiedConnection)
            {
                var stats = unifiedConnection.GetTransportStats();
                diagnostics.Latency = stats.AverageLatency;
                diagnostics.PacketLoss = stats.PacketLossRate;
                diagnostics.BytesSent = stats.TotalBytesSent;
                diagnostics.BytesReceived = stats.TotalBytesReceived;
            }

            return diagnostics;
        }

        #endregion
    }

    /// <summary>
    /// 网络诊断信息
    /// </summary>
    [Serializable]
    public struct NetworkDiagnostics
    {
        public float Latency;
        public float PacketLoss;
        public long BytesSent;
        public long BytesReceived;
        public int ActiveConnections;

        public override string ToString()
        {
            return $"Diagnostics[Latency: {Latency:F1}ms, Loss: {PacketLoss:P1}, " +
                   $"Sent: {BytesSent}, Received: {BytesReceived}, Connections: {ActiveConnections}]";
        }
    }
}
