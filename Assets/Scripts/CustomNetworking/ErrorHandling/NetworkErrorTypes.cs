using System;

namespace CustomNetworking.ErrorHandling
{
    /// <summary>
    /// 网络错误类型枚举
    /// </summary>
    public enum ErrorType
    {
        /// <summary>
        /// 连接丢失
        /// </summary>
        ConnectionLost,
        
        /// <summary>
        /// 连接超时
        /// </summary>
        ConnectionTimeout,
        
        /// <summary>
        /// 服务器不可达
        /// </summary>
        ServerUnreachable,
        
        /// <summary>
        /// 认证失败
        /// </summary>
        AuthenticationFailed,
        
        /// <summary>
        /// 房间已满
        /// </summary>
        RoomFull,
        
        /// <summary>
        /// 房间未找到
        /// </summary>
        RoomNotFound,
        
        /// <summary>
        /// 网络数据损坏
        /// </summary>
        NetworkDataCorrupted,
        
        /// <summary>
        /// RPC调用失败
        /// </summary>
        RpcFailed,
        
        /// <summary>
        /// 同步错误
        /// </summary>
        SynchronizationError,
        
        /// <summary>
        /// 带宽不足
        /// </summary>
        InsufficientBandwidth,
        
        /// <summary>
        /// 协议版本不匹配
        /// </summary>
        ProtocolVersionMismatch,
        
        /// <summary>
        /// 服务器维护中
        /// </summary>
        ServerMaintenance,
        
        /// <summary>
        /// 未知错误
        /// </summary>
        UnknownError
    }
    
    /// <summary>
    /// 错误严重程度
    /// </summary>
    public enum ErrorSeverity
    {
        /// <summary>
        /// 低级错误 - 不影响游戏继续
        /// </summary>
        Low,
        
        /// <summary>
        /// 中级错误 - 可能影响部分功能
        /// </summary>
        Medium,
        
        /// <summary>
        /// 高级错误 - 严重影响游戏体验
        /// </summary>
        High,
        
        /// <summary>
        /// 严重错误 - 导致游戏无法继续
        /// </summary>
        Critical
    }
    
    /// <summary>
    /// 网络错误信息结构
    /// </summary>
    [Serializable]
    public struct NetworkError
    {
        /// <summary>
        /// 错误类型
        /// </summary>
        public ErrorType Type;
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message;
        
        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception Exception;
        
        /// <summary>
        /// 错误发生时间
        /// </summary>
        public float Timestamp;
        
        /// <summary>
        /// 错误严重程度
        /// </summary>
        public ErrorSeverity Severity;
        
        /// <summary>
        /// 错误上下文信息
        /// </summary>
        public string Context;
        
        /// <summary>
        /// 是否可以自动恢复
        /// </summary>
        public bool CanAutoRecover;
        
        /// <summary>
        /// 建议的恢复动作
        /// </summary>
        public RecoveryAction SuggestedAction;
        
        public override string ToString()
        {
            return $"[{Severity}] {Type}: {Message} (Time: {Timestamp:F2}s)";
        }
    }
    
    /// <summary>
    /// 恢复动作类型
    /// </summary>
    public enum RecoveryAction
    {
        /// <summary>
        /// 无需动作
        /// </summary>
        None,
        
        /// <summary>
        /// 重试连接
        /// </summary>
        RetryConnection,
        
        /// <summary>
        /// 重新认证
        /// </summary>
        Reauthenticate,
        
        /// <summary>
        /// 重新同步
        /// </summary>
        Resynchronize,
        
        /// <summary>
        /// 重启网络系统
        /// </summary>
        RestartNetworking,
        
        /// <summary>
        /// 返回主菜单
        /// </summary>
        ReturnToMainMenu,
        
        /// <summary>
        /// 重启应用
        /// </summary>
        RestartApplication
    }
    
    /// <summary>
    /// 网络连接状态
    /// </summary>
    public enum ConnectionState
    {
        /// <summary>
        /// 未连接
        /// </summary>
        Disconnected,
        
        /// <summary>
        /// 连接中
        /// </summary>
        Connecting,
        
        /// <summary>
        /// 已连接
        /// </summary>
        Connected,
        
        /// <summary>
        /// 连接不稳定
        /// </summary>
        Unstable,
        
        /// <summary>
        /// 重连中
        /// </summary>
        Reconnecting,
        
        /// <summary>
        /// 连接失败
        /// </summary>
        Failed
    }
    
    /// <summary>
    /// 网络质量等级
    /// </summary>
    public enum NetworkQuality
    {
        /// <summary>
        /// 优秀
        /// </summary>
        Excellent,
        
        /// <summary>
        /// 良好
        /// </summary>
        Good,
        
        /// <summary>
        /// 一般
        /// </summary>
        Fair,
        
        /// <summary>
        /// 较差
        /// </summary>
        Poor,
        
        /// <summary>
        /// 很差
        /// </summary>
        VeryPoor
    }
    
    /// <summary>
    /// 网络诊断信息
    /// </summary>
    [Serializable]
    public struct NetworkDiagnostics
    {
        /// <summary>
        /// 连接状态
        /// </summary>
        public ConnectionState State;
        
        /// <summary>
        /// 网络质量
        /// </summary>
        public NetworkQuality Quality;
        
        /// <summary>
        /// 延迟 (毫秒)
        /// </summary>
        public float Latency;
        
        /// <summary>
        /// 丢包率 (0-1)
        /// </summary>
        public float PacketLoss;
        
        /// <summary>
        /// 带宽使用 (KB/s)
        /// </summary>
        public float BandwidthUsage;
        
        /// <summary>
        /// 连接稳定性 (0-1)
        /// </summary>
        public float Stability;
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public float LastUpdateTime;
        
        /// <summary>
        /// 获取网络质量描述
        /// </summary>
        public string GetQualityDescription()
        {
            switch (Quality)
            {
                case NetworkQuality.Excellent:
                    return "网络状况优秀";
                case NetworkQuality.Good:
                    return "网络状况良好";
                case NetworkQuality.Fair:
                    return "网络状况一般";
                case NetworkQuality.Poor:
                    return "网络状况较差";
                case NetworkQuality.VeryPoor:
                    return "网络状况很差";
                default:
                    return "网络状况未知";
            }
        }
        
        /// <summary>
        /// 获取连接状态描述
        /// </summary>
        public string GetStateDescription()
        {
            switch (State)
            {
                case ConnectionState.Disconnected:
                    return "未连接";
                case ConnectionState.Connecting:
                    return "连接中";
                case ConnectionState.Connected:
                    return "已连接";
                case ConnectionState.Unstable:
                    return "连接不稳定";
                case ConnectionState.Reconnecting:
                    return "重连中";
                case ConnectionState.Failed:
                    return "连接失败";
                default:
                    return "状态未知";
            }
        }
    }
    
    /// <summary>
    /// 错误恢复配置
    /// </summary>
    [Serializable]
    public struct ErrorRecoveryConfig
    {
        /// <summary>
        /// 是否启用自动恢复
        /// </summary>
        public bool EnableAutoRecovery;
        
        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryAttempts;
        
        /// <summary>
        /// 重试延迟 (秒)
        /// </summary>
        public float RetryDelay;
        
        /// <summary>
        /// 重试延迟倍数
        /// </summary>
        public float RetryDelayMultiplier;
        
        /// <summary>
        /// 最大重试延迟 (秒)
        /// </summary>
        public float MaxRetryDelay;
        
        /// <summary>
        /// 连接超时时间 (秒)
        /// </summary>
        public float ConnectionTimeout;
        
        /// <summary>
        /// 心跳间隔 (秒)
        /// </summary>
        public float HeartbeatInterval;
        
        /// <summary>
        /// 最大连续失败次数
        /// </summary>
        public int MaxConsecutiveFailures;
        
        /// <summary>
        /// 默认配置
        /// </summary>
        public static ErrorRecoveryConfig Default => new ErrorRecoveryConfig
        {
            EnableAutoRecovery = true,
            MaxRetryAttempts = 3,
            RetryDelay = 2f,
            RetryDelayMultiplier = 1.5f,
            MaxRetryDelay = 30f,
            ConnectionTimeout = 10f,
            HeartbeatInterval = 5f,
            MaxConsecutiveFailures = 3
        };
    }
}
