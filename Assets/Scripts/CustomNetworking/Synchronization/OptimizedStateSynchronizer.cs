using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Synchronization
{
    /// <summary>
    /// 优化的状态同步器 - 高性能的网络状态同步系统
    /// </summary>
    public class OptimizedStateSynchronizer : MonoBehaviour
    {
        [Header("同步配置")]
        [SerializeField] private float syncRate = 20f; // 同步频率 Hz
        [SerializeField] private float compressionThreshold = 0.01f; // 压缩阈值
        [SerializeField] private bool enableDeltaCompression = true; // 增量压缩
        [SerializeField] private bool enablePrioritySystem = true; // 优先级系统

        [Header("性能优化")]
        [SerializeField] private int maxSyncObjectsPerFrame = 10; // 每帧最大同步对象数
        [SerializeField] private float cullingDistance = 100f; // 剔除距离
        [SerializeField] private bool enableLODSystem = true; // LOD系统

        [Header("调试")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool logSyncStats = false;

        #region 私有字段

        private Dictionary<NetworkId, OptimizedSyncObjectData> _syncObjects = new Dictionary<NetworkId, OptimizedSyncObjectData>();
        private PriorityQueue<SyncTask> _syncQueue = new PriorityQueue<SyncTask>();
        private SyncStatistics _statistics = new SyncStatistics();

        private float _lastSyncTime;
        private float _syncInterval;
        private int _currentFrameSyncCount;

        // 状态缓存
        private Dictionary<NetworkId, NetworkState> _previousStates = new Dictionary<NetworkId, NetworkState>();
        private Dictionary<NetworkId, NetworkState> _currentStates = new Dictionary<NetworkId, NetworkState>();

        #endregion

        #region Unity生命周期

        private void Start()
        {
            _syncInterval = 1f / syncRate;
            _lastSyncTime = Time.time;
        }

        private void Update()
        {
            ProcessSynchronization();

            if (showDebugInfo)
            {
                UpdateDebugInfo();
            }
        }

        private void LateUpdate()
        {
            // 重置每帧计数器
            _currentFrameSyncCount = 0;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 注册需要同步的网络对象
        /// </summary>
        public void RegisterSyncObject(NetworkBehaviour networkBehaviour, SyncPriority priority = SyncPriority.Normal)
        {
            if (networkBehaviour?.Object == null)
                return;

            var objectId = networkBehaviour.Object.Id;

            if (!_syncObjects.ContainsKey(objectId))
            {
                var syncData = new OptimizedSyncObjectData
                {
                    NetworkBehaviour = networkBehaviour,
                    Priority = priority,
                    LastSyncTime = Time.time,
                    SyncCount = 0,
                    IsActive = true
                };

                _syncObjects[objectId] = syncData;

                // 初始化状态
                _previousStates[objectId] = CaptureState(networkBehaviour);
                _currentStates[objectId] = CaptureState(networkBehaviour);

                UnityEngine.Debug.Log($"[StateSynchronizer] Registered sync object: {networkBehaviour.name} (ID: {objectId})");
            }
        }

        /// <summary>
        /// 取消注册网络对象
        /// </summary>
        public void UnregisterSyncObject(NetworkBehaviour networkBehaviour)
        {
            if (networkBehaviour?.Object == null)
                return;

            var objectId = networkBehaviour.Object.Id;

            if (_syncObjects.Remove(objectId))
            {
                _previousStates.Remove(objectId);
                _currentStates.Remove(objectId);

                UnityEngine.Debug.Log($"[StateSynchronizer] Unregistered sync object: {networkBehaviour.name} (ID: {objectId})");
            }
        }

        /// <summary>
        /// 强制同步指定对象
        /// </summary>
        public void ForceSyncObject(NetworkId objectId)
        {
            if (_syncObjects.TryGetValue(objectId, out var syncData))
            {
                var syncTask = new SyncTask
                {
                    ObjectId = objectId,
                    Priority = SyncPriority.Critical,
                    Timestamp = Time.time,
                    ForceSync = true
                };

                _syncQueue.Enqueue(syncTask);
            }
        }

        /// <summary>
        /// 获取同步统计信息
        /// </summary>
        public SyncStatistics GetStatistics()
        {
            return _statistics;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.Reset();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 处理同步逻辑
        /// </summary>
        private void ProcessSynchronization()
        {
            float currentTime = Time.time;

            // 检查是否到了同步时间
            if (currentTime - _lastSyncTime < _syncInterval)
                return;

            _lastSyncTime = currentTime;

            // 更新所有对象的状态
            UpdateObjectStates();

            // 处理同步队列
            ProcessSyncQueue();

            // 更新统计信息
            _statistics.TotalSyncFrames++;
        }

        /// <summary>
        /// 更新对象状态
        /// </summary>
        private void UpdateObjectStates()
        {
            foreach (var kvp in _syncObjects)
            {
                var objectId = kvp.Key;
                var syncData = kvp.Value;

                if (!syncData.IsActive || syncData.NetworkBehaviour == null)
                    continue;

                // 捕获当前状态
                var currentState = CaptureState(syncData.NetworkBehaviour);
                var previousState = _currentStates.GetValueOrDefault(objectId);

                // 检查是否需要同步
                if (ShouldSync(objectId, currentState, previousState, syncData))
                {
                    var syncTask = new SyncTask
                    {
                        ObjectId = objectId,
                        Priority = CalculateSyncPriority(objectId, syncData),
                        Timestamp = Time.time,
                        ForceSync = false
                    };

                    _syncQueue.Enqueue(syncTask);
                }

                // 更新状态缓存
                _previousStates[objectId] = _currentStates[objectId];
                _currentStates[objectId] = currentState;
            }
        }

        /// <summary>
        /// 处理同步队列
        /// </summary>
        private void ProcessSyncQueue()
        {
            int processedCount = 0;

            while (_syncQueue.Count > 0 && processedCount < maxSyncObjectsPerFrame)
            {
                var syncTask = _syncQueue.Dequeue();

                if (_syncObjects.TryGetValue(syncTask.ObjectId, out var syncData))
                {
                    ExecuteSync(syncTask.ObjectId, syncData);
                    processedCount++;
                    _currentFrameSyncCount++;
                }
            }

            _statistics.ObjectsSyncedThisFrame = processedCount;
        }

        /// <summary>
        /// 执行同步
        /// </summary>
        private void ExecuteSync(NetworkId objectId, OptimizedSyncObjectData syncData)
        {
            try
            {
                var currentState = _currentStates[objectId];
                var previousState = _previousStates[objectId];

                // 创建同步数据
                var syncMessage = CreateSyncMessage(objectId, currentState, previousState);

                // 发送同步消息
                SendSyncMessage(syncMessage);

                // 更新同步数据
                syncData.LastSyncTime = Time.time;
                syncData.SyncCount++;

                _statistics.TotalSyncMessages++;
                _statistics.TotalBytesSent += syncMessage.GetSize();
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[StateSynchronizer] Failed to sync object {objectId}: {ex.Message}");
                _statistics.SyncErrors++;
            }
        }

        /// <summary>
        /// 判断是否需要同步
        /// </summary>
        private bool ShouldSync(NetworkId objectId, NetworkState currentState, NetworkState previousState, OptimizedSyncObjectData syncData)
        {
            // 强制同步
            if (syncData.Priority == SyncPriority.Critical)
                return true;

            // 状态变化检测
            if (!StateEquals(currentState, previousState, compressionThreshold))
                return true;

            // 距离剔除
            if (enableLODSystem && !IsWithinSyncDistance(syncData.NetworkBehaviour))
                return false;

            // 时间间隔检测
            float timeSinceLastSync = Time.time - syncData.LastSyncTime;
            float requiredInterval = GetSyncInterval(syncData.Priority);

            return timeSinceLastSync >= requiredInterval;
        }

        /// <summary>
        /// 计算同步优先级
        /// </summary>
        private SyncPriority CalculateSyncPriority(NetworkId objectId, OptimizedSyncObjectData syncData)
        {
            if (enablePrioritySystem)
            {
                // 基于距离和重要性计算优先级
                float distance = GetDistanceToLocalPlayer(syncData.NetworkBehaviour);

                if (distance < 10f)
                    return SyncPriority.High;
                else if (distance < 50f)
                    return SyncPriority.Normal;
                else
                    return SyncPriority.Low;
            }

            return syncData.Priority;
        }

        /// <summary>
        /// 捕获对象状态
        /// </summary>
        private NetworkState CaptureState(NetworkBehaviour networkBehaviour)
        {
            var transform = networkBehaviour.transform;

            return new NetworkState
            {
                Position = transform.position,
                Rotation = transform.rotation,
                Scale = transform.localScale,
                Timestamp = Time.time
            };
        }

        /// <summary>
        /// 比较状态是否相等
        /// </summary>
        private bool StateEquals(NetworkState state1, NetworkState state2, float threshold)
        {
            return Vector3.Distance(state1.Position, state2.Position) < threshold &&
                   Quaternion.Angle(state1.Rotation, state2.Rotation) < threshold * 10f &&
                   Vector3.Distance(state1.Scale, state2.Scale) < threshold;
        }

        /// <summary>
        /// 检查是否在同步距离内
        /// </summary>
        private bool IsWithinSyncDistance(NetworkBehaviour networkBehaviour)
        {
            float distance = GetDistanceToLocalPlayer(networkBehaviour);
            return distance <= cullingDistance;
        }

        /// <summary>
        /// 获取到本地玩家的距离
        /// </summary>
        private float GetDistanceToLocalPlayer(NetworkBehaviour networkBehaviour)
        {
            // 简化实现 - 实际应该获取本地玩家位置
            var localPlayer = Camera.main?.transform;
            if (localPlayer != null)
            {
                return Vector3.Distance(networkBehaviour.transform.position, localPlayer.position);
            }

            return 0f;
        }

        /// <summary>
        /// 获取同步间隔
        /// </summary>
        private float GetSyncInterval(SyncPriority priority)
        {
            switch (priority)
            {
                case SyncPriority.Critical:
                    return 0f; // 立即同步
                case SyncPriority.High:
                    return _syncInterval;
                case SyncPriority.Normal:
                    return _syncInterval * 2f;
                case SyncPriority.Low:
                    return _syncInterval * 4f;
                default:
                    return _syncInterval;
            }
        }

        /// <summary>
        /// 创建同步消息
        /// </summary>
        private SyncMessage CreateSyncMessage(NetworkId objectId, NetworkState currentState, NetworkState previousState)
        {
            var message = new SyncMessage
            {
                ObjectId = objectId,
                Timestamp = Time.time,
                State = currentState
            };

            // 增量压缩
            if (enableDeltaCompression)
            {
                message.DeltaState = CalculateDelta(currentState, previousState);
                message.UseDelta = true;
            }

            return message;
        }

        /// <summary>
        /// 发送同步消息
        /// </summary>
        private void SendSyncMessage(SyncMessage message)
        {
            // 这里应该通过网络连接发送消息
            // 简化实现
            UnityEngine.Debug.Log($"[StateSynchronizer] Sending sync message for object {message.ObjectId}");
        }

        /// <summary>
        /// 计算状态增量
        /// </summary>
        private NetworkState CalculateDelta(NetworkState current, NetworkState previous)
        {
            return new NetworkState
            {
                Position = current.Position - previous.Position,
                Rotation = Quaternion.Inverse(previous.Rotation) * current.Rotation,
                Scale = current.Scale - previous.Scale,
                Timestamp = current.Timestamp
            };
        }

        /// <summary>
        /// 更新调试信息
        /// </summary>
        private void UpdateDebugInfo()
        {
            if (logSyncStats && Time.frameCount % 60 == 0) // 每秒记录一次
            {
                UnityEngine.Debug.Log($"[StateSynchronizer] Stats: {_statistics}");
            }
        }

        #endregion
    }

    #region 数据结构和枚举

    /// <summary>
    /// 同步优先级
    /// </summary>
    public enum SyncPriority
    {
        Low = 0,
        Normal = 1,
        High = 2,
        Critical = 3
    }

    /// <summary>
    /// 优化同步对象数据
    /// </summary>
    [Serializable]
    public class OptimizedSyncObjectData
    {
        public NetworkBehaviour NetworkBehaviour;
        public SyncPriority Priority;
        public float LastSyncTime;
        public int SyncCount;
        public bool IsActive;
    }

    /// <summary>
    /// 同步任务
    /// </summary>
    public class SyncTask : ISyncPriorityQueueItem
    {
        public NetworkId ObjectId;
        public SyncPriority Priority { get; set; }
        public float Timestamp { get; set; }
        public bool ForceSync;

        public int CompareTo(ISyncPriorityQueueItem other)
        {
            if (other == null) return -1;

            // 优先级比较
            int priorityComparison = other.Priority.CompareTo(this.Priority);
            if (priorityComparison != 0)
                return priorityComparison;

            // 时间戳比较
            return this.Timestamp.CompareTo(other.Timestamp);
        }
    }

    /// <summary>
    /// 网络状态
    /// </summary>
    [Serializable]
    public struct NetworkState
    {
        public Vector3 Position;
        public Quaternion Rotation;
        public Vector3 Scale;
        public float Timestamp;
    }

    /// <summary>
    /// 同步消息
    /// </summary>
    [Serializable]
    public class SyncMessage
    {
        public NetworkId ObjectId;
        public float Timestamp;
        public NetworkState State;
        public NetworkState DeltaState;
        public bool UseDelta;

        public int GetSize()
        {
            // 估算消息大小（字节）
            int baseSize = sizeof(float) * 11; // 3个Vector3 + 1个Quaternion + 1个float
            return UseDelta ? baseSize * 2 : baseSize;
        }
    }

    /// <summary>
    /// 同步统计信息
    /// </summary>
    [Serializable]
    public class SyncStatistics
    {
        [Header("同步统计")]
        public long TotalSyncFrames;
        public long TotalSyncMessages;
        public long TotalBytesSent;
        public int ObjectsSyncedThisFrame;

        [Header("性能统计")]
        public float AverageSyncTime;
        public float MaxSyncTime;
        public long SyncErrors;

        [Header("优化统计")]
        public long DeltaCompressionSaves;
        public long CulledObjects;
        public float CompressionRatio;

        public void Reset()
        {
            TotalSyncFrames = 0;
            TotalSyncMessages = 0;
            TotalBytesSent = 0;
            ObjectsSyncedThisFrame = 0;
            AverageSyncTime = 0;
            MaxSyncTime = 0;
            SyncErrors = 0;
            DeltaCompressionSaves = 0;
            CulledObjects = 0;
            CompressionRatio = 0;
        }

        public override string ToString()
        {
            return $"Sync Stats - Frames: {TotalSyncFrames}, Messages: {TotalSyncMessages}, " +
                   $"Bytes: {TotalBytesSent}, Errors: {SyncErrors}, Compression: {CompressionRatio:P1}";
        }
    }

    /// <summary>
    /// 同步优先级队列项接口
    /// </summary>
    public interface ISyncPriorityQueueItem : IComparable<ISyncPriorityQueueItem>
    {
        SyncPriority Priority { get; }
        float Timestamp { get; }
    }

    /// <summary>
    /// 同步优先级队列 - 按优先级和时间戳排序同步任务
    /// </summary>
    public class PriorityQueue<T> where T : ISyncPriorityQueueItem
    {
        private List<T> _items = new List<T>();

        public int Count => _items.Count;

        /// <summary>
        /// 入队
        /// </summary>
        public void Enqueue(T item)
        {
            _items.Add(item);
            HeapifyUp(_items.Count - 1);
        }

        /// <summary>
        /// 出队
        /// </summary>
        public T Dequeue()
        {
            if (_items.Count == 0)
                throw new InvalidOperationException("Queue is empty");

            T result = _items[0];
            _items[0] = _items[_items.Count - 1];
            _items.RemoveAt(_items.Count - 1);

            if (_items.Count > 0)
                HeapifyDown(0);

            return result;
        }

        /// <summary>
        /// 查看队首元素
        /// </summary>
        public T Peek()
        {
            if (_items.Count == 0)
                throw new InvalidOperationException("Queue is empty");

            return _items[0];
        }

        /// <summary>
        /// 清空队列
        /// </summary>
        public void Clear()
        {
            _items.Clear();
        }

        /// <summary>
        /// 检查队列是否为空
        /// </summary>
        public bool IsEmpty => _items.Count == 0;

        private void HeapifyUp(int index)
        {
            while (index > 0)
            {
                int parentIndex = (index - 1) / 2;
                if (_items[index].CompareTo(_items[parentIndex]) >= 0)
                    break;

                Swap(index, parentIndex);
                index = parentIndex;
            }
        }

        private void HeapifyDown(int index)
        {
            while (true)
            {
                int leftChild = 2 * index + 1;
                int rightChild = 2 * index + 2;
                int smallest = index;

                if (leftChild < _items.Count && _items[leftChild].CompareTo(_items[smallest]) < 0)
                    smallest = leftChild;

                if (rightChild < _items.Count && _items[rightChild].CompareTo(_items[smallest]) < 0)
                    smallest = rightChild;

                if (smallest == index)
                    break;

                Swap(index, smallest);
                index = smallest;
            }
        }

        private void Swap(int i, int j)
        {
            T temp = _items[i];
            _items[i] = _items[j];
            _items[j] = temp;
        }
    }

    #endregion
}
