using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Synchronization
{
    /// <summary>
    /// 同步模式枚举
    /// </summary>
    public enum SynchronizationMode
    {
        InterpolationOnly,  // 仅插值
        PredictionOnly,     // 仅预测
        RollbackOnly,       // 仅回滚
        Hybrid,             // 混合模式
        Adaptive            // 自适应模式
    }

    /// <summary>
    /// LOD等级枚举
    /// </summary>
    public enum LODLevel
    {
        High,       // 高精度同步
        Medium,     // 中等精度同步
        Low,        // 低精度同步
        Minimal     // 最小同步
    }

    /// <summary>
    /// 网络同步管理器 - 统一管理插值、预测和回滚
    /// </summary>
    public class NetworkSynchronizationManager : MonoBehaviour
    {
        [Header("同步模式")]
        [SerializeField] private SynchronizationMode syncMode = SynchronizationMode.Hybrid;
        [SerializeField] private bool enableAdaptiveSync = true;
        [SerializeField] private float adaptiveThreshold = 0.1f;
        
        [Header("组件配置")]
        [SerializeField] private bool autoCreateComponents = true;
        [SerializeField] private bool enableInterpolation = true;
        [SerializeField] private bool enablePrediction = true;
        [SerializeField] private bool enableRollback = true;
        
        [Header("性能设置")]
        [SerializeField] private int maxSyncObjects = 100;
        [SerializeField] private float syncUpdateRate = 20f; // Hz
        [SerializeField] private bool enableLODSystem = true;
        [SerializeField] private float[] lodDistances = { 10f, 25f, 50f };
        
        [Header("调试")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool logSyncEvents = false;
        
        // 同步组件
        private NetworkInterpolation _interpolation;
        private NetworkPrediction _prediction;
        private NetworkRollback _rollback;
        
        // 同步状态
        private Dictionary<NetworkObject, SyncObjectData> _syncObjects = new Dictionary<NetworkObject, SyncObjectData>();
        private float _lastSyncTime;
        private int _currentTick;
        
        // 性能监控
        private SyncPerformanceStats _performanceStats;
        private float _lastStatsUpdate;
        
        // 单例实例
        private static NetworkSynchronizationManager _instance;
        public static NetworkSynchronizationManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<NetworkSynchronizationManager>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("NetworkSynchronizationManager");
                        _instance = go.AddComponent<NetworkSynchronizationManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        // 事件
        public event Action<NetworkObject> OnObjectRegistered;
        public event Action<NetworkObject> OnObjectUnregistered;
        public event Action<SynchronizationMode> OnSyncModeChanged;
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeSynchronization();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }
        
        private void Update()
        {
            UpdateSynchronization();
            UpdatePerformanceStats();
            
            if (enableAdaptiveSync)
            {
                UpdateAdaptiveSync();
            }
        }
        
        /// <summary>
        /// 初始化同步系统
        /// </summary>
        private void InitializeSynchronization()
        {
            // 创建同步组件
            if (autoCreateComponents)
            {
                CreateSyncComponents();
            }
            
            // 初始化性能统计
            _performanceStats = new SyncPerformanceStats();
            _lastSyncTime = Time.time;
            _lastStatsUpdate = Time.time;
            _currentTick = 0;
            
            UnityEngine.Debug.Log($"[NetworkSync] 同步管理器已初始化，模式: {syncMode}");
        }
        
        /// <summary>
        /// 创建同步组件
        /// </summary>
        private void CreateSyncComponents()
        {
            if (enableInterpolation && _interpolation == null)
            {
                _interpolation = gameObject.AddComponent<NetworkInterpolation>();
            }
            
            if (enablePrediction && _prediction == null)
            {
                _prediction = gameObject.AddComponent<NetworkPrediction>();
            }
            
            if (enableRollback && _rollback == null)
            {
                _rollback = gameObject.AddComponent<NetworkRollback>();
            }
        }
        
        /// <summary>
        /// 注册同步对象
        /// </summary>
        public void RegisterSyncObject(NetworkObject networkObject)
        {
            if (_syncObjects.ContainsKey(networkObject))
                return;
            
            var syncData = new SyncObjectData
            {
                NetworkObject = networkObject,
                LastSyncTime = Time.time,
                SyncPriority = CalculateSyncPriority(networkObject),
                LODLevel = CalculateLODLevel(networkObject),
                IsActive = true
            };
            
            _syncObjects[networkObject] = syncData;
            
            // 为对象添加同步组件
            SetupSyncComponents(networkObject);
            
            OnObjectRegistered?.Invoke(networkObject);
            
            if (logSyncEvents)
            {
                UnityEngine.Debug.Log($"[NetworkSync] 注册同步对象: {networkObject.name}");
            }
        }
        
        /// <summary>
        /// 注销同步对象
        /// </summary>
        public void UnregisterSyncObject(NetworkObject networkObject)
        {
            if (!_syncObjects.ContainsKey(networkObject))
                return;
            
            _syncObjects.Remove(networkObject);
            OnObjectUnregistered?.Invoke(networkObject);
            
            if (logSyncEvents)
            {
                UnityEngine.Debug.Log($"[NetworkSync] 注销同步对象: {networkObject.name}");
            }
        }
        
        /// <summary>
        /// 为对象设置同步组件
        /// </summary>
        private void SetupSyncComponents(NetworkObject networkObject)
        {
            var gameObj = networkObject.gameObject;
            
            switch (syncMode)
            {
                case SynchronizationMode.InterpolationOnly:
                    if (enableInterpolation && gameObj.GetComponent<NetworkInterpolation>() == null)
                    {
                        gameObj.AddComponent<NetworkInterpolation>();
                    }
                    break;
                    
                case SynchronizationMode.PredictionOnly:
                    if (enablePrediction && gameObj.GetComponent<NetworkPrediction>() == null)
                    {
                        gameObj.AddComponent<NetworkPrediction>();
                    }
                    break;
                    
                case SynchronizationMode.RollbackOnly:
                    if (enableRollback && gameObj.GetComponent<NetworkRollback>() == null)
                    {
                        gameObj.AddComponent<NetworkRollback>();
                    }
                    break;
                    
                case SynchronizationMode.Hybrid:
                case SynchronizationMode.Adaptive:
                    // 添加所有组件
                    if (enableInterpolation && gameObj.GetComponent<NetworkInterpolation>() == null)
                    {
                        gameObj.AddComponent<NetworkInterpolation>();
                    }
                    if (enablePrediction && gameObj.GetComponent<NetworkPrediction>() == null)
                    {
                        gameObj.AddComponent<NetworkPrediction>();
                    }
                    if (enableRollback && gameObj.GetComponent<NetworkRollback>() == null)
                    {
                        gameObj.AddComponent<NetworkRollback>();
                    }
                    break;
            }
        }
        
        /// <summary>
        /// 更新同步
        /// </summary>
        private void UpdateSynchronization()
        {
            float currentTime = Time.time;
            float deltaTime = currentTime - _lastSyncTime;
            
            // 检查是否需要同步更新
            if (deltaTime >= 1f / syncUpdateRate)
            {
                ProcessSynchronization();
                _lastSyncTime = currentTime;
                _currentTick++;
            }
        }
        
        /// <summary>
        /// 处理同步
        /// </summary>
        private void ProcessSynchronization()
        {
            var objectsToSync = GetObjectsToSync();
            
            foreach (var syncData in objectsToSync)
            {
                ProcessObjectSync(syncData);
            }
            
            // 更新性能统计
            _performanceStats.SyncedObjectsCount = objectsToSync.Count;
            _performanceStats.TotalSyncOperations++;
        }
        
        /// <summary>
        /// 获取需要同步的对象
        /// </summary>
        private List<SyncObjectData> GetObjectsToSync()
        {
            var objectsToSync = new List<SyncObjectData>();
            
            foreach (var kvp in _syncObjects)
            {
                var syncData = kvp.Value;
                
                if (syncData.IsActive && ShouldSyncObject(syncData))
                {
                    objectsToSync.Add(syncData);
                }
            }
            
            // 按优先级排序
            objectsToSync.Sort((a, b) => b.SyncPriority.CompareTo(a.SyncPriority));
            
            // 限制同步对象数量
            if (objectsToSync.Count > maxSyncObjects)
            {
                objectsToSync = objectsToSync.GetRange(0, maxSyncObjects);
            }
            
            return objectsToSync;
        }
        
        /// <summary>
        /// 检查是否应该同步对象
        /// </summary>
        private bool ShouldSyncObject(SyncObjectData syncData)
        {
            // 检查LOD距离
            if (enableLODSystem)
            {
                float distance = GetDistanceToLocalPlayer(syncData.NetworkObject);
                LODLevel requiredLOD = GetLODLevelForDistance(distance);
                
                if (requiredLOD > syncData.LODLevel)
                {
                    return false; // LOD等级不够，跳过同步
                }
            }
            
            // 检查同步频率
            float timeSinceLastSync = Time.time - syncData.LastSyncTime;
            float requiredInterval = GetSyncIntervalForLOD(syncData.LODLevel);
            
            return timeSinceLastSync >= requiredInterval;
        }
        
        /// <summary>
        /// 处理对象同步
        /// </summary>
        private void ProcessObjectSync(SyncObjectData syncData)
        {
            var networkObject = syncData.NetworkObject;
            
            // 根据同步模式处理
            switch (syncMode)
            {
                case SynchronizationMode.InterpolationOnly:
                    ProcessInterpolationSync(networkObject);
                    break;
                    
                case SynchronizationMode.PredictionOnly:
                    ProcessPredictionSync(networkObject);
                    break;
                    
                case SynchronizationMode.RollbackOnly:
                    ProcessRollbackSync(networkObject);
                    break;
                    
                case SynchronizationMode.Hybrid:
                    ProcessHybridSync(networkObject);
                    break;
                    
                case SynchronizationMode.Adaptive:
                    ProcessAdaptiveSync(networkObject);
                    break;
            }
            
            // 更新同步时间
            syncData.LastSyncTime = Time.time;
            _syncObjects[networkObject] = syncData;
        }
        
        /// <summary>
        /// 处理插值同步
        /// </summary>
        private void ProcessInterpolationSync(NetworkObject networkObject)
        {
            var interpolation = networkObject.GetComponent<NetworkInterpolation>();
            if (interpolation != null)
            {
                // 添加网络快照
                var snapshot = new NetworkSnapshot
                {
                    Position = networkObject.transform.position,
                    Rotation = networkObject.transform.rotation,
                    Scale = networkObject.transform.localScale,
                    Timestamp = Time.time
                };
                
                interpolation.AddSnapshot(snapshot);
            }
        }
        
        /// <summary>
        /// 处理预测同步
        /// </summary>
        private void ProcessPredictionSync(NetworkObject networkObject)
        {
            var prediction = networkObject.GetComponent<NetworkPrediction>();
            if (prediction != null && networkObject.HasInputAuthority)
            {
                // 处理客户端预测
                // 这里可以添加具体的预测逻辑
            }
        }
        
        /// <summary>
        /// 处理回滚同步
        /// </summary>
        private void ProcessRollbackSync(NetworkObject networkObject)
        {
            var rollback = networkObject.GetComponent<NetworkRollback>();
            if (rollback != null)
            {
                // 保存当前状态
                rollback.SaveCurrentState();
            }
        }
        
        /// <summary>
        /// 处理混合同步
        /// </summary>
        private void ProcessHybridSync(NetworkObject networkObject)
        {
            if (networkObject.HasInputAuthority)
            {
                // 有输入权限的对象使用预测
                ProcessPredictionSync(networkObject);
            }
            else
            {
                // 其他对象使用插值
                ProcessInterpolationSync(networkObject);
            }
        }
        
        /// <summary>
        /// 处理自适应同步
        /// </summary>
        private void ProcessAdaptiveSync(NetworkObject networkObject)
        {
            // 根据网络条件选择最佳同步方式
            var networkManager = GetNetworkManager();
            if (networkManager != null)
            {
                var diagnostics = networkManager.GetNetworkDiagnostics();
                
                if (diagnostics.Latency > 100f || diagnostics.PacketLoss > 0.05f)
                {
                    // 网络条件差，使用回滚
                    ProcessRollbackSync(networkObject);
                }
                else if (networkObject.HasInputAuthority)
                {
                    // 网络条件好且有输入权限，使用预测
                    ProcessPredictionSync(networkObject);
                }
                else
                {
                    // 其他情况使用插值
                    ProcessInterpolationSync(networkObject);
                }
            }
            else
            {
                // 默认使用混合模式
                ProcessHybridSync(networkObject);
            }
        }
        
        /// <summary>
        /// 更新自适应同步
        /// </summary>
        private void UpdateAdaptiveSync()
        {
            if (syncMode != SynchronizationMode.Adaptive) return;
            
            // 根据网络状况动态调整同步参数
            var networkManager = GetNetworkManager();
            if (networkManager != null)
            {
                var diagnostics = networkManager.GetNetworkDiagnostics();
                
                // 根据延迟调整同步频率
                if (diagnostics.Latency > 150f)
                {
                    syncUpdateRate = Mathf.Max(10f, syncUpdateRate * 0.8f);
                }
                else if (diagnostics.Latency < 50f)
                {
                    syncUpdateRate = Mathf.Min(30f, syncUpdateRate * 1.1f);
                }
            }
        }
        
        /// <summary>
        /// 计算同步优先级
        /// </summary>
        private float CalculateSyncPriority(NetworkObject networkObject)
        {
            float priority = 1f;
            
            // 玩家对象优先级最高 - 使用字符串比较避免直接类型引用
            if (networkObject.GetComponent("PlayerController") != null)
            {
                priority = 10f;
            }
            
            // 根据距离调整优先级
            float distance = GetDistanceToLocalPlayer(networkObject);
            priority *= Mathf.Max(0.1f, 1f / (1f + distance * 0.1f));
            
            return priority;
        }
        
        /// <summary>
        /// 计算LOD等级
        /// </summary>
        private LODLevel CalculateLODLevel(NetworkObject networkObject)
        {
            if (!enableLODSystem) return LODLevel.High;
            
            float distance = GetDistanceToLocalPlayer(networkObject);
            return GetLODLevelForDistance(distance);
        }
        
        /// <summary>
        /// 根据距离获取LOD等级
        /// </summary>
        private LODLevel GetLODLevelForDistance(float distance)
        {
            if (distance <= lodDistances[0])
                return LODLevel.High;
            else if (distance <= lodDistances[1])
                return LODLevel.Medium;
            else if (distance <= lodDistances[2])
                return LODLevel.Low;
            else
                return LODLevel.Minimal;
        }
        
        /// <summary>
        /// 获取LOD对应的同步间隔
        /// </summary>
        private float GetSyncIntervalForLOD(LODLevel lod)
        {
            switch (lod)
            {
                case LODLevel.High: return 1f / 30f;    // 30 FPS
                case LODLevel.Medium: return 1f / 20f;  // 20 FPS
                case LODLevel.Low: return 1f / 10f;     // 10 FPS
                case LODLevel.Minimal: return 1f / 5f;  // 5 FPS
                default: return 1f / 20f;
            }
        }
        
        /// <summary>
        /// 获取到本地玩家的距离
        /// </summary>
        private float GetDistanceToLocalPlayer(NetworkObject networkObject)
        {
            // 简化实现：假设本地玩家在原点
            return Vector3.Distance(networkObject.transform.position, Vector3.zero);
        }
        
        /// <summary>
        /// 更新性能统计
        /// </summary>
        private void UpdatePerformanceStats()
        {
            float currentTime = Time.time;
            
            if (currentTime - _lastStatsUpdate >= 1f)
            {
                _performanceStats.SyncRate = _performanceStats.TotalSyncOperations;
                _performanceStats.TotalSyncOperations = 0;
                _lastStatsUpdate = currentTime;
            }
        }
        
        /// <summary>
        /// 设置同步模式
        /// </summary>
        public void SetSynchronizationMode(SynchronizationMode mode)
        {
            if (syncMode != mode)
            {
                syncMode = mode;
                OnSyncModeChanged?.Invoke(mode);
                
                // 重新配置所有对象的同步组件
                foreach (var networkObject in _syncObjects.Keys)
                {
                    SetupSyncComponents(networkObject);
                }
                
                UnityEngine.Debug.Log($"[NetworkSync] 同步模式已更改为: {mode}");
            }
        }
        
        /// <summary>
        /// 获取性能统计
        /// </summary>
        public SyncPerformanceStats GetPerformanceStats()
        {
            return _performanceStats;
        }
        
        /// <summary>
        /// 获取同步对象数量
        /// </summary>
        public int GetSyncObjectCount()
        {
            return _syncObjects.Count;
        }

        /// <summary>
        /// 获取网络管理器实例
        /// </summary>
        private INetworkManager GetNetworkManager()
        {
            // 查找实现了INetworkManager接口的组件
            var allComponents = FindObjectsOfType<MonoBehaviour>();
            foreach (var component in allComponents)
            {
                if (component is INetworkManager networkManager)
                {
                    return networkManager;
                }
            }
            return null;
        }
        
        /// <summary>
        /// 屏幕显示调试信息
        /// </summary>
        private void OnGUI()
        {
            if (!showDebugInfo) return;
            
            GUILayout.BeginArea(new Rect(10, 400, 300, 200));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("网络同步管理器", GUI.skin.label);
            GUILayout.Label($"同步模式: {syncMode}");
            GUILayout.Label($"同步对象: {_syncObjects.Count}");
            GUILayout.Label($"同步频率: {syncUpdateRate:F1} Hz");
            GUILayout.Label($"当前Tick: {_currentTick}");
            GUILayout.Label($"同步率: {_performanceStats.SyncRate}/s");
            
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
    }
    
    /// <summary>
    /// 同步对象数据
    /// </summary>
    [Serializable]
    public struct SyncObjectData
    {
        public NetworkObject NetworkObject;
        public float LastSyncTime;
        public float SyncPriority;
        public LODLevel LODLevel;
        public bool IsActive;
    }
    
    /// <summary>
    /// 同步性能统计
    /// </summary>
    [Serializable]
    public struct SyncPerformanceStats
    {
        public int SyncedObjectsCount;
        public int TotalSyncOperations;
        public float SyncRate;
        public float AverageLatency;
        public float PacketLoss;
    }
}
