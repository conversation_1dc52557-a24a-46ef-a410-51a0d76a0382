using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.WebSocket
{
    /// <summary>
    /// WebSocket管理器 - 实现文档中描述的WebSocket通信功能
    /// </summary>
    public class WebSocketManager : MonoBehaviour
    {
        #region 配置参数
        
        [Header("WebSocket Configuration")]
        [SerializeField] private string serverUrl = "ws://localhost:8080";
        [SerializeField] private float connectionTimeout = 10f;
        [SerializeField] private float heartbeatInterval = 30f;
        [SerializeField] private int maxReconnectAttempts = 5;
        [SerializeField] private float reconnectDelay = 2f;
        [SerializeField] private bool autoReconnect = true;
        
        #endregion
        
        #region 状态管理
        
        public bool IsConnected { get; private set; }
        public bool IsConnecting { get; private set; }
        public string CurrentGameId { get; private set; }
        
        // 连接状态
        private WebSocketConnection _connection;
        private int _reconnectAttempts;
        private float _lastHeartbeat;
        private float _lastPingTime;
        private float _currentLatency;
        
        // 消息队列
        private Queue<WebSocketMessage> _outgoingMessages = new Queue<WebSocketMessage>();
        private Queue<WebSocketMessage> _incomingMessages = new Queue<WebSocketMessage>();
        
        // 消息处理器
        private Dictionary<string, Action<WebSocketMessage>> _messageHandlers = new Dictionary<string, Action<WebSocketMessage>>();
        
        // 网络组件
        private NetworkRunner _runner;
        
        #endregion
        
        #region 事件
        
        public event Action OnConnected;
        public event Action OnDisconnected;
        public event Action<string> OnGameJoined;
        public event Action<string> OnGameLeft;
        public event Action<string, string> OnHostChanged; // oldHost, newHost
        public event Action<WebSocketMessage> OnMessageReceived;
        public event Action<string> OnError;
        
        #endregion
        
        #region Unity生命周期
        
        private void Start()
        {
            InitializeWebSocketManager();
        }
        
        private void Update()
        {
            ProcessMessages();
            UpdateHeartbeat();
            UpdateConnection();
        }
        
        private void OnDestroy()
        {
            Cleanup();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeWebSocketManager()
        {
            _runner = FindFirstObjectByType<NetworkRunner>();
            if (_runner == null)
            {
                UnityEngine.Debug.LogError("NetworkRunner not found! WebSocketManager requires NetworkRunner.");
                return;
            }

            // 注册默认消息处理器
            RegisterDefaultHandlers();

            UnityEngine.Debug.Log("WebSocket Manager initialized");
        }
        
        private void RegisterDefaultHandlers()
        {
            RegisterHandler("ping", HandlePing);
            RegisterHandler("pong", HandlePong);
            RegisterHandler("game_joined", HandleGameJoined);
            RegisterHandler("game_left", HandleGameLeft);
            RegisterHandler("host_changed", HandleHostChanged);
            RegisterHandler("player_joined", HandlePlayerJoined);
            RegisterHandler("player_left", HandlePlayerLeft);
            RegisterHandler("game_state_update", HandleGameStateUpdate);
        }
        
        private void Cleanup()
        {
            if (_connection != null)
            {
                _connection.Disconnect();
                _connection = null;
            }
            
            _messageHandlers.Clear();
            _outgoingMessages.Clear();
            _incomingMessages.Clear();
        }
        
        #endregion
        
        #region 连接管理
        
        /// <summary>
        /// 连接到服务器
        /// </summary>
        public void ConnectToServer()
        {
            if (IsConnected || IsConnecting)
                return;
                
            StartCoroutine(ConnectCoroutine());
        }
        
        /// <summary>
        /// 断开连接
        /// </summary>
        public void DisconnectFromServer()
        {
            if (_connection != null)
            {
                _connection.Disconnect();
            }
            
            IsConnected = false;
            IsConnecting = false;
            _reconnectAttempts = 0;
            
            OnDisconnected?.Invoke();
            UnityEngine.Debug.Log("Disconnected from WebSocket server");
        }
        
        /// <summary>
        /// 连接协程
        /// </summary>
        private IEnumerator ConnectCoroutine()
        {
            IsConnecting = true;

            UnityEngine.Debug.Log($"Connecting to WebSocket server: {serverUrl}");

            _connection = new WebSocketConnection(serverUrl);
            _connection.OnConnected += HandleConnectionEstablished;
            _connection.OnDisconnected += HandleConnectionLost;
            _connection.OnMessageReceived += HandleMessageReceived;
            _connection.OnError += HandleConnectionError;

            yield return _connection.Connect();

            if (_connection.IsConnected)
            {
                IsConnected = true;
                IsConnecting = false;
                _reconnectAttempts = 0;
                _lastHeartbeat = Time.time;

                OnConnected?.Invoke();
                UnityEngine.Debug.Log("Successfully connected to WebSocket server");
            }
            else
            {
                // Handle connection failure
                IsConnecting = false;
                UnityEngine.Debug.LogError("WebSocket connection failed: Failed to establish connection");
                OnError?.Invoke("Connection failed: Failed to establish connection");

                if (autoReconnect && _reconnectAttempts < maxReconnectAttempts)
                {
                    StartCoroutine(ReconnectCoroutine());
                }
            }
        }
        
        /// <summary>
        /// 重连协程
        /// </summary>
        private IEnumerator ReconnectCoroutine()
        {
            _reconnectAttempts++;
            UnityEngine.Debug.Log($"Attempting to reconnect ({_reconnectAttempts}/{maxReconnectAttempts})...");
            
            yield return new WaitForSeconds(reconnectDelay * _reconnectAttempts);
            
            if (!IsConnected && !IsConnecting)
            {
                ConnectToServer();
            }
        }
        
        #endregion
        
        #region 游戏会话管理
        
        /// <summary>
        /// 加入游戏
        /// </summary>
        public void JoinGame(string gameId, bool asHost = false)
        {
            if (!IsConnected)
            {
                UnityEngine.Debug.LogWarning("Cannot join game: not connected to server");
                return;
            }
            
            var message = new WebSocketMessage
            {
                Type = "join_game",
                Data = new Dictionary<string, object>
                {
                    ["game_id"] = gameId,
                    ["player_id"] = _runner.LocalPlayer.ToString(),
                    ["as_host"] = asHost
                }
            };
            
            SendMessage(message);
            CurrentGameId = gameId;

            UnityEngine.Debug.Log($"Requesting to join game: {gameId} (as host: {asHost})");
        }
        
        /// <summary>
        /// 离开游戏
        /// </summary>
        public void LeaveGame()
        {
            if (!IsConnected || string.IsNullOrEmpty(CurrentGameId))
                return;
                
            var message = new WebSocketMessage
            {
                Type = "leave_game",
                Data = new Dictionary<string, object>
                {
                    ["game_id"] = CurrentGameId,
                    ["player_id"] = _runner.LocalPlayer.ToString()
                }
            };
            
            SendMessage(message);

            UnityEngine.Debug.Log($"Requesting to leave game: {CurrentGameId}");
        }
        
        #endregion
        
        #region 消息处理
        
        /// <summary>
        /// 发送消息
        /// </summary>
        public void SendMessage(WebSocketMessage message)
        {
            if (!IsConnected)
            {
                UnityEngine.Debug.LogWarning("Cannot send message: not connected to server");
                return;
            }
            
            message.Timestamp = Time.time;
            message.SenderId = _runner.LocalPlayer.ToString();
            
            _outgoingMessages.Enqueue(message);
        }
        
        /// <summary>
        /// 注册消息处理器
        /// </summary>
        public void RegisterHandler(string messageType, Action<WebSocketMessage> handler)
        {
            _messageHandlers[messageType] = handler;
        }
        
        /// <summary>
        /// 取消注册消息处理器
        /// </summary>
        public void UnregisterHandler(string messageType)
        {
            _messageHandlers.Remove(messageType);
        }
        
        /// <summary>
        /// 处理消息队列
        /// </summary>
        private void ProcessMessages()
        {
            // 处理发送队列
            while (_outgoingMessages.Count > 0 && IsConnected)
            {
                var message = _outgoingMessages.Dequeue();
                _connection.SendMessage(message);
            }
            
            // 处理接收队列
            while (_incomingMessages.Count > 0)
            {
                var message = _incomingMessages.Dequeue();
                ProcessIncomingMessage(message);
            }
        }
        
        /// <summary>
        /// 处理接收到的消息
        /// </summary>
        private void ProcessIncomingMessage(WebSocketMessage message)
        {
            if (_messageHandlers.TryGetValue(message.Type, out var handler))
            {
                try
                {
                    handler.Invoke(message);
                }
                catch (Exception ex)
                {
                    UnityEngine.Debug.LogError($"Error processing message {message.Type}: {ex.Message}");
                }
            }
            else
            {
                UnityEngine.Debug.LogWarning($"No handler registered for message type: {message.Type}");
            }
            
            OnMessageReceived?.Invoke(message);
        }
        
        #endregion
        
        #region 心跳管理
        
        private void UpdateHeartbeat()
        {
            if (!IsConnected)
                return;
                
            if (Time.time - _lastHeartbeat >= heartbeatInterval)
            {
                SendPing();
                _lastHeartbeat = Time.time;
            }
        }
        
        private void SendPing()
        {
            _lastPingTime = Time.time;
            
            var pingMessage = new WebSocketMessage
            {
                Type = "ping",
                Data = new Dictionary<string, object>
                {
                    ["timestamp"] = _lastPingTime
                }
            };
            
            SendMessage(pingMessage);
        }
        
        #endregion
        
        #region 连接事件处理
        
        private void HandleConnectionEstablished()
        {
            UnityEngine.Debug.Log("WebSocket connection established");
        }

        private void HandleConnectionLost()
        {
            IsConnected = false;
            OnDisconnected?.Invoke();

            UnityEngine.Debug.LogWarning("WebSocket connection lost");
            
            if (autoReconnect && _reconnectAttempts < maxReconnectAttempts)
            {
                StartCoroutine(ReconnectCoroutine());
            }
        }
        
        private void HandleMessageReceived(WebSocketMessage message)
        {
            _incomingMessages.Enqueue(message);
        }
        
        private void HandleConnectionError(string error)
        {
            UnityEngine.Debug.LogError($"WebSocket connection error: {error}");
            OnError?.Invoke(error);
        }
        
        #endregion
        
        #region 默认消息处理器
        
        private void HandlePing(WebSocketMessage message)
        {
            // 响应ping消息
            var pongMessage = new WebSocketMessage
            {
                Type = "pong",
                Data = message.Data
            };
            
            SendMessage(pongMessage);
        }
        
        private void HandlePong(WebSocketMessage message)
        {
            // 计算延迟
            if (message.Data.TryGetValue("timestamp", out var timestampObj) && timestampObj is float timestamp)
            {
                _currentLatency = Time.time - timestamp;
                UnityEngine.Debug.Log($"WebSocket latency: {_currentLatency * 1000:F1}ms");
            }
        }
        
        private void HandleGameJoined(WebSocketMessage message)
        {
            if (message.Data.TryGetValue("game_id", out var gameIdObj) && gameIdObj is string gameId)
            {
                CurrentGameId = gameId;
                OnGameJoined?.Invoke(gameId);
                UnityEngine.Debug.Log($"Successfully joined game: {gameId}");
            }
        }
        
        private void HandleGameLeft(WebSocketMessage message)
        {
            if (message.Data.TryGetValue("game_id", out var gameIdObj) && gameIdObj is string gameId)
            {
                if (gameId == CurrentGameId)
                {
                    CurrentGameId = null;
                    OnGameLeft?.Invoke(gameId);
                    UnityEngine.Debug.Log($"Left game: {gameId}");
                }
            }
        }
        
        private void HandleHostChanged(WebSocketMessage message)
        {
            if (message.Data.TryGetValue("old_host", out var oldHostObj) && oldHostObj is string oldHost &&
                message.Data.TryGetValue("new_host", out var newHostObj) && newHostObj is string newHost)
            {
                OnHostChanged?.Invoke(oldHost, newHost);
                UnityEngine.Debug.Log($"Host changed from {oldHost} to {newHost}");
            }
        }
        
        private void HandlePlayerJoined(WebSocketMessage message)
        {
            if (message.Data.TryGetValue("player_id", out var playerIdObj) && playerIdObj is string playerId)
            {
                UnityEngine.Debug.Log($"Player joined: {playerId}");
            }
        }

        private void HandlePlayerLeft(WebSocketMessage message)
        {
            if (message.Data.TryGetValue("player_id", out var playerIdObj) && playerIdObj is string playerId)
            {
                UnityEngine.Debug.Log($"Player left: {playerId}");
            }
        }

        private void HandleGameStateUpdate(WebSocketMessage message)
        {
            UnityEngine.Debug.Log("Received game state update");
            // 处理游戏状态更新
        }
        
        #endregion
        
        #region 状态查询
        
        private void UpdateConnection()
        {
            if (_connection != null)
            {
                _connection.Update();
            }
        }
        
        /// <summary>
        /// 获取当前延迟
        /// </summary>
        public float GetLatency()
        {
            return _currentLatency;
        }
        
        /// <summary>
        /// 获取连接状态信息
        /// </summary>
        public WebSocketConnectionInfo GetConnectionInfo()
        {
            return new WebSocketConnectionInfo
            {
                IsConnected = IsConnected,
                IsConnecting = IsConnecting,
                ServerUrl = serverUrl,
                CurrentGameId = CurrentGameId,
                Latency = _currentLatency,
                ReconnectAttempts = _reconnectAttempts
            };
        }
        
        #endregion
    }
}
