using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace CustomNetworking.WebSocket
{
    /// <summary>
    /// WebSocket消息结构
    /// </summary>
    [Serializable]
    public class WebSocketMessage
    {
        public string Type;
        public string SenderId;
        public Dictionary<string, object> Data;
        public float Timestamp;
        public string MessageId;
        
        public WebSocketMessage()
        {
            MessageId = Guid.NewGuid().ToString();
            Data = new Dictionary<string, object>();
            Timestamp = Time.time;
        }
        
        public WebSocketMessage(string type) : this()
        {
            Type = type;
        }
        
        /// <summary>
        /// 序列化为JSON字符串
        /// </summary>
        public string ToJson()
        {
            try
            {
                return JsonUtility.ToJson(this);
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Failed to serialize WebSocket message: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 从JSON字符串反序列化
        /// </summary>
        public static WebSocketMessage FromJson(string json)
        {
            try
            {
                return JsonUtility.FromJson<WebSocketMessage>(json);
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Failed to deserialize WebSocket message: {ex.Message}");
                return null;
            }
        }
    }
    
    /// <summary>
    /// WebSocket连接信息
    /// </summary>
    [Serializable]
    public class WebSocketConnectionInfo
    {
        public bool IsConnected;
        public bool IsConnecting;
        public string ServerUrl;
        public string CurrentGameId;
        public float Latency;
        public int ReconnectAttempts;
        public float LastHeartbeat;
        public WebSocketConnectionState State;
    }
    
    /// <summary>
    /// WebSocket连接状态
    /// </summary>
    public enum WebSocketConnectionState
    {
        Disconnected,
        Connecting,
        Connected,
        Reconnecting,
        Error
    }
    
    /// <summary>
    /// WebSocket连接类 - 简化实现
    /// </summary>
    public class WebSocketConnection
    {
        public bool IsConnected { get; private set; }
        public string Url { get; private set; }
        
        public event Action OnConnected;
        public event Action OnDisconnected;
        public event Action<WebSocketMessage> OnMessageReceived;
        public event Action<string> OnError;
        
        private Queue<WebSocketMessage> _messageQueue = new Queue<WebSocketMessage>();
        private bool _isSimulated = true; // 简化实现，使用模拟连接
        
        public WebSocketConnection(string url)
        {
            Url = url;
        }
        
        /// <summary>
        /// 连接到服务器
        /// </summary>
        public IEnumerator Connect()
        {
            UnityEngine.Debug.Log($"Connecting to WebSocket server: {Url}");
            
            // 模拟连接延迟
            yield return new WaitForSeconds(1f);
            
            if (_isSimulated)
            {
                // 模拟连接成功
                IsConnected = true;
                OnConnected?.Invoke();
                UnityEngine.Debug.Log("WebSocket connection established (simulated)");
            }
            else
            {
                // 实际WebSocket连接实现
                // 这里应该使用真实的WebSocket库，如WebSocketSharp
                try
                {
                    // TODO: 实现真实的WebSocket连接
                    IsConnected = true;
                    OnConnected?.Invoke();
                }
                catch (Exception ex)
                {
                    OnError?.Invoke(ex.Message);
                    throw;
                }
            }
        }
        
        /// <summary>
        /// 断开连接
        /// </summary>
        public void Disconnect()
        {
            if (!IsConnected)
                return;
                
            IsConnected = false;
            OnDisconnected?.Invoke();
            UnityEngine.Debug.Log("WebSocket disconnected");
        }
        
        /// <summary>
        /// 发送消息
        /// </summary>
        public void SendMessage(WebSocketMessage message)
        {
            if (!IsConnected)
            {
                UnityEngine.Debug.LogWarning("Cannot send message: WebSocket not connected");
                return;
            }
            
            if (_isSimulated)
            {
                // 模拟发送消息
                UnityEngine.Debug.Log($"Sending WebSocket message: {message.Type}");
                
                // 模拟一些服务器响应
                SimulateServerResponse(message);
            }
            else
            {
                // 实际发送消息
                try
                {
                    string json = message.ToJson();
                    if (!string.IsNullOrEmpty(json))
                    {
                        // TODO: 使用真实的WebSocket发送
                        UnityEngine.Debug.Log($"Sent WebSocket message: {json}");
                    }
                }
                catch (Exception ex)
                {
                    OnError?.Invoke($"Failed to send message: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 更新连接状态
        /// </summary>
        public void Update()
        {
            // 处理接收到的消息队列
            while (_messageQueue.Count > 0)
            {
                var message = _messageQueue.Dequeue();
                OnMessageReceived?.Invoke(message);
            }
        }
        
        /// <summary>
        /// 模拟服务器响应
        /// </summary>
        private void SimulateServerResponse(WebSocketMessage clientMessage)
        {
            // 根据客户端消息类型模拟相应的服务器响应
            switch (clientMessage.Type)
            {
                case "ping":
                    // 响应pong
                    var pongMessage = new WebSocketMessage("pong")
                    {
                        Data = clientMessage.Data
                    };
                    _messageQueue.Enqueue(pongMessage);
                    break;
                    
                case "join_game":
                    // 响应游戏加入成功
                    if (clientMessage.Data.TryGetValue("game_id", out var gameId))
                    {
                        var joinResponse = new WebSocketMessage("game_joined")
                        {
                            Data = new Dictionary<string, object>
                            {
                                ["game_id"] = gameId,
                                ["success"] = true
                            }
                        };
                        _messageQueue.Enqueue(joinResponse);
                    }
                    break;
                    
                case "leave_game":
                    // 响应游戏离开成功
                    if (clientMessage.Data.TryGetValue("game_id", out var leftGameId))
                    {
                        var leaveResponse = new WebSocketMessage("game_left")
                        {
                            Data = new Dictionary<string, object>
                            {
                                ["game_id"] = leftGameId,
                                ["success"] = true
                            }
                        };
                        _messageQueue.Enqueue(leaveResponse);
                    }
                    break;
            }
        }
    }
    
    /// <summary>
    /// WebSocket事件类型
    /// </summary>
    public static class WebSocketEventTypes
    {
        // 连接相关
        public const string PING = "ping";
        public const string PONG = "pong";
        public const string CONNECT = "connect";
        public const string DISCONNECT = "disconnect";
        
        // 游戏会话相关
        public const string JOIN_GAME = "join_game";
        public const string LEAVE_GAME = "leave_game";
        public const string GAME_JOINED = "game_joined";
        public const string GAME_LEFT = "game_left";
        
        // 玩家相关
        public const string PLAYER_JOINED = "player_joined";
        public const string PLAYER_LEFT = "player_left";
        public const string PLAYER_UPDATE = "player_update";
        
        // 主机迁移相关
        public const string HOST_CHANGED = "host_changed";
        public const string HOST_MIGRATION_START = "host_migration_start";
        public const string HOST_MIGRATION_COMPLETE = "host_migration_complete";
        
        // 游戏状态相关
        public const string GAME_STATE_UPDATE = "game_state_update";
        public const string GAME_EVENT = "game_event";
        
        // RPC相关
        public const string RPC_CALL = "rpc_call";
        public const string RPC_RESPONSE = "rpc_response";
        
        // 错误相关
        public const string ERROR = "error";
        public const string WARNING = "warning";
    }
    
    /// <summary>
    /// WebSocket配置
    /// </summary>
    [Serializable]
    public class WebSocketConfiguration
    {
        [Header("Connection Settings")]
        public string ServerUrl = "ws://localhost:8080";
        public float ConnectionTimeout = 10f;
        public bool AutoReconnect = true;
        public int MaxReconnectAttempts = 5;
        public float ReconnectDelay = 2f;
        
        [Header("Heartbeat Settings")]
        public bool EnableHeartbeat = true;
        public float HeartbeatInterval = 30f;
        public float HeartbeatTimeout = 60f;
        
        [Header("Message Settings")]
        public int MaxMessageQueueSize = 1000;
        public bool EnableMessageLogging = false;
        public bool EnableMessageCompression = false;
        
        [Header("Security Settings")]
        public bool EnableSSL = false;
        public string AuthToken = "";
        public Dictionary<string, string> Headers = new Dictionary<string, string>();
        
        public WebSocketConfiguration()
        {
            Headers = new Dictionary<string, string>();
        }
    }
    
    /// <summary>
    /// WebSocket统计信息
    /// </summary>
    [Serializable]
    public class WebSocketStatistics
    {
        public int MessagesSent;
        public int MessagesReceived;
        public int ConnectionAttempts;
        public int SuccessfulConnections;
        public int FailedConnections;
        public float AverageLatency;
        public float TotalUptime;
        public DateTime LastConnected;
        public DateTime LastDisconnected;
        
        public float GetConnectionSuccessRate()
        {
            if (ConnectionAttempts == 0) return 0f;
            return (float)SuccessfulConnections / ConnectionAttempts * 100f;
        }
    }
}
