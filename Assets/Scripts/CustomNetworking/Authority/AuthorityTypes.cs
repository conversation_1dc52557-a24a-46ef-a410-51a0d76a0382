using System;
using System.Collections.Generic;
using CustomNetworking.Core;

namespace CustomNetworking.Authority
{
    /// <summary>
    /// 权限类型枚举
    /// </summary>
    public enum AuthorityType
    {
        Read,       // 读取权限
        Modify,     // 修改权限
        Delete,     // 删除权限
        Execute,    // 执行权限
        Admin       // 管理员权限
    }
    
    /// <summary>
    /// 权限请求类型
    /// </summary>
    public enum AuthorityRequestType
    {
        Check,      // 检查权限
        Grant,      // 授予权限
        Revoke,     // 撤销权限
        Transfer    // 转移权限
    }
    
    /// <summary>
    /// 对象权限信息
    /// </summary>
    [Serializable]
    public class ObjectAuthority
    {
        public NetworkId ObjectId;
        public PlayerRef Owner;
        public Dictionary<PlayerRef, List<AuthorityType>> Authorities;
        public float LastUpdated;
        
        public ObjectAuthority()
        {
            Authorities = new Dictionary<PlayerRef, List<AuthorityType>>();
        }
        
        /// <summary>
        /// 检查玩家是否有指定权限
        /// </summary>
        public bool HasAuthority(PlayerRef player, AuthorityType authorityType)
        {
            // 所有者有所有权限
            if (Owner.Equals(player))
                return true;
                
            // 检查特定权限
            if (Authorities.TryGetValue(player, out var playerAuthorities))
            {
                return playerAuthorities.Contains(authorityType) || playerAuthorities.Contains(AuthorityType.Admin);
            }
            
            return false;
        }
        
        /// <summary>
        /// 获取玩家的所有权限
        /// </summary>
        public List<AuthorityType> GetPlayerAuthorities(PlayerRef player)
        {
            if (Owner.Equals(player))
            {
                // 所有者拥有所有权限
                return new List<AuthorityType> 
                { 
                    AuthorityType.Read, 
                    AuthorityType.Modify, 
                    AuthorityType.Delete, 
                    AuthorityType.Execute, 
                    AuthorityType.Admin 
                };
            }
            
            if (Authorities.TryGetValue(player, out var authorities))
            {
                return new List<AuthorityType>(authorities);
            }
            
            return new List<AuthorityType>();
        }
    }
    
    /// <summary>
    /// 权限请求信息
    /// </summary>
    [Serializable]
    public class AuthorityRequest
    {
        public string RequestId;
        public NetworkId ObjectId;
        public PlayerRef Player;
        public PlayerRef TargetPlayer; // 用于转移权限
        public AuthorityType AuthorityType;
        public AuthorityRequestType RequestType;
        public float Timestamp;
        public bool IsCompleted;
        public bool Success;
        public string ErrorMessage;
        
        public AuthorityRequest()
        {
            RequestId = Guid.NewGuid().ToString();
            Timestamp = UnityEngine.Time.time;
        }
    }
    
    /// <summary>
    /// 权限变更事件
    /// </summary>
    [Serializable]
    public class AuthorityChangeEvent
    {
        public NetworkId ObjectId;
        public PlayerRef Player;
        public AuthorityType AuthorityType;
        public AuthorityChangeType ChangeType;
        public float Timestamp;
        public string Reason;
        
        public AuthorityChangeEvent()
        {
            Timestamp = UnityEngine.Time.time;
        }
    }
    
    /// <summary>
    /// 权限变更类型
    /// </summary>
    public enum AuthorityChangeType
    {
        Granted,    // 权限被授予
        Revoked,    // 权限被撤销
        Transferred // 权限被转移
    }
    
    /// <summary>
    /// 权限验证结果
    /// </summary>
    [Serializable]
    public class AuthorityValidationResult
    {
        public bool IsValid;
        public string Reason;
        public List<AuthorityType> RequiredAuthorities;
        public List<AuthorityType> MissingAuthorities;
        
        public AuthorityValidationResult()
        {
            RequiredAuthorities = new List<AuthorityType>();
            MissingAuthorities = new List<AuthorityType>();
        }
        
        public static AuthorityValidationResult Success()
        {
            return new AuthorityValidationResult { IsValid = true };
        }
        
        public static AuthorityValidationResult Failure(string reason)
        {
            return new AuthorityValidationResult { IsValid = false, Reason = reason };
        }
    }
    
    /// <summary>
    /// 权限配置
    /// </summary>
    [Serializable]
    public class AuthorityConfiguration
    {
        public bool EnableStrictValidation = true;
        public bool EnableLocalCache = true;
        public float CacheTimeout = 300f; // 5分钟
        public float RequestTimeout = 30f; // 30秒
        public int MaxCachedObjects = 1000;
        public bool LogAuthorityChanges = true;
        
        // 默认权限设置
        public Dictionary<string, List<AuthorityType>> DefaultObjectAuthorities;
        public Dictionary<string, List<AuthorityType>> DefaultPlayerAuthorities;
        
        public AuthorityConfiguration()
        {
            DefaultObjectAuthorities = new Dictionary<string, List<AuthorityType>>();
            DefaultPlayerAuthorities = new Dictionary<string, List<AuthorityType>>();
        }
    }
    
    /// <summary>
    /// 权限统计信息
    /// </summary>
    [Serializable]
    public class AuthorityStatistics
    {
        public int TotalObjects;
        public int TotalPlayers;
        public int TotalRequests;
        public int SuccessfulRequests;
        public int FailedRequests;
        public float AverageResponseTime;
        public Dictionary<AuthorityType, int> AuthorityTypeUsage;
        public Dictionary<AuthorityRequestType, int> RequestTypeUsage;
        
        public AuthorityStatistics()
        {
            AuthorityTypeUsage = new Dictionary<AuthorityType, int>();
            RequestTypeUsage = new Dictionary<AuthorityRequestType, int>();
        }
        
        public float GetSuccessRate()
        {
            if (TotalRequests == 0) return 0f;
            return (float)SuccessfulRequests / TotalRequests * 100f;
        }
    }
}
