using UnityEngine;
using CustomNetworking.Core;
using System.Threading.Tasks;

namespace CustomNetworking.Examples
{
    /// <summary>
    /// 简单的网络示例 - 展示如何使用自定义网络框架
    /// </summary>
    public class SimpleNetworkExample : MonoBehaviour
    {
        [Header("网络设置")]
        [SerializeField] private string sessionName = "TestRoom";
        [SerializeField] private int maxPlayers = 10;
        [SerializeField] private NetworkRunner.GameMode gameMode = NetworkRunner.GameMode.AutoHostOrClient;
        
        [Header("玩家预制体")]
        [SerializeField] private GameObject playerPrefab;
        
        private NetworkRunner _runner;
        
        private void Start()
        {
            // 创建网络运行器
            _runner = gameObject.AddComponent<NetworkRunner>();
            _runner.AddCallbacks(new NetworkCallbacks());
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            
            GUILayout.Label("自定义网络框架示例", GUI.skin.box);
            GUILayout.Space(10);
            
            // 显示当前状态
            GUILayout.Label($"状态: {(_runner != null ? _runner.State.ToString() : "未初始化")}");
            GUILayout.Label($"模式: {gameMode}");
            GUILayout.Label($"会话: {sessionName}");
            
            GUILayout.Space(10);
            
            // 控制按钮
            if (_runner == null || _runner.State == NetworkRunner.States.Shutdown)
            {
                if (GUILayout.Button("启动主机"))
                {
                    StartHost();
                }
                
                if (GUILayout.Button("启动客户端"))
                {
                    StartClient();
                }
                
                if (GUILayout.Button("启动服务器"))
                {
                    StartServer();
                }
            }
            else if (_runner.State == NetworkRunner.States.Running)
            {
                GUILayout.Label($"活跃玩家: {_runner.ActivePlayers.Count}");
                GUILayout.Label($"本地玩家: {_runner.LocalPlayer}");
                GUILayout.Label($"当前Tick: {_runner.Tick}");
                
                GUILayout.Space(10);
                
                if (GUILayout.Button("生成玩家") && _runner.IsServer && playerPrefab != null)
                {
                    SpawnPlayer();
                }
                
                if (GUILayout.Button("断开连接"))
                {
                    Disconnect();
                }
            }
            else
            {
                GUILayout.Label("正在连接...");
            }
            
            GUILayout.EndArea();
        }
        
        /// <summary>
        /// 启动主机
        /// </summary>
        private async void StartHost()
        {
            await StartNetwork(NetworkRunner.GameMode.Host);
        }
        
        /// <summary>
        /// 启动客户端
        /// </summary>
        private async void StartClient()
        {
            await StartNetwork(NetworkRunner.GameMode.Client);
        }
        
        /// <summary>
        /// 启动服务器
        /// </summary>
        private async void StartServer()
        {
            await StartNetwork(NetworkRunner.GameMode.Server);
        }
        
        /// <summary>
        /// 启动网络
        /// </summary>
        private async Task StartNetwork(NetworkRunner.GameMode mode)
        {
            if (_runner == null) return;
            
            var startGameArgs = new StartGameArgs
            {
                GameMode = mode,
                SessionName = sessionName,
                Address = NetAddress.Any(),
                MaxPlayers = maxPlayers,
                IsOpen = true,
                IsVisible = true
            };
            
            UnityEngine.Debug.Log($"启动网络会话: {mode}");

            var result = await _runner.StartGame(startGameArgs);

            if (result.Ok)
            {
                UnityEngine.Debug.Log("网络会话启动成功！");
            }
            else
            {
                UnityEngine.Debug.LogError($"网络会话启动失败: {result.ErrorMessage}");
            }
        }
        
        /// <summary>
        /// 生成玩家
        /// </summary>
        private void SpawnPlayer()
        {
            if (_runner == null || !_runner.IsServer || playerPrefab == null) return;
            
            // 随机生成位置
            Vector3 spawnPosition = new Vector3(
                Random.Range(-5f, 5f),
                0f,
                Random.Range(-5f, 5f)
            );
            
            // 生成玩家对象
            var playerObject = _runner.Spawn(playerPrefab, spawnPosition, Quaternion.identity);

            UnityEngine.Debug.Log($"生成玩家对象: {playerObject.Id}");
        }
        
        /// <summary>
        /// 断开连接
        /// </summary>
        private async void Disconnect()
        {
            if (_runner != null)
            {
                await _runner.Shutdown();
                UnityEngine.Debug.Log("已断开网络连接");
            }
        }
        
        private void OnDestroy()
        {
            if (_runner != null)
            {
                _runner.Shutdown();
            }
        }
    }
    
    /// <summary>
    /// 网络回调处理器
    /// </summary>
    public class NetworkCallbacks : INetworkRunnerCallbacks
    {
        public void OnPlayerJoined(NetworkRunner runner, PlayerRef player)
        {
            UnityEngine.Debug.Log($"玩家加入: {player}");
        }

        public void OnPlayerLeft(NetworkRunner runner, PlayerRef player)
        {
            UnityEngine.Debug.Log($"玩家离开: {player}");
        }

        public void OnConnectedToServer(NetworkRunner runner)
        {
            UnityEngine.Debug.Log("已连接到服务器");
        }

        public void OnDisconnectedFromServer(NetworkRunner runner, NetDisconnectReason reason)
        {
            UnityEngine.Debug.Log($"与服务器断开连接: {reason}");
        }

        public void OnShutdown(NetworkRunner runner, NetDisconnectReason shutdownReason)
        {
            UnityEngine.Debug.Log($"网络运行器关闭: {shutdownReason}");
        }
        
        // 其他回调方法的空实现
        public void OnInput(NetworkRunner runner, NetworkInput input) { }
        public void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInput input) { }
        public void OnConnectRequest(NetworkRunner runner, NetworkRunnerCallbackArgs.ConnectRequest request, byte[] token) { request.Accept(); }
        public void OnConnectFailed(NetworkRunner runner, NetAddress remoteAddress, NetConnectFailedReason reason) { }
        public void OnUserSimulationMessage(NetworkRunner runner, SimulationMessagePtr message) { }
        public void OnSessionListUpdated(NetworkRunner runner, System.Collections.Generic.List<SessionInfo> sessionList) { }
        public void OnCustomAuthenticationResponse(NetworkRunner runner, System.Collections.Generic.Dictionary<string, object> data) { }
        public void OnHostMigration(NetworkRunner runner, HostMigrationToken hostMigrationToken) { }
        public void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, ReliableKey key, System.ArraySegment<byte> data) { }
        public void OnReliableDataProgress(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress) { }
        public void OnSceneLoadDone(NetworkRunner runner) { }
        public void OnSceneLoadStart(NetworkRunner runner) { }
        public void OnObjectExitAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
        public void OnObjectEnterAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
    }
}

/// <summary>
/// 简单的网络玩家示例
/// </summary>
public class SimpleNetworkPlayer : NetworkBehaviour
{
    [Header("玩家设置")]
    [SerializeField] private float moveSpeed = 5f;
    [SerializeField] private Color playerColor = Color.blue;
    
    // 网络同步属性
    [Networked] public NetworkString<_16> PlayerName { get; set; }
    [Networked] public int Score { get; set; }
    [Networked] public NetworkBool IsActive { get; set; }
    
    private Renderer _renderer;
    
    public override void Spawned()
    {
        base.Spawned();
        
        _renderer = GetComponent<Renderer>();
        
        if (HasStateAuthority)
        {
            // 服务器初始化
            PlayerName = $"Player_{Object.Id.Raw}";
            Score = 0;
            IsActive = true;
            
            // 设置随机颜色
            playerColor = new Color(
                Random.Range(0f, 1f),
                Random.Range(0f, 1f),
                Random.Range(0f, 1f)
            );
        }
        
        // 更新外观
        UpdateAppearance();

        UnityEngine.Debug.Log($"玩家生成: {PlayerName} (ID: {Object.Id})");
    }
    
    public override void FixedUpdateNetwork()
    {
        if (!HasInputAuthority) return;
        
        // 简单的移动逻辑
        Vector3 movement = Vector3.zero;
        
        if (Input.GetKey(KeyCode.W)) movement += Vector3.forward;
        if (Input.GetKey(KeyCode.S)) movement += Vector3.back;
        if (Input.GetKey(KeyCode.A)) movement += Vector3.left;
        if (Input.GetKey(KeyCode.D)) movement += Vector3.right;
        
        if (movement != Vector3.zero)
        {
            transform.position += movement.normalized * moveSpeed * Runner.DeltaTime;
        }
        
        // 简单的得分逻辑
        if (Input.GetKeyDown(KeyCode.Space) && HasStateAuthority)
        {
            Score += 10;
            UnityEngine.Debug.Log($"{PlayerName} 得分: {Score}");
        }
    }
    
    public override void Render()
    {
        base.Render();
        
        // 更新外观
        UpdateAppearance();
    }
    
    private void UpdateAppearance()
    {
        if (_renderer != null)
        {
            _renderer.material.color = playerColor;
        }
    }
    
    private void OnGUI()
    {
        if (!HasInputAuthority) return;
        
        // 显示玩家信息
        Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position + Vector3.up * 2);
        if (screenPos.z > 0)
        {
            GUI.Label(new Rect(screenPos.x - 50, Screen.height - screenPos.y, 100, 20), 
                     $"{PlayerName}\n分数: {Score}");
        }
        
        // 控制说明
        GUI.Label(new Rect(10, Screen.height - 100, 200, 80), 
                 "控制:\nWASD - 移动\n空格 - 得分");
    }
}
