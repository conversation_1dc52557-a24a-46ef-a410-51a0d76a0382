using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Optimization
{
    /// <summary>
    /// 网络性能监控器 - 监控网络性能指标并提供优化建议
    /// </summary>
    public class NetworkPerformanceMonitor : MonoBehaviour
    {
        [Header("监控设置")]
        [SerializeField] private bool enableMonitoring = true;
        [SerializeField] private float updateInterval = 1f;
        [SerializeField] private int historySize = 60; // 保存60秒的历史数据
        
        [Header("性能阈值")]
        [SerializeField] private float warningLatency = 100f; // 延迟警告阈值 (ms)
        [SerializeField] private float criticalLatency = 200f; // 延迟严重阈值 (ms)
        [SerializeField] private float warningPacketLoss = 0.05f; // 丢包率警告阈值 (5%)
        [SerializeField] private float criticalPacketLoss = 0.1f; // 丢包率严重阈值 (10%)
        
        [Header("UI显示")]
        [SerializeField] private bool showOnScreenStats = false;
        [SerializeField] private KeyCode toggleStatsKey = KeyCode.F3;
        
        // 性能数据
        private Queue<PerformanceSnapshot> _performanceHistory = new Queue<PerformanceSnapshot>();
        private PerformanceSnapshot _currentSnapshot;
        private float _lastUpdateTime;
        
        // 网络统计
        private int _totalPacketsSent;
        private int _totalPacketsReceived;
        private int _totalPacketsLost;
        private float _totalBandwidthSent;
        private float _totalBandwidthReceived;
        
        // 延迟测量
        private List<float> _latencyMeasurements = new List<float>();
        private float _averageLatency;
        private float _maxLatency;
        private float _minLatency = float.MaxValue;
        
        // 事件
        public event Action<PerformanceAlert> OnPerformanceAlert;
        
        private void Start()
        {
            _lastUpdateTime = Time.time;
            _currentSnapshot = new PerformanceSnapshot();
        }
        
        private void Update()
        {
            if (!enableMonitoring) return;
            
            // 检查切换显示快捷键
            if (Input.GetKeyDown(toggleStatsKey))
            {
                showOnScreenStats = !showOnScreenStats;
            }
            
            // 定期更新性能数据
            float currentTime = Time.time;
            if (currentTime - _lastUpdateTime >= updateInterval)
            {
                UpdatePerformanceSnapshot();
                CheckPerformanceThresholds();
                _lastUpdateTime = currentTime;
            }
        }
        
        /// <summary>
        /// 更新性能快照
        /// </summary>
        private void UpdatePerformanceSnapshot()
        {
            _currentSnapshot = new PerformanceSnapshot
            {
                Timestamp = Time.time,
                FrameRate = 1f / Time.deltaTime,
                AverageLatency = _averageLatency,
                MaxLatency = _maxLatency,
                MinLatency = _minLatency == float.MaxValue ? 0 : _minLatency,
                PacketLossRate = CalculatePacketLossRate(),
                BandwidthSent = _totalBandwidthSent,
                BandwidthReceived = _totalBandwidthReceived,
                ConnectedPlayers = GetConnectedPlayerCount(),
                NetworkObjects = GetNetworkObjectCount()
            };
            
            // 添加到历史记录
            _performanceHistory.Enqueue(_currentSnapshot);
            
            // 保持历史记录大小
            while (_performanceHistory.Count > historySize)
            {
                _performanceHistory.Dequeue();
            }
            
            // 重置统计数据
            ResetPeriodicStats();
        }
        
        /// <summary>
        /// 检查性能阈值
        /// </summary>
        private void CheckPerformanceThresholds()
        {
            // 检查延迟
            if (_averageLatency > criticalLatency)
            {
                TriggerAlert(PerformanceAlertType.CriticalLatency, $"严重延迟: {_averageLatency:F1}ms");
            }
            else if (_averageLatency > warningLatency)
            {
                TriggerAlert(PerformanceAlertType.WarningLatency, $"延迟警告: {_averageLatency:F1}ms");
            }
            
            // 检查丢包率
            float packetLoss = CalculatePacketLossRate();
            if (packetLoss > criticalPacketLoss)
            {
                TriggerAlert(PerformanceAlertType.CriticalPacketLoss, $"严重丢包: {packetLoss * 100:F1}%");
            }
            else if (packetLoss > warningPacketLoss)
            {
                TriggerAlert(PerformanceAlertType.WarningPacketLoss, $"丢包警告: {packetLoss * 100:F1}%");
            }
            
            // 检查帧率
            if (_currentSnapshot.FrameRate < 30f)
            {
                TriggerAlert(PerformanceAlertType.LowFrameRate, $"低帧率: {_currentSnapshot.FrameRate:F1} FPS");
            }
        }
        
        /// <summary>
        /// 触发性能警报
        /// </summary>
        private void TriggerAlert(PerformanceAlertType type, string message)
        {
            var alert = new PerformanceAlert
            {
                Type = type,
                Message = message,
                Timestamp = Time.time,
                Severity = GetAlertSeverity(type)
            };
            
            OnPerformanceAlert?.Invoke(alert);
            
            if (alert.Severity >= AlertSeverity.Warning)
            {
                UnityEngine.Debug.LogWarning($"[NetworkPerformance] {message}");
            }
        }
        
        /// <summary>
        /// 记录延迟测量
        /// </summary>
        public void RecordLatency(float latency)
        {
            _latencyMeasurements.Add(latency);
            
            // 更新统计
            _maxLatency = Mathf.Max(_maxLatency, latency);
            _minLatency = Mathf.Min(_minLatency, latency);
            
            // 计算平均延迟
            if (_latencyMeasurements.Count > 10)
            {
                _latencyMeasurements.RemoveAt(0);
            }
            
            float sum = 0f;
            foreach (float l in _latencyMeasurements)
            {
                sum += l;
            }
            _averageLatency = sum / _latencyMeasurements.Count;
        }
        
        /// <summary>
        /// 记录数据包发送
        /// </summary>
        public void RecordPacketSent(int size)
        {
            _totalPacketsSent++;
            _totalBandwidthSent += size / 1024f; // 转换为KB
        }
        
        /// <summary>
        /// 记录数据包接收
        /// </summary>
        public void RecordPacketReceived(int size)
        {
            _totalPacketsReceived++;
            _totalBandwidthReceived += size / 1024f; // 转换为KB
        }
        
        /// <summary>
        /// 记录数据包丢失
        /// </summary>
        public void RecordPacketLost()
        {
            _totalPacketsLost++;
        }
        
        /// <summary>
        /// 计算丢包率
        /// </summary>
        private float CalculatePacketLossRate()
        {
            int totalPackets = _totalPacketsSent + _totalPacketsLost;
            if (totalPackets == 0) return 0f;
            
            return (float)_totalPacketsLost / totalPackets;
        }
        
        /// <summary>
        /// 获取连接的玩家数量
        /// </summary>
        private int GetConnectedPlayerCount()
        {
            var runner = FindObjectOfType<NetworkRunner>();
            return runner?.ActivePlayers?.Count ?? 0;
        }
        
        /// <summary>
        /// 获取网络对象数量
        /// </summary>
        private int GetNetworkObjectCount()
        {
            return FindObjectsOfType<NetworkObject>().Length;
        }
        
        /// <summary>
        /// 重置周期性统计数据
        /// </summary>
        private void ResetPeriodicStats()
        {
            _totalPacketsSent = 0;
            _totalPacketsReceived = 0;
            _totalPacketsLost = 0;
            _totalBandwidthSent = 0f;
            _totalBandwidthReceived = 0f;
            _maxLatency = 0f;
            _minLatency = float.MaxValue;
        }
        
        /// <summary>
        /// 获取警报严重程度
        /// </summary>
        private AlertSeverity GetAlertSeverity(PerformanceAlertType type)
        {
            switch (type)
            {
                case PerformanceAlertType.CriticalLatency:
                case PerformanceAlertType.CriticalPacketLoss:
                    return AlertSeverity.Critical;
                    
                case PerformanceAlertType.WarningLatency:
                case PerformanceAlertType.WarningPacketLoss:
                case PerformanceAlertType.LowFrameRate:
                    return AlertSeverity.Warning;
                    
                default:
                    return AlertSeverity.Info;
            }
        }
        
        /// <summary>
        /// 获取当前性能快照
        /// </summary>
        public PerformanceSnapshot GetCurrentSnapshot()
        {
            return _currentSnapshot;
        }
        
        /// <summary>
        /// 获取性能历史
        /// </summary>
        public PerformanceSnapshot[] GetPerformanceHistory()
        {
            return _performanceHistory.ToArray();
        }
        
        /// <summary>
        /// 屏幕显示统计信息
        /// </summary>
        private void OnGUI()
        {
            if (!showOnScreenStats) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("网络性能监控", GUI.skin.label);
            GUILayout.Label($"帧率: {_currentSnapshot.FrameRate:F1} FPS");
            GUILayout.Label($"延迟: {_averageLatency:F1}ms (最大: {_maxLatency:F1}ms)");
            GUILayout.Label($"丢包率: {CalculatePacketLossRate() * 100:F1}%");
            GUILayout.Label($"发送带宽: {_totalBandwidthSent:F1} KB/s");
            GUILayout.Label($"接收带宽: {_totalBandwidthReceived:F1} KB/s");
            GUILayout.Label($"连接玩家: {_currentSnapshot.ConnectedPlayers}");
            GUILayout.Label($"网络对象: {_currentSnapshot.NetworkObjects}");
            
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
    }
    
    /// <summary>
    /// 性能快照
    /// </summary>
    [Serializable]
    public struct PerformanceSnapshot
    {
        public float Timestamp;
        public float FrameRate;
        public float AverageLatency;
        public float MaxLatency;
        public float MinLatency;
        public float PacketLossRate;
        public float BandwidthSent;
        public float BandwidthReceived;
        public int ConnectedPlayers;
        public int NetworkObjects;
    }
    
    /// <summary>
    /// 性能警报
    /// </summary>
    [Serializable]
    public struct PerformanceAlert
    {
        public PerformanceAlertType Type;
        public string Message;
        public float Timestamp;
        public AlertSeverity Severity;
    }
    
    /// <summary>
    /// 性能警报类型
    /// </summary>
    public enum PerformanceAlertType
    {
        WarningLatency,
        CriticalLatency,
        WarningPacketLoss,
        CriticalPacketLoss,
        LowFrameRate,
        HighBandwidthUsage
    }
    
    /// <summary>
    /// 警报严重程度
    /// </summary>
    public enum AlertSeverity
    {
        Info,
        Warning,
        Critical
    }
}
