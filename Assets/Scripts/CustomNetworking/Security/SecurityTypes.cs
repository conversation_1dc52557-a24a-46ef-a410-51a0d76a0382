using System;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Security
{
    /// <summary>
    /// 作弊类型枚举
    /// </summary>
    public enum CheatType
    {
        /// <summary>
        /// 速度作弊
        /// </summary>
        SpeedHack,
        
        /// <summary>
        /// 瞬移作弊
        /// </summary>
        Teleport,
        
        /// <summary>
        /// RPC垃圾邮件
        /// </summary>
        RpcSpam,
        
        /// <summary>
        /// 动作垃圾邮件
        /// </summary>
        ActionSpam,
        
        /// <summary>
        /// 无敌作弊
        /// </summary>
        Invincibility,
        
        /// <summary>
        /// 透视作弊
        /// </summary>
        Wallhack,
        
        /// <summary>
        /// 自动瞄准
        /// </summary>
        Aimbot,
        
        /// <summary>
        /// 时间操控
        /// </summary>
        TimeManipulation,
        
        /// <summary>
        /// 资源作弊
        /// </summary>
        ResourceHack,
        
        /// <summary>
        /// 权限提升
        /// </summary>
        PrivilegeEscalation
    }
    
    /// <summary>
    /// 验证失败类型
    /// </summary>
    public enum ValidationFailure
    {
        /// <summary>
        /// 无效的RPC参数
        /// </summary>
        InvalidRpcParameters,
        
        /// <summary>
        /// 非法动作
        /// </summary>
        IllegalAction,
        
        /// <summary>
        /// 无效时机
        /// </summary>
        InvalidTiming,
        
        /// <summary>
        /// 数据损坏
        /// </summary>
        DataCorruption,
        
        /// <summary>
        /// 状态不一致
        /// </summary>
        StateInconsistency,
        
        /// <summary>
        /// 权限不足
        /// </summary>
        InsufficientPermission,
        
        /// <summary>
        /// 范围检查失败
        /// </summary>
        RangeCheckFailed,
        
        /// <summary>
        /// 类型检查失败
        /// </summary>
        TypeCheckFailed
    }
    
    /// <summary>
    /// 安全违规类型
    /// </summary>
    public enum SecurityViolation
    {
        /// <summary>
        /// 未授权的RPC调用
        /// </summary>
        UnauthorizedRpc,
        
        /// <summary>
        /// 数据篡改
        /// </summary>
        DataTampering,
        
        /// <summary>
        /// 加密失败
        /// </summary>
        EncryptionFailure,
        
        /// <summary>
        /// 身份验证失败
        /// </summary>
        AuthenticationFailure,
        
        /// <summary>
        /// 会话劫持
        /// </summary>
        SessionHijacking,
        
        /// <summary>
        /// 重放攻击
        /// </summary>
        ReplayAttack,
        
        /// <summary>
        /// 注入攻击
        /// </summary>
        InjectionAttack
    }
    
    /// <summary>
    /// 玩家动作类型
    /// </summary>
    public enum ActionType
    {
        /// <summary>
        /// 移动
        /// </summary>
        Move,
        
        /// <summary>
        /// 杀人
        /// </summary>
        Kill,
        
        /// <summary>
        /// 报告尸体
        /// </summary>
        Report,
        
        /// <summary>
        /// 完成任务
        /// </summary>
        CompleteTask,
        
        /// <summary>
        /// 使用通风口
        /// </summary>
        UseVent,
        
        /// <summary>
        /// 破坏设施
        /// </summary>
        Sabotage,
        
        /// <summary>
        /// 召开紧急会议
        /// </summary>
        EmergencyMeeting,
        
        /// <summary>
        /// 投票
        /// </summary>
        Vote,
        
        /// <summary>
        /// 交互
        /// </summary>
        Interact
    }
    
    /// <summary>
    /// 玩家动作结构
    /// </summary>
    [Serializable]
    public struct PlayerAction
    {
        /// <summary>
        /// 动作类型
        /// </summary>
        public ActionType Type;
        
        /// <summary>
        /// 动作位置
        /// </summary>
        public Vector3 Position;
        
        /// <summary>
        /// 目标玩家 (如果有)
        /// </summary>
        public PlayerRef Target;
        
        /// <summary>
        /// 动作参数
        /// </summary>
        public object[] Parameters;
        
        /// <summary>
        /// 动作时间戳
        /// </summary>
        public float Timestamp;
        
        /// <summary>
        /// 动作ID
        /// </summary>
        public int ActionId;
        
        public override string ToString()
        {
            return $"Action[{Type}] at {Position} (Time: {Timestamp:F2}s)";
        }
    }
    
    /// <summary>
    /// 玩家安全数据
    /// </summary>
    [Serializable]
    public struct PlayerSecurityData
    {
        /// <summary>
        /// 玩家ID
        /// </summary>
        public PlayerRef PlayerId;
        
        /// <summary>
        /// 最后位置
        /// </summary>
        public Vector3 LastPosition;
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public float LastUpdateTime;
        
        /// <summary>
        /// 违规次数
        /// </summary>
        public int ViolationCount;
        
        /// <summary>
        /// 信任等级 (0-1)
        /// </summary>
        public float TrustLevel;
        
        /// <summary>
        /// 最后动作时间
        /// </summary>
        public float LastActionTime;
        
        /// <summary>
        /// 动作计数
        /// </summary>
        public int ActionCount;
        
        /// <summary>
        /// 是否被标记为可疑
        /// </summary>
        public bool IsSuspicious;
        
        /// <summary>
        /// 警告次数
        /// </summary>
        public int WarningCount;
    }
    
    /// <summary>
    /// 安全统计信息
    /// </summary>
    [Serializable]
    public struct SecurityStats
    {
        /// <summary>
        /// 总玩家数
        /// </summary>
        public int TotalPlayers;
        
        /// <summary>
        /// 总违规次数
        /// </summary>
        public int TotalViolations;
        
        /// <summary>
        /// 平均信任等级
        /// </summary>
        public float AverageTrustLevel;
        
        /// <summary>
        /// 是否启用加密
        /// </summary>
        public bool EncryptionEnabled;
        
        /// <summary>
        /// 是否启用防作弊
        /// </summary>
        public bool AntiCheatEnabled;
        
        /// <summary>
        /// 检测到的作弊次数
        /// </summary>
        public int CheatDetections;
        
        /// <summary>
        /// 验证失败次数
        /// </summary>
        public int ValidationFailures;
        
        /// <summary>
        /// 安全违规次数
        /// </summary>
        public int SecurityViolations;
    }
    
    /// <summary>
    /// 安全配置
    /// </summary>
    [Serializable]
    public struct SecurityConfig
    {
        /// <summary>
        /// 是否启用防作弊
        /// </summary>
        public bool EnableAntiCheat;
        
        /// <summary>
        /// 是否启用数据验证
        /// </summary>
        public bool EnableDataValidation;
        
        /// <summary>
        /// 是否启用加密
        /// </summary>
        public bool EnableEncryption;
        
        /// <summary>
        /// 是否启用频率限制
        /// </summary>
        public bool EnableRateLimit;
        
        /// <summary>
        /// 最大移动速度
        /// </summary>
        public float MaxMovementSpeed;
        
        /// <summary>
        /// 最大位置变化
        /// </summary>
        public float MaxPositionDelta;
        
        /// <summary>
        /// 最大RPC频率
        /// </summary>
        public float MaxRpcFrequency;
        
        /// <summary>
        /// 每秒最大动作数
        /// </summary>
        public int MaxActionsPerSecond;
        
        /// <summary>
        /// 信任等级阈值
        /// </summary>
        public float TrustLevelThreshold;
        
        /// <summary>
        /// 最大违规次数
        /// </summary>
        public int MaxViolations;
        
        /// <summary>
        /// 默认配置
        /// </summary>
        public static SecurityConfig Default => new SecurityConfig
        {
            EnableAntiCheat = true,
            EnableDataValidation = true,
            EnableEncryption = true,
            EnableRateLimit = true,
            MaxMovementSpeed = 10f,
            MaxPositionDelta = 5f,
            MaxRpcFrequency = 20f,
            MaxActionsPerSecond = 10,
            TrustLevelThreshold = 0.5f,
            MaxViolations = 5
        };
    }
    
    /// <summary>
    /// 加密数据包
    /// </summary>
    [Serializable]
    public struct EncryptedPacket
    {
        /// <summary>
        /// 加密的数据
        /// </summary>
        public byte[] EncryptedData;
        
        /// <summary>
        /// HMAC签名
        /// </summary>
        public byte[] Signature;
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public long Timestamp;
        
        /// <summary>
        /// 包ID
        /// </summary>
        public int PacketId;
        
        /// <summary>
        /// 发送者ID
        /// </summary>
        public PlayerRef SenderId;
    }
    
    /// <summary>
    /// 验证结果
    /// </summary>
    public struct ValidationResult
    {
        /// <summary>
        /// 是否验证成功
        /// </summary>
        public bool IsValid;
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage;
        
        /// <summary>
        /// 失败类型
        /// </summary>
        public ValidationFailure FailureType;
        
        /// <summary>
        /// 建议动作
        /// </summary>
        public string SuggestedAction;
        
        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static ValidationResult Success()
        {
            return new ValidationResult
            {
                IsValid = true,
                ErrorMessage = string.Empty,
                SuggestedAction = string.Empty
            };
        }
        
        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static ValidationResult Failure(ValidationFailure failureType, string errorMessage, string suggestedAction = "")
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = errorMessage,
                FailureType = failureType,
                SuggestedAction = suggestedAction
            };
        }
    }
    
    /// <summary>
    /// 安全事件
    /// </summary>
    [Serializable]
    public struct SecurityEvent
    {
        /// <summary>
        /// 事件类型
        /// </summary>
        public SecurityEventType Type;
        
        /// <summary>
        /// 涉及的玩家
        /// </summary>
        public PlayerRef Player;
        
        /// <summary>
        /// 事件描述
        /// </summary>
        public string Description;
        
        /// <summary>
        /// 事件时间
        /// </summary>
        public float Timestamp;
        
        /// <summary>
        /// 严重程度
        /// </summary>
        public SecuritySeverity Severity;
        
        /// <summary>
        /// 相关数据
        /// </summary>
        public object Data;
    }
    
    /// <summary>
    /// 安全事件类型
    /// </summary>
    public enum SecurityEventType
    {
        CheatDetected,
        ValidationFailed,
        SecurityViolation,
        SuspiciousActivity,
        PlayerBanned,
        PlayerWarned
    }
    
    /// <summary>
    /// 安全严重程度
    /// </summary>
    public enum SecuritySeverity
    {
        Low,
        Medium,
        High,
        Critical
    }
}
