using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Security
{
    /// <summary>
    /// 网络数据验证器 - 验证网络数据的完整性和合法性
    /// </summary>
    public static class NetworkDataValidator
    {
        // 验证规则缓存
        private static Dictionary<Type, ValidationRule[]> _validationRules = new Dictionary<Type, ValidationRule[]>();
        
        // 数据范围限制
        private static readonly Dictionary<string, (float min, float max)> _rangeConstraints = new Dictionary<string, (float, float)>
        {
            { "position.x", (-100f, 100f) },
            { "position.y", (-10f, 50f) },
            { "position.z", (-100f, 100f) },
            { "velocity.magnitude", (0f, 20f) },
            { "health", (0f, 100f) },
            { "energy", (0f, 100f) },
            { "angle", (0f, 360f) }
        };
        
        /// <summary>
        /// 验证玩家网络输入
        /// </summary>
        public static ValidationResult ValidatePlayerInput(PlayerNetworkInput input)
        {
            // 验证移动输入
            if (input.MovementInput.magnitude > 1.1f) // 允许小误差
            {
                return ValidationResult.Failure(
                    ValidationFailure.RangeCheckFailed,
                    $"移动输入超出范围: {input.MovementInput.magnitude}",
                    "限制移动输入到单位向量"
                );
            }
            
            // 验证输入组合的合理性
            if (input.InteractPressed && input.ActionPressed && input.ReportPressed)
            {
                return ValidationResult.Failure(
                    ValidationFailure.IllegalAction,
                    "同时按下多个互斥按钮",
                    "检查输入系统逻辑"
                );
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 验证玩家位置
        /// </summary>
        public static ValidationResult ValidatePlayerPosition(Vector3 position, Vector3 lastPosition, float deltaTime)
        {
            // 验证位置范围
            if (!IsPositionInBounds(position))
            {
                return ValidationResult.Failure(
                    ValidationFailure.RangeCheckFailed,
                    $"位置超出游戏边界: {position}",
                    "将玩家传送回有效位置"
                );
            }
            
            // 验证移动距离
            float distance = Vector3.Distance(position, lastPosition);
            float maxDistance = GetMaxMovementDistance(deltaTime);
            
            if (distance > maxDistance)
            {
                return ValidationResult.Failure(
                    ValidationFailure.RangeCheckFailed,
                    $"移动距离过大: {distance} > {maxDistance}",
                    "回滚到上一个有效位置"
                );
            }
            
            // 验证Y轴变化（防止飞行作弊）
            float yDelta = Mathf.Abs(position.y - lastPosition.y);
            if (yDelta > 2f && deltaTime < 1f) // 1秒内Y轴变化不应超过2米
            {
                return ValidationResult.Failure(
                    ValidationFailure.IllegalAction,
                    $"垂直移动异常: {yDelta}",
                    "检查是否存在飞行作弊"
                );
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 验证RPC参数
        /// </summary>
        public static ValidationResult ValidateRpcParameters(string methodName, object[] parameters)
        {
            switch (methodName)
            {
                case "RPC_KillPlayer":
                    return ValidateKillRpc(parameters);
                case "RPC_ReportBody":
                    return ValidateReportRpc(parameters);
                case "RPC_CompleteTask":
                    return ValidateTaskRpc(parameters);
                case "RPC_UseVent":
                    return ValidateVentRpc(parameters);
                case "RPC_Sabotage":
                    return ValidateSabotageRpc(parameters);
                case "RPC_Vote":
                    return ValidateVoteRpc(parameters);
                default:
                    return ValidateGenericRpc(parameters);
            }
        }
        
        /// <summary>
        /// 验证杀人RPC
        /// </summary>
        private static ValidationResult ValidateKillRpc(object[] parameters)
        {
            if (parameters == null || parameters.Length < 2)
            {
                return ValidationResult.Failure(
                    ValidationFailure.InvalidRpcParameters,
                    "杀人RPC参数不足",
                    "检查RPC调用参数"
                );
            }
            
            // 验证目标玩家ID
            if (!(parameters[0] is PlayerRef target))
            {
                return ValidationResult.Failure(
                    ValidationFailure.TypeCheckFailed,
                    "目标玩家ID类型错误",
                    "确保传递正确的PlayerRef类型"
                );
            }
            
            // 验证杀人位置
            if (!(parameters[1] is Vector3 killPosition))
            {
                return ValidationResult.Failure(
                    ValidationFailure.TypeCheckFailed,
                    "杀人位置类型错误",
                    "确保传递正确的Vector3类型"
                );
            }
            
            // 验证位置合理性
            if (!IsPositionInBounds(killPosition))
            {
                return ValidationResult.Failure(
                    ValidationFailure.RangeCheckFailed,
                    "杀人位置超出边界",
                    "检查杀人位置是否有效"
                );
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 验证报告RPC
        /// </summary>
        private static ValidationResult ValidateReportRpc(object[] parameters)
        {
            if (parameters == null || parameters.Length < 2)
            {
                return ValidationResult.Failure(
                    ValidationFailure.InvalidRpcParameters,
                    "报告RPC参数不足"
                );
            }
            
            // 验证尸体ID
            if (!(parameters[0] is int bodyId))
            {
                return ValidationResult.Failure(
                    ValidationFailure.TypeCheckFailed,
                    "尸体ID类型错误"
                );
            }
            
            // 验证报告位置
            if (!(parameters[1] is Vector3 reportPosition))
            {
                return ValidationResult.Failure(
                    ValidationFailure.TypeCheckFailed,
                    "报告位置类型错误"
                );
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 验证任务RPC
        /// </summary>
        private static ValidationResult ValidateTaskRpc(object[] parameters)
        {
            if (parameters == null || parameters.Length < 1)
            {
                return ValidationResult.Failure(
                    ValidationFailure.InvalidRpcParameters,
                    "任务RPC参数不足"
                );
            }
            
            // 验证任务ID
            if (!(parameters[0] is int taskId))
            {
                return ValidationResult.Failure(
                    ValidationFailure.TypeCheckFailed,
                    "任务ID类型错误"
                );
            }
            
            // 验证任务ID范围
            if (taskId < 0 || taskId > 1000)
            {
                return ValidationResult.Failure(
                    ValidationFailure.RangeCheckFailed,
                    $"任务ID超出范围: {taskId}"
                );
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 验证通风口RPC
        /// </summary>
        private static ValidationResult ValidateVentRpc(object[] parameters)
        {
            if (parameters == null || parameters.Length < 2)
            {
                return ValidationResult.Failure(
                    ValidationFailure.InvalidRpcParameters,
                    "通风口RPC参数不足"
                );
            }
            
            // 验证通风口ID
            if (!(parameters[0] is int ventId))
            {
                return ValidationResult.Failure(
                    ValidationFailure.TypeCheckFailed,
                    "通风口ID类型错误"
                );
            }
            
            // 验证进入/退出标志
            if (!(parameters[1] is bool isEntering))
            {
                return ValidationResult.Failure(
                    ValidationFailure.TypeCheckFailed,
                    "进入标志类型错误"
                );
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 验证破坏RPC
        /// </summary>
        private static ValidationResult ValidateSabotageRpc(object[] parameters)
        {
            if (parameters == null || parameters.Length < 1)
            {
                return ValidationResult.Failure(
                    ValidationFailure.InvalidRpcParameters,
                    "破坏RPC参数不足"
                );
            }
            
            // 验证破坏类型
            if (!(parameters[0] is int sabotageType))
            {
                return ValidationResult.Failure(
                    ValidationFailure.TypeCheckFailed,
                    "破坏类型错误"
                );
            }
            
            // 验证破坏类型范围
            if (sabotageType < 0 || sabotageType > 10)
            {
                return ValidationResult.Failure(
                    ValidationFailure.RangeCheckFailed,
                    $"破坏类型超出范围: {sabotageType}"
                );
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 验证投票RPC
        /// </summary>
        private static ValidationResult ValidateVoteRpc(object[] parameters)
        {
            if (parameters == null || parameters.Length < 1)
            {
                return ValidationResult.Failure(
                    ValidationFailure.InvalidRpcParameters,
                    "投票RPC参数不足"
                );
            }
            
            // 验证投票目标（可以为null表示弃票）
            if (parameters[0] != null && !(parameters[0] is PlayerRef))
            {
                return ValidationResult.Failure(
                    ValidationFailure.TypeCheckFailed,
                    "投票目标类型错误"
                );
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 验证通用RPC
        /// </summary>
        private static ValidationResult ValidateGenericRpc(object[] parameters)
        {
            // 检查参数数量
            if (parameters != null && parameters.Length > 10)
            {
                return ValidationResult.Failure(
                    ValidationFailure.RangeCheckFailed,
                    $"RPC参数过多: {parameters.Length}",
                    "减少RPC参数数量"
                );
            }
            
            // 检查参数类型
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    if (param != null && !IsValidParameterType(param.GetType()))
                    {
                        return ValidationResult.Failure(
                            ValidationFailure.TypeCheckFailed,
                            $"不支持的参数类型: {param.GetType()}",
                            "使用支持的参数类型"
                        );
                    }
                }
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 验证游戏状态
        /// </summary>
        public static ValidationResult ValidateGameState(GameState currentState, GameState newState)
        {
            // 验证状态转换的合法性
            if (!IsValidStateTransition(currentState, newState))
            {
                return ValidationResult.Failure(
                    ValidationFailure.StateInconsistency,
                    $"非法状态转换: {currentState} -> {newState}",
                    "检查游戏状态转换逻辑"
                );
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 验证玩家状态
        /// </summary>
        public static ValidationResult ValidatePlayerState(PlayerValidationState playerState)
        {
            // 验证血量
            if (playerState.Health < 0 || playerState.Health > 100)
            {
                return ValidationResult.Failure(
                    ValidationFailure.RangeCheckFailed,
                    $"血量超出范围: {playerState.Health}",
                    "将血量限制在0-100范围内"
                );
            }
            
            // 验证状态组合
            if (playerState.IsDead && playerState.Health > 0)
            {
                return ValidationResult.Failure(
                    ValidationFailure.StateInconsistency,
                    "死亡玩家血量大于0",
                    "修正玩家状态"
                );
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 检查位置是否在边界内
        /// </summary>
        private static bool IsPositionInBounds(Vector3 position)
        {
            // 这里应该根据实际地图边界进行检查
            // 简化实现：假设地图是100x100的正方形
            return position.x >= -50f && position.x <= 50f &&
                   position.z >= -50f && position.z <= 50f &&
                   position.y >= -5f && position.y <= 20f;
        }
        
        /// <summary>
        /// 获取最大移动距离
        /// </summary>
        private static float GetMaxMovementDistance(float deltaTime)
        {
            // 基于最大移动速度计算
            const float maxSpeed = 10f; // 最大移动速度
            return maxSpeed * deltaTime * 1.1f; // 允许10%的误差
        }
        
        /// <summary>
        /// 检查是否为有效的状态转换
        /// </summary>
        private static bool IsValidStateTransition(GameState from, GameState to)
        {
            // 定义有效的状态转换
            var validTransitions = new Dictionary<GameState, GameState[]>
            {
                { GameState.Lobby, new[] { GameState.Starting, GameState.Ending } },
                { GameState.Starting, new[] { GameState.Playing, GameState.Ending } },
                { GameState.Playing, new[] { GameState.Meeting, GameState.Ending } },
                { GameState.Meeting, new[] { GameState.Playing, GameState.Ending } },
                { GameState.Ending, new[] { GameState.Lobby } }
            };

            if (validTransitions.ContainsKey(from))
            {
                return Array.Exists(validTransitions[from], state => state == to);
            }

            return false;
        }
        
        /// <summary>
        /// 检查是否为有效的参数类型
        /// </summary>
        private static bool IsValidParameterType(Type type)
        {
            // 允许的参数类型
            var validTypes = new[]
            {
                typeof(int), typeof(float), typeof(bool), typeof(string),
                typeof(Vector3), typeof(Vector2), typeof(Quaternion),
                typeof(PlayerRef), typeof(byte), typeof(short), typeof(long)
            };
            
            return Array.Exists(validTypes, t => t == type || t.IsAssignableFrom(type));
        }
        
        /// <summary>
        /// 验证数据完整性
        /// </summary>
        public static ValidationResult ValidateDataIntegrity(byte[] data, byte[] expectedHash)
        {
            if (data == null || expectedHash == null)
            {
                return ValidationResult.Failure(
                    ValidationFailure.DataCorruption,
                    "数据或哈希为空"
                );
            }
            
            // 计算数据哈希
            byte[] actualHash = ComputeHash(data);
            
            // 比较哈希
            if (!CompareHashes(actualHash, expectedHash))
            {
                return ValidationResult.Failure(
                    ValidationFailure.DataCorruption,
                    "数据完整性验证失败",
                    "重新请求数据"
                );
            }
            
            return ValidationResult.Success();
        }
        
        /// <summary>
        /// 计算数据哈希
        /// </summary>
        private static byte[] ComputeHash(byte[] data)
        {
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                return sha256.ComputeHash(data);
            }
        }
        
        /// <summary>
        /// 比较哈希值
        /// </summary>
        private static bool CompareHashes(byte[] hash1, byte[] hash2)
        {
            if (hash1.Length != hash2.Length) return false;
            
            for (int i = 0; i < hash1.Length; i++)
            {
                if (hash1[i] != hash2[i]) return false;
            }
            
            return true;
        }
    }
    
    /// <summary>
    /// 验证规则
    /// </summary>
    public struct ValidationRule
    {
        public string PropertyName;
        public Func<object, bool> Validator;
        public string ErrorMessage;
    }
    
    /// <summary>
    /// 玩家状态（用于验证）
    /// </summary>
    public struct PlayerValidationState
    {
        public float Health;
        public bool IsDead;
        public bool IsImpostor;
        public Vector3 Position;
        public float LastActionTime;
    }

    /// <summary>
    /// 玩家网络输入（本地定义以避免程序集依赖）
    /// </summary>
    public struct PlayerNetworkInput
    {
        public Vector2 MovementInput;
        public bool InteractPressed;
        public bool ActionPressed;
        public bool ReportPressed;
    }

    /// <summary>
    /// 游戏状态枚举（本地定义以避免程序集依赖）
    /// </summary>
    public enum GameState
    {
        Lobby,      // 大厅状态
        Starting,   // 游戏开始中
        Playing,    // 正常游戏中
        Meeting,    // 会议状态
        Ending      // 游戏结束
    }
}
