using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Security
{
    /// <summary>
    /// 防作弊检测器 - 检测各种作弊行为
    /// </summary>
    public class AntiCheatDetector : MonoBehaviour
    {
        [Header("检测设置")]
        [SerializeField] private bool enableSpeedHackDetection = true;
        [SerializeField] private bool enableTeleportDetection = true;
        [SerializeField] private bool enableWallhackDetection = true;
        [SerializeField] private bool enableTimeManipulationDetection = true;
        
        [Header("速度检测")]
        [SerializeField] private float maxNormalSpeed = 8f;
        [SerializeField] private float maxSprintSpeed = 12f;
        [SerializeField] private float speedCheckInterval = 0.1f;
        [SerializeField] private int speedViolationThreshold = 3;
        
        [Header("瞬移检测")]
        [SerializeField] private float maxTeleportDistance = 15f;
        [SerializeField] private float teleportCheckInterval = 0.05f;
        
        [Header("穿墙检测")]
        [SerializeField] private LayerMask wallLayerMask = 1;
        [SerializeField] private float wallCheckRadius = 0.5f;
        
        [Header("时间操控检测")]
        [SerializeField] private float expectedTickRate = 60f;
        [SerializeField] private float timeDeviationThreshold = 0.1f;
        
        // 玩家追踪数据
        private Dictionary<PlayerRef, PlayerTrackingData> _playerTrackingData = new Dictionary<PlayerRef, PlayerTrackingData>();
        
        // 检测统计
        private Dictionary<PlayerRef, CheatDetectionStats> _detectionStats = new Dictionary<PlayerRef, CheatDetectionStats>();
        
        // 事件
        public event Action<PlayerRef, CheatType, float> OnCheatDetected;
        public event Action<PlayerRef, string> OnSuspiciousActivity;
        
        private void Update()
        {
            if (enabled)
            {
                PerformCheatDetection();
            }
        }
        
        /// <summary>
        /// 注册玩家追踪
        /// </summary>
        public void RegisterPlayer(PlayerRef player, Vector3 initialPosition)
        {
            _playerTrackingData[player] = new PlayerTrackingData
            {
                PlayerId = player,
                LastPosition = initialPosition,
                LastUpdateTime = Time.time,
                PositionHistory = new Queue<Vector3>(),
                SpeedHistory = new Queue<float>(),
                ViolationCount = 0
            };
            
            _detectionStats[player] = new CheatDetectionStats();
        }
        
        /// <summary>
        /// 注销玩家追踪
        /// </summary>
        public void UnregisterPlayer(PlayerRef player)
        {
            _playerTrackingData.Remove(player);
            _detectionStats.Remove(player);
        }
        
        /// <summary>
        /// 更新玩家位置
        /// </summary>
        public void UpdatePlayerPosition(PlayerRef player, Vector3 newPosition)
        {
            if (!_playerTrackingData.ContainsKey(player))
                return;
            
            var trackingData = _playerTrackingData[player];
            float currentTime = Time.time;
            float deltaTime = currentTime - trackingData.LastUpdateTime;
            
            if (deltaTime > 0)
            {
                // 计算移动速度
                float distance = Vector3.Distance(trackingData.LastPosition, newPosition);
                float speed = distance / deltaTime;
                
                // 更新历史数据
                UpdatePositionHistory(trackingData, newPosition);
                UpdateSpeedHistory(trackingData, speed);
                
                // 执行检测
                if (enableSpeedHackDetection)
                {
                    CheckSpeedHack(player, speed, trackingData);
                }
                
                if (enableTeleportDetection)
                {
                    CheckTeleportHack(player, trackingData.LastPosition, newPosition, deltaTime);
                }
                
                if (enableWallhackDetection)
                {
                    CheckWallhack(player, trackingData.LastPosition, newPosition);
                }
                
                // 更新追踪数据
                trackingData.LastPosition = newPosition;
                trackingData.LastUpdateTime = currentTime;
            }
        }
        
        /// <summary>
        /// 执行作弊检测
        /// </summary>
        private void PerformCheatDetection()
        {
            foreach (var kvp in _playerTrackingData)
            {
                var player = kvp.Key;
                var trackingData = kvp.Value;
                
                // 检查时间操控
                if (enableTimeManipulationDetection)
                {
                    CheckTimeManipulation(player, trackingData);
                }
                
                // 检查异常行为模式
                CheckAnomalousPatterns(player, trackingData);
            }
        }
        
        /// <summary>
        /// 检测速度作弊
        /// </summary>
        private void CheckSpeedHack(PlayerRef player, float speed, PlayerTrackingData trackingData)
        {
            // 检查瞬时速度
            if (speed > maxSprintSpeed)
            {
                trackingData.ViolationCount++;
                
                if (trackingData.ViolationCount >= speedViolationThreshold)
                {
                    float confidence = Mathf.Clamp01((speed - maxSprintSpeed) / maxSprintSpeed);
                    ReportCheat(player, CheatType.SpeedHack, confidence);
                    trackingData.ViolationCount = 0; // 重置计数
                }
            }
            else
            {
                // 逐渐减少违规计数
                trackingData.ViolationCount = Mathf.Max(0, trackingData.ViolationCount - 1);
            }
            
            // 检查平均速度
            if (trackingData.SpeedHistory.Count >= 10)
            {
                float averageSpeed = CalculateAverageSpeed(trackingData.SpeedHistory);
                if (averageSpeed > maxNormalSpeed * 1.5f)
                {
                    float confidence = Mathf.Clamp01((averageSpeed - maxNormalSpeed) / maxNormalSpeed);
                    ReportSuspiciousActivity(player, $"持续高速移动: {averageSpeed:F2} m/s");
                }
            }
        }
        
        /// <summary>
        /// 检测瞬移作弊
        /// </summary>
        private void CheckTeleportHack(PlayerRef player, Vector3 lastPosition, Vector3 newPosition, float deltaTime)
        {
            float distance = Vector3.Distance(lastPosition, newPosition);
            float maxAllowedDistance = maxSprintSpeed * deltaTime;
            
            if (distance > maxTeleportDistance && deltaTime < 1f)
            {
                float confidence = Mathf.Clamp01(distance / maxTeleportDistance - 1f);
                ReportCheat(player, CheatType.Teleport, confidence);
            }
            else if (distance > maxAllowedDistance * 2f)
            {
                ReportSuspiciousActivity(player, $"可疑的位置跳跃: {distance:F2}m in {deltaTime:F3}s");
            }
        }
        
        /// <summary>
        /// 检测穿墙作弊
        /// </summary>
        private void CheckWallhack(PlayerRef player, Vector3 fromPosition, Vector3 toPosition)
        {
            // 检查路径上是否有墙壁
            Vector3 direction = (toPosition - fromPosition).normalized;
            float distance = Vector3.Distance(fromPosition, toPosition);
            
            if (Physics.SphereCast(fromPosition, wallCheckRadius, direction, out RaycastHit hit, distance, wallLayerMask))
            {
                // 检查是否穿过了墙壁
                if (hit.distance < distance * 0.9f) // 允许一些误差
                {
                    float confidence = 1f - (hit.distance / distance);
                    ReportCheat(player, CheatType.Wallhack, confidence);
                }
            }
        }
        
        /// <summary>
        /// 检测时间操控
        /// </summary>
        private void CheckTimeManipulation(PlayerRef player, PlayerTrackingData trackingData)
        {
            float currentTime = Time.time;
            float expectedInterval = 1f / expectedTickRate;
            float actualInterval = currentTime - trackingData.LastUpdateTime;
            
            if (actualInterval > 0)
            {
                float deviation = Mathf.Abs(actualInterval - expectedInterval) / expectedInterval;
                
                if (deviation > timeDeviationThreshold)
                {
                    trackingData.TimeDeviationCount++;
                    
                    if (trackingData.TimeDeviationCount >= 5)
                    {
                        float confidence = Mathf.Clamp01(deviation);
                        ReportCheat(player, CheatType.TimeManipulation, confidence);
                        trackingData.TimeDeviationCount = 0;
                    }
                }
                else
                {
                    trackingData.TimeDeviationCount = Mathf.Max(0, trackingData.TimeDeviationCount - 1);
                }
            }
        }
        
        /// <summary>
        /// 检查异常行为模式
        /// </summary>
        private void CheckAnomalousPatterns(PlayerRef player, PlayerTrackingData trackingData)
        {
            // 检查位置历史中的异常模式
            if (trackingData.PositionHistory.Count >= 5)
            {
                // 检查是否有重复的位置（可能是位置锁定）
                if (CheckRepeatingPositions(trackingData.PositionHistory))
                {
                    ReportSuspiciousActivity(player, "检测到位置重复模式");
                }
                
                // 检查是否有不自然的移动模式
                if (CheckUnnaturalMovementPattern(trackingData.PositionHistory))
                {
                    ReportSuspiciousActivity(player, "检测到不自然的移动模式");
                }
            }
        }
        
        /// <summary>
        /// 检查重复位置
        /// </summary>
        private bool CheckRepeatingPositions(Queue<Vector3> positions)
        {
            var positionArray = positions.ToArray();
            int repeatCount = 0;
            
            for (int i = 1; i < positionArray.Length; i++)
            {
                if (Vector3.Distance(positionArray[i], positionArray[i - 1]) < 0.01f)
                {
                    repeatCount++;
                }
            }
            
            return repeatCount >= 3; // 连续3次相同位置
        }
        
        /// <summary>
        /// 检查不自然的移动模式
        /// </summary>
        private bool CheckUnnaturalMovementPattern(Queue<Vector3> positions)
        {
            var positionArray = positions.ToArray();
            
            // 检查是否有完美的直线移动（可能是自动移动）
            if (positionArray.Length >= 3)
            {
                for (int i = 2; i < positionArray.Length; i++)
                {
                    Vector3 dir1 = (positionArray[i - 1] - positionArray[i - 2]).normalized;
                    Vector3 dir2 = (positionArray[i] - positionArray[i - 1]).normalized;
                    
                    float dot = Vector3.Dot(dir1, dir2);
                    if (dot > 0.999f) // 几乎完美的直线
                    {
                        return true;
                    }
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// 更新位置历史
        /// </summary>
        private void UpdatePositionHistory(PlayerTrackingData trackingData, Vector3 newPosition)
        {
            trackingData.PositionHistory.Enqueue(newPosition);
            
            // 保持历史记录大小
            while (trackingData.PositionHistory.Count > 10)
            {
                trackingData.PositionHistory.Dequeue();
            }
        }
        
        /// <summary>
        /// 更新速度历史
        /// </summary>
        private void UpdateSpeedHistory(PlayerTrackingData trackingData, float speed)
        {
            trackingData.SpeedHistory.Enqueue(speed);
            
            // 保持历史记录大小
            while (trackingData.SpeedHistory.Count > 20)
            {
                trackingData.SpeedHistory.Dequeue();
            }
        }
        
        /// <summary>
        /// 计算平均速度
        /// </summary>
        private float CalculateAverageSpeed(Queue<float> speedHistory)
        {
            float sum = 0f;
            foreach (float speed in speedHistory)
            {
                sum += speed;
            }
            return sum / speedHistory.Count;
        }
        
        /// <summary>
        /// 报告作弊行为
        /// </summary>
        private void ReportCheat(PlayerRef player, CheatType cheatType, float confidence)
        {
            if (_detectionStats.ContainsKey(player))
            {
                var stats = _detectionStats[player];
                stats.TotalDetections++;
                stats.LastDetectionTime = Time.time;
                _detectionStats[player] = stats;
            }
            
            UnityEngine.Debug.LogWarning($"[AntiCheat] 检测到作弊: {player} - {cheatType} (置信度: {confidence:F2})");
            OnCheatDetected?.Invoke(player, cheatType, confidence);
        }
        
        /// <summary>
        /// 报告可疑活动
        /// </summary>
        private void ReportSuspiciousActivity(PlayerRef player, string description)
        {
            if (_detectionStats.ContainsKey(player))
            {
                var stats = _detectionStats[player];
                stats.SuspiciousActivities++;
                _detectionStats[player] = stats;
            }
            
            UnityEngine.Debug.Log($"[AntiCheat] 可疑活动: {player} - {description}");
            OnSuspiciousActivity?.Invoke(player, description);
        }
        
        /// <summary>
        /// 获取玩家检测统计
        /// </summary>
        public CheatDetectionStats GetPlayerStats(PlayerRef player)
        {
            return _detectionStats.ContainsKey(player) ? _detectionStats[player] : new CheatDetectionStats();
        }
        
        /// <summary>
        /// 重置玩家统计
        /// </summary>
        public void ResetPlayerStats(PlayerRef player)
        {
            if (_detectionStats.ContainsKey(player))
            {
                _detectionStats[player] = new CheatDetectionStats();
            }
            
            if (_playerTrackingData.ContainsKey(player))
            {
                _playerTrackingData[player].ViolationCount = 0;
                _playerTrackingData[player].TimeDeviationCount = 0;
            }
        }
    }
    
    /// <summary>
    /// 玩家追踪数据
    /// </summary>
    public class PlayerTrackingData
    {
        public PlayerRef PlayerId;
        public Vector3 LastPosition;
        public float LastUpdateTime;
        public Queue<Vector3> PositionHistory;
        public Queue<float> SpeedHistory;
        public int ViolationCount;
        public int TimeDeviationCount;
    }
    
    /// <summary>
    /// 作弊检测统计
    /// </summary>
    [Serializable]
    public struct CheatDetectionStats
    {
        public int TotalDetections;
        public int SuspiciousActivities;
        public float LastDetectionTime;
        public float FirstDetectionTime;
        public Dictionary<CheatType, int> DetectionsByType;
    }
}
