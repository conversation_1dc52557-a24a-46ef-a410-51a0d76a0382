using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Debug
{
    /// <summary>
    /// 网络可视化工具 - 可视化网络连接和数据流
    /// </summary>
    public class NetworkVisualizer : MonoBehaviour
    {
        [Header("可视化设置")]
        [SerializeField] private bool enableVisualization = true;
        [SerializeField] private bool showConnections = true;
        [SerializeField] private bool showDataFlow = true;
        [SerializeField] private bool showSyncObjects = true;
        [SerializeField] private bool showRpcCalls = true;
        
        [Header("视觉效果")]
        [SerializeField] private Color connectionColor = Color.green;
        [SerializeField] private Color dataFlowColor = Color.blue;
        [SerializeField] private Color syncObjectColor = Color.yellow;
        [SerializeField] private Color rpcCallColor = Color.red;
        [SerializeField] private float lineWidth = 2f;
        [SerializeField] private float fadeTime = 2f;
        
        [Header("UI设置")]
        [SerializeField] private bool showLabels = true;
        [SerializeField] private Font labelFont;
        [SerializeField] private int labelFontSize = 12;
        [SerializeField] private Color labelColor = Color.white;
        
        // 可视化数据
        private List<ConnectionVisualization> _connections = new List<ConnectionVisualization>();
        private List<DataFlowVisualization> _dataFlows = new List<DataFlowVisualization>();
        private List<RpcVisualization> _rpcCalls = new List<RpcVisualization>();
        private Dictionary<NetworkObject, ObjectVisualization> _syncObjects = new Dictionary<NetworkObject, ObjectVisualization>();
        
        // 组件引用
        private Camera _camera;
        private INetworkManager _networkManager;
        
        private void Start()
        {
            _camera = Camera.main;
            _networkManager = GetNetworkManager();

            // 订阅网络事件
            if (_networkManager != null)
            {
                // 这里可以订阅网络事件
            }
        }
        
        private void Update()
        {
            if (enableVisualization)
            {
                UpdateVisualizations();
                CleanupExpiredVisualizations();
            }
        }
        
        private void OnDrawGizmos()
        {
            if (!enableVisualization) return;
            
            DrawConnections();
            DrawDataFlows();
            DrawSyncObjects();
            DrawRpcCalls();
        }
        
        private void OnGUI()
        {
            if (!enableVisualization || !showLabels) return;
            
            DrawLabels();
        }
        
        /// <summary>
        /// 更新可视化
        /// </summary>
        private void UpdateVisualizations()
        {
            UpdateSyncObjectVisualizations();
            UpdateConnectionVisualizations();
        }
        
        /// <summary>
        /// 更新同步对象可视化
        /// </summary>
        private void UpdateSyncObjectVisualizations()
        {
            var networkObjects = FindObjectsOfType<NetworkObject>();
            
            foreach (var obj in networkObjects)
            {
                if (!_syncObjects.ContainsKey(obj))
                {
                    _syncObjects[obj] = new ObjectVisualization
                    {
                        NetworkObject = obj,
                        LastPosition = obj.transform.position,
                        LastUpdateTime = Time.time,
                        IsActive = true
                    };
                }
                else
                {
                    var visualization = _syncObjects[obj];
                    visualization.LastPosition = obj.transform.position;
                    visualization.LastUpdateTime = Time.time;
                    _syncObjects[obj] = visualization;
                }
            }
        }
        
        /// <summary>
        /// 更新连接可视化
        /// </summary>
        private void UpdateConnectionVisualizations()
        {
            // 这里可以根据实际的网络连接状态更新可视化
            // 简化实现：假设有一个到服务器的连接
            if (_networkManager != null && IsNetworkConnected())
            {
                var diagnostics = GetNetworkDiagnostics();
                var serverConnection = new ConnectionVisualization
                {
                    StartPosition = Vector3.zero,
                    EndPosition = new Vector3(10, 0, 0), // 假设服务器位置
                    ConnectionType = ConnectionType.Server,
                    Quality = diagnostics?.Quality ?? CustomNetworking.ErrorHandling.NetworkQuality.Poor,
                    StartTime = Time.time,
                    IsActive = true
                };

                // 更新或添加连接
                bool found = false;
                for (int i = 0; i < _connections.Count; i++)
                {
                    if (_connections[i].ConnectionType == ConnectionType.Server)
                    {
                        _connections[i] = serverConnection;
                        found = true;
                        break;
                    }
                }

                if (!found)
                {
                    _connections.Add(serverConnection);
                }
            }
        }
        
        /// <summary>
        /// 清理过期的可视化
        /// </summary>
        private void CleanupExpiredVisualizations()
        {
            float currentTime = Time.time;
            
            // 清理过期的数据流
            _dataFlows.RemoveAll(flow => currentTime - flow.StartTime > fadeTime);
            
            // 清理过期的RPC调用
            _rpcCalls.RemoveAll(rpc => currentTime - rpc.StartTime > fadeTime);
            
            // 清理不活跃的连接
            _connections.RemoveAll(conn => !conn.IsActive && currentTime - conn.StartTime > fadeTime);
        }
        
        /// <summary>
        /// 绘制连接
        /// </summary>
        private void DrawConnections()
        {
            if (!showConnections) return;
            
            Gizmos.color = connectionColor;
            
            foreach (var connection in _connections)
            {
                if (!connection.IsActive) continue;
                
                // 根据连接质量调整颜色
                Color color = GetQualityColor(connection.Quality);
                Gizmos.color = color;
                
                // 绘制连接线
                Gizmos.DrawLine(connection.StartPosition, connection.EndPosition);
                
                // 绘制连接点
                Gizmos.DrawWireSphere(connection.StartPosition, 0.2f);
                Gizmos.DrawWireSphere(connection.EndPosition, 0.2f);
            }
        }
        
        /// <summary>
        /// 绘制数据流
        /// </summary>
        private void DrawDataFlows()
        {
            if (!showDataFlow) return;
            
            foreach (var flow in _dataFlows)
            {
                float elapsed = Time.time - flow.StartTime;
                float progress = elapsed / flow.Duration;
                
                if (progress > 1f) continue;
                
                // 计算当前位置
                Vector3 currentPosition = Vector3.Lerp(flow.StartPosition, flow.EndPosition, progress);
                
                // 根据数据大小调整颜色和大小
                Color color = dataFlowColor;
                color.a = 1f - progress; // 渐隐效果
                Gizmos.color = color;
                
                float size = Mathf.Lerp(0.1f, 0.5f, flow.DataSize / 1000f); // 根据数据大小调整
                Gizmos.DrawWireSphere(currentPosition, size);
                
                // 绘制轨迹
                if (progress > 0.1f)
                {
                    Vector3 previousPosition = Vector3.Lerp(flow.StartPosition, flow.EndPosition, progress - 0.1f);
                    Gizmos.DrawLine(previousPosition, currentPosition);
                }
            }
        }
        
        /// <summary>
        /// 绘制同步对象
        /// </summary>
        private void DrawSyncObjects()
        {
            if (!showSyncObjects) return;
            
            foreach (var kvp in _syncObjects)
            {
                var obj = kvp.Key;
                var visualization = kvp.Value;
                
                if (obj == null || !visualization.IsActive) continue;
                
                // 根据权限设置颜色
                Color color = obj.HasInputAuthority ? Color.green : syncObjectColor;
                Gizmos.color = color;
                
                // 绘制对象边界
                Gizmos.DrawWireCube(obj.transform.position, Vector3.one * 0.5f);
                
                // 绘制同步状态指示器
                Vector3 indicatorPos = obj.transform.position + Vector3.up * 1f;
                Gizmos.DrawWireSphere(indicatorPos, 0.1f);
            }
        }
        
        /// <summary>
        /// 绘制RPC调用
        /// </summary>
        private void DrawRpcCalls()
        {
            if (!showRpcCalls) return;
            
            foreach (var rpc in _rpcCalls)
            {
                float elapsed = Time.time - rpc.StartTime;
                float progress = elapsed / rpc.Duration;
                
                if (progress > 1f) continue;
                
                // 计算当前位置
                Vector3 currentPosition = Vector3.Lerp(rpc.StartPosition, rpc.EndPosition, progress);
                
                // RPC调用效果
                Color color = rpcCallColor;
                color.a = 1f - progress;
                Gizmos.color = color;
                
                // 绘制RPC轨迹
                Gizmos.DrawLine(rpc.StartPosition, currentPosition);
                Gizmos.DrawWireSphere(currentPosition, 0.15f);
            }
        }
        
        /// <summary>
        /// 绘制标签
        /// </summary>
        private void DrawLabels()
        {
            if (_camera == null) return;
            
            // 绘制连接标签
            foreach (var connection in _connections)
            {
                if (!connection.IsActive) continue;
                
                Vector3 midPoint = (connection.StartPosition + connection.EndPosition) * 0.5f;
                Vector3 screenPos = _camera.WorldToScreenPoint(midPoint);
                
                if (screenPos.z > 0)
                {
                    screenPos.y = Screen.height - screenPos.y; // 翻转Y坐标
                    
                    string label = $"{connection.ConnectionType}\n{connection.Quality}";
                    DrawLabel(screenPos, label, labelColor);
                }
            }
            
            // 绘制同步对象标签
            foreach (var kvp in _syncObjects)
            {
                var obj = kvp.Key;
                var visualization = kvp.Value;
                
                if (obj == null || !visualization.IsActive) continue;
                
                Vector3 screenPos = _camera.WorldToScreenPoint(obj.transform.position + Vector3.up * 1.5f);
                
                if (screenPos.z > 0)
                {
                    screenPos.y = Screen.height - screenPos.y;
                    
                    string label = $"{obj.name}\nID: {obj.Id}\n{(obj.HasInputAuthority ? "Local" : "Remote")}";
                    DrawLabel(screenPos, label, labelColor);
                }
            }
        }
        
        /// <summary>
        /// 绘制文本标签
        /// </summary>
        private void DrawLabel(Vector3 position, string text, Color color)
        {
            GUIStyle style = new GUIStyle();
            style.normal.textColor = color;
            style.fontSize = labelFontSize;
            style.alignment = TextAnchor.MiddleCenter;
            
            if (labelFont != null)
            {
                style.font = labelFont;
            }
            
            Vector2 size = style.CalcSize(new GUIContent(text));
            Rect rect = new Rect(position.x - size.x * 0.5f, position.y - size.y * 0.5f, size.x, size.y);
            
            GUI.Label(rect, text, style);
        }
        
        /// <summary>
        /// 根据质量获取颜色
        /// </summary>
        private Color GetQualityColor(CustomNetworking.ErrorHandling.NetworkQuality quality)
        {
            switch (quality)
            {
                case CustomNetworking.ErrorHandling.NetworkQuality.Excellent:
                    return Color.green;
                case CustomNetworking.ErrorHandling.NetworkQuality.Good:
                    return Color.yellow;
                case CustomNetworking.ErrorHandling.NetworkQuality.Fair:
                    return Color.orange;
                case CustomNetworking.ErrorHandling.NetworkQuality.Poor:
                    return Color.red;
                case CustomNetworking.ErrorHandling.NetworkQuality.VeryPoor:
                    return Color.magenta;
                default:
                    return Color.gray;
            }
        }
        
        /// <summary>
        /// 添加数据流可视化
        /// </summary>
        public void AddDataFlow(Vector3 start, Vector3 end, float dataSize, float duration = 1f)
        {
            var flow = new DataFlowVisualization
            {
                StartPosition = start,
                EndPosition = end,
                DataSize = dataSize,
                Duration = duration,
                StartTime = Time.time
            };
            
            _dataFlows.Add(flow);
        }
        
        /// <summary>
        /// 添加RPC调用可视化
        /// </summary>
        public void AddRpcCall(Vector3 start, Vector3 end, string methodName, float duration = 0.5f)
        {
            var rpc = new RpcVisualization
            {
                StartPosition = start,
                EndPosition = end,
                MethodName = methodName,
                Duration = duration,
                StartTime = Time.time
            };
            
            _rpcCalls.Add(rpc);
        }
        
        /// <summary>
        /// 切换可视化显示
        /// </summary>
        public void ToggleVisualization()
        {
            enableVisualization = !enableVisualization;
        }
        
        /// <summary>
        /// 设置可视化选项
        /// </summary>
        public void SetVisualizationOptions(bool connections, bool dataFlow, bool syncObjects, bool rpcCalls)
        {
            showConnections = connections;
            showDataFlow = dataFlow;
            showSyncObjects = syncObjects;
            showRpcCalls = rpcCalls;
        }

        /// <summary>
        /// 检查网络连接状态
        /// </summary>
        private bool IsNetworkConnected()
        {
            if (_networkManager == null)
                return false;

            try
            {
                return _networkManager.IsConnected;
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogWarning($"Failed to get network connection status: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取网络诊断信息
        /// </summary>
        private CustomNetworking.ErrorHandling.NetworkDiagnostics? GetNetworkDiagnostics()
        {
            if (_networkManager == null)
                return null;

            try
            {
                return _networkManager.GetNetworkDiagnostics();
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogWarning($"Failed to get network diagnostics: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取网络管理器实例
        /// </summary>
        private INetworkManager GetNetworkManager()
        {
            // 查找实现了INetworkManager接口的组件
            var allComponents = FindObjectsOfType<MonoBehaviour>();
            foreach (var component in allComponents)
            {
                if (component is INetworkManager networkManager)
                {
                    return networkManager;
                }
            }
            return null;
        }
    }
    
    /// <summary>
    /// 连接可视化数据
    /// </summary>
    [Serializable]
    public struct ConnectionVisualization
    {
        public Vector3 StartPosition;
        public Vector3 EndPosition;
        public ConnectionType ConnectionType;
        public CustomNetworking.ErrorHandling.NetworkQuality Quality;
        public float StartTime;
        public bool IsActive;
    }
    
    /// <summary>
    /// 数据流可视化数据
    /// </summary>
    [Serializable]
    public struct DataFlowVisualization
    {
        public Vector3 StartPosition;
        public Vector3 EndPosition;
        public float DataSize;
        public float Duration;
        public float StartTime;
    }
    
    /// <summary>
    /// RPC可视化数据
    /// </summary>
    [Serializable]
    public struct RpcVisualization
    {
        public Vector3 StartPosition;
        public Vector3 EndPosition;
        public string MethodName;
        public float Duration;
        public float StartTime;
    }
    
    /// <summary>
    /// 对象可视化数据
    /// </summary>
    [Serializable]
    public struct ObjectVisualization
    {
        public NetworkObject NetworkObject;
        public Vector3 LastPosition;
        public float LastUpdateTime;
        public bool IsActive;
    }
    
    /// <summary>
    /// 连接类型
    /// </summary>
    public enum ConnectionType
    {
        Server,     // 服务器连接
        Client,     // 客户端连接
        Peer        // 对等连接
    }
}
