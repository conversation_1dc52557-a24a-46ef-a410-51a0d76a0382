using System;
using UnityEngine;

namespace CustomNetworking.Debug
{
    /// <summary>
    /// 全局静态日志方法容器 - 提供可直接调用的日志方法
    /// </summary>
    public static class GlobalLogMethods
    {
        /// <summary>
        /// 全局Log方法 - 支持直接调用 Log(message)
        /// </summary>
        public static void Log(object message) => Debug.Log(message);

        /// <summary>
        /// 全局Log方法 - 带上下文
        /// </summary>
        public static void Log(object message, UnityEngine.Object context) => Debug.Log(message, context);

        /// <summary>
        /// 全局LogWarning方法 - 支持直接调用 LogWarning(message)
        /// </summary>
        public static void LogWarning(object message) => Debug.LogWarning(message);

        /// <summary>
        /// 全局LogWarning方法 - 带上下文
        /// </summary>
        public static void LogWarning(object message, UnityEngine.Object context) => Debug.LogWarning(message, context);

        /// <summary>
        /// 全局LogError方法 - 支持直接调用 LogError(message)
        /// </summary>
        public static void LogError(object message) => Debug.LogError(message);

        /// <summary>
        /// 全局LogError方法 - 带上下文
        /// </summary>
        public static void LogError(object message, UnityEngine.Object context) => Debug.LogError(message, context);
    }

    /// <summary>
    /// 网络调试工具类 - 兼容Unity Debug类的接口
    /// </summary>
    public static class Debug
    {
        private static bool _enableLogging = true;
        private static string _logPrefix = "[NetworkDebug]";

        /// <summary>
        /// 启用或禁用日志记录
        /// </summary>
        public static bool EnableLogging
        {
            get => _enableLogging;
            set => _enableLogging = value;
        }

        /// <summary>
        /// 记录信息日志 - 兼容Unity Debug.Log调用
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="context">上下文对象（可选）</param>
        public static void Log(object message, UnityEngine.Object context = null)
        {
            if (!_enableLogging) return;

            string formattedMessage = $"{_logPrefix} {message?.ToString() ?? "null"}";

            if (context != null)
                UnityEngine.Debug.Log(formattedMessage, context);
            else
                UnityEngine.Debug.Log(formattedMessage);

            // 同时发送到网络调试管理器（如果存在）
            NotifyDebugManager(message?.ToString() ?? "null", DebugLogType.Info);
        }

        /// <summary>
        /// 记录信息日志 - 重载方法（仅消息）
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void Log(object message)
        {
            Log(message, null);
        }

        /// <summary>
        /// 记录警告日志 - 兼容Unity Debug.LogWarning调用
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="context">上下文对象（可选）</param>
        public static void LogWarning(object message, UnityEngine.Object context = null)
        {
            if (!_enableLogging) return;

            string formattedMessage = $"{_logPrefix} {message?.ToString() ?? "null"}";

            if (context != null)
                UnityEngine.Debug.LogWarning(formattedMessage, context);
            else
                UnityEngine.Debug.LogWarning(formattedMessage);

            // 同时发送到网络调试管理器（如果存在）
            NotifyDebugManager(message?.ToString() ?? "null", DebugLogType.Warning);
        }

        /// <summary>
        /// 记录警告日志 - 重载方法（仅消息）
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void LogWarning(object message)
        {
            LogWarning(message, null);
        }

        /// <summary>
        /// 记录错误日志 - 兼容Unity Debug.LogError调用
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="context">上下文对象（可选）</param>
        public static void LogError(object message, UnityEngine.Object context = null)
        {
            if (!_enableLogging) return;

            string formattedMessage = $"{_logPrefix} {message?.ToString() ?? "null"}";

            if (context != null)
                UnityEngine.Debug.LogError(formattedMessage, context);
            else
                UnityEngine.Debug.LogError(formattedMessage);

            // 同时发送到网络调试管理器（如果存在）
            NotifyDebugManager(message?.ToString() ?? "null", DebugLogType.Error);
        }

        /// <summary>
        /// 记录错误日志 - 重载方法（仅消息）
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void LogError(object message)
        {
            LogError(message, null);
        }

        /// <summary>
        /// 通知网络调试管理器
        /// </summary>
        private static void NotifyDebugManager(string message, DebugLogType type)
        {
            try
            {
                var debugManager = UnityEngine.Object.FindFirstObjectByType<NetworkDebugManager>();
                debugManager?.LogDebug(message, type);
            }
            catch
            {
                // 忽略错误，避免循环依赖
            }
        }
    }

    /// <summary>
    /// Log 类型 - 提供静态方法以支持调用
    /// 注意：由于C#限制，无法直接支持 Log(message) 调用，请使用 Log.Write(message)
    /// </summary>
    public static class Log
    {
        /// <summary>
        /// 记录信息日志 - 使用 Log.Write(message) 调用
        /// </summary>
        public static void Write(object message) => Debug.Log(message);

        /// <summary>
        /// 记录信息日志（带上下文） - 使用 Log.Write(message, context) 调用
        /// </summary>
        public static void Write(object message, UnityEngine.Object context) => Debug.Log(message, context);
    }

    /// <summary>
    /// LogWarning 类型 - 提供静态方法以支持调用
    /// 注意：由于C#限制，无法直接支持 LogWarning(message) 调用，请使用 LogWarning.Write(message)
    /// </summary>
    public static class LogWarning
    {
        /// <summary>
        /// 记录警告日志 - 使用 LogWarning.Write(message) 调用
        /// </summary>
        public static void Write(object message) => Debug.LogWarning(message);

        /// <summary>
        /// 记录警告日志（带上下文） - 使用 LogWarning.Write(message, context) 调用
        /// </summary>
        public static void Write(object message, UnityEngine.Object context) => Debug.LogWarning(message, context);
    }

    /// <summary>
    /// LogError 类型 - 提供静态方法以支持调用
    /// 注意：由于C#限制，无法直接支持 LogError(message) 调用，请使用 LogError.Write(message)
    /// </summary>
    public static class LogError
    {
        /// <summary>
        /// 记录错误日志 - 使用 LogError.Write(message) 调用
        /// </summary>
        public static void Write(object message) => Debug.LogError(message);

        /// <summary>
        /// 记录错误日志（带上下文） - 使用 LogError.Write(message, context) 调用
        /// </summary>
        public static void Write(object message, UnityEngine.Object context) => Debug.LogError(message, context);
    }
}
