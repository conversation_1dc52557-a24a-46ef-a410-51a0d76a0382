using System;
using UnityEngine;

namespace CustomNetworking.Debug
{
    /// <summary>
    /// 调试日志类型
    /// </summary>
    public enum DebugLogType
    {
        Info,       // 信息
        Warning,    // 警告
        Error       // 错误
    }
    
    /// <summary>
    /// 调试日志条目
    /// </summary>
    [Serializable]
    public struct DebugLogEntry
    {
        public string Message;
        public DebugLogType Type;
        public DateTime Timestamp;
        
        public override string ToString()
        {
            return $"[{Timestamp:HH:mm:ss}] [{Type}] {Message}";
        }
    }
    
    /// <summary>
    /// 网络调试统计
    /// </summary>
    [Serializable]
    public struct NetworkDebugStats
    {
        // 性能统计
        public float CurrentFPS;
        public float AverageFPS;
        public float MinFPS;
        public float MaxFPS;
        
        // 网络统计
        public float CurrentLatency;
        public float AverageLatency;
        public float MinLatency;
        public float MaxLatency;
        public float PacketLoss;
        public float CurrentBandwidth;
        public string ConnectionState;
        public string NetworkQuality;
        
        // 同步统计
        public int SyncedObjects;
        public float SyncRate;
        public int TotalSyncOperations;
        
        // 错误统计
        public int TotalErrors;
        public int TotalWarnings;
        public int TotalLogEntries;
        
        // 内存统计
        public long TotalMemory;
        public long UsedMemory;
        public long UnityMemory;
        
        public override string ToString()
        {
            return $"FPS: {CurrentFPS:F1}, 延迟: {CurrentLatency:F1}ms, 对象: {SyncedObjects}";
        }
    }
    
    /// <summary>
    /// 网络性能样本
    /// </summary>
    [Serializable]
    public struct NetworkPerformanceSample
    {
        public float Timestamp;
        public float FPS;
        public float Latency;
        public float Bandwidth;
        public float PacketLoss;
        public int ActiveObjects;
        
        public static NetworkPerformanceSample Create()
        {
            return new NetworkPerformanceSample
            {
                Timestamp = Time.time,
                FPS = 1f / Time.deltaTime,
                Latency = 0f,
                Bandwidth = 0f,
                PacketLoss = 0f,
                ActiveObjects = 0
            };
        }
    }
    
    /// <summary>
    /// 网络对象调试信息
    /// </summary>
    [Serializable]
    public struct NetworkObjectDebugInfo
    {
        public string Name;
        public int ObjectId;
        public Vector3 Position;
        public bool HasInputAuthority;
        public bool IsActive;
        public float LastSyncTime;
        public int SyncCount;
        public string SyncMode;
        
        public override string ToString()
        {
            return $"{Name} (ID: {ObjectId}) - {Position} - Authority: {HasInputAuthority}";
        }
    }
    
    /// <summary>
    /// RPC调试信息
    /// </summary>
    [Serializable]
    public struct RpcDebugInfo
    {
        public string MethodName;
        public string SenderName;
        public string TargetName;
        public float Timestamp;
        public int DataSize;
        public bool IsReliable;
        public RpcStatus Status;
        
        public override string ToString()
        {
            return $"{MethodName} ({SenderName} -> {TargetName}) - {Status}";
        }
    }
    
    /// <summary>
    /// RPC状态
    /// </summary>
    public enum RpcStatus
    {
        Sent,       // 已发送
        Received,   // 已接收
        Failed,     // 失败
        Timeout     // 超时
    }
    
    /// <summary>
    /// 网络事件调试信息
    /// </summary>
    [Serializable]
    public struct NetworkEventDebugInfo
    {
        public string EventType;
        public string Description;
        public float Timestamp;
        public string Source;
        public NetworkEventSeverity Severity;
        
        public override string ToString()
        {
            return $"[{Severity}] {EventType}: {Description} (来源: {Source})";
        }
    }
    
    /// <summary>
    /// 网络事件严重程度
    /// </summary>
    public enum NetworkEventSeverity
    {
        Info,       // 信息
        Warning,    // 警告
        Error,      // 错误
        Critical    // 严重
    }
    
    /// <summary>
    /// 带宽使用统计
    /// </summary>
    [Serializable]
    public struct BandwidthStats
    {
        public float TotalSent;         // 总发送量 (KB)
        public float TotalReceived;     // 总接收量 (KB)
        public float CurrentSendRate;   // 当前发送速率 (KB/s)
        public float CurrentReceiveRate; // 当前接收速率 (KB/s)
        public float PeakSendRate;      // 峰值发送速率 (KB/s)
        public float PeakReceiveRate;   // 峰值接收速率 (KB/s)
        
        public float TotalBandwidth => TotalSent + TotalReceived;
        public float CurrentBandwidth => CurrentSendRate + CurrentReceiveRate;
        
        public override string ToString()
        {
            return $"发送: {CurrentSendRate:F1} KB/s, 接收: {CurrentReceiveRate:F1} KB/s";
        }
    }
    
    /// <summary>
    /// 同步性能统计
    /// </summary>
    [Serializable]
    public struct SyncPerformanceStats
    {
        public int TotalObjects;        // 总对象数
        public int ActiveObjects;       // 活跃对象数
        public int HighPriorityObjects; // 高优先级对象数
        public float AverageSyncRate;   // 平均同步率
        public float InterpolationRate; // 插值率
        public float PredictionRate;    // 预测率
        public float RollbackRate;      // 回滚率
        
        public override string ToString()
        {
            return $"对象: {ActiveObjects}/{TotalObjects}, 同步率: {AverageSyncRate:F1}/s";
        }
    }
    
    /// <summary>
    /// 错误分析报告
    /// </summary>
    [Serializable]
    public struct ErrorAnalysisReport
    {
        public int TotalErrors;
        public int CriticalErrors;
        public int NetworkErrors;
        public int SyncErrors;
        public int SecurityErrors;
        public float ErrorRate;         // 错误率 (错误/分钟)
        public string MostCommonError;  // 最常见错误
        public float MTBF;             // 平均故障间隔时间 (分钟)
        
        public override string ToString()
        {
            return $"总错误: {TotalErrors}, 错误率: {ErrorRate:F2}/min, MTBF: {MTBF:F1}min";
        }
    }
    
    /// <summary>
    /// 网络质量报告
    /// </summary>
    [Serializable]
    public struct NetworkQualityReport
    {
        public float AverageLatency;    // 平均延迟
        public float LatencyVariance;   // 延迟方差
        public float PacketLoss;        // 丢包率
        public float Jitter;           // 抖动
        public float Stability;        // 稳定性评分 (0-1)
        public string QualityGrade;    // 质量等级
        public string Recommendation; // 建议
        
        public override string ToString()
        {
            return $"质量: {QualityGrade}, 延迟: {AverageLatency:F1}ms, 丢包: {PacketLoss * 100:F1}%";
        }
    }
    
    /// <summary>
    /// 调试配置
    /// </summary>
    [Serializable]
    public struct DebugConfig
    {
        public bool EnableUI;
        public bool EnableLogging;
        public bool EnablePerformanceMonitoring;
        public bool EnableNetworkVisualization;
        public bool EnableAutoCapture;
        public float UpdateInterval;
        public int MaxLogEntries;
        public int MaxPerformanceSamples;
        public KeyCode ToggleKey;
        
        public static DebugConfig Default => new DebugConfig
        {
            EnableUI = true,
            EnableLogging = true,
            EnablePerformanceMonitoring = true,
            EnableNetworkVisualization = false,
            EnableAutoCapture = false,
            UpdateInterval = 0.5f,
            MaxLogEntries = 100,
            MaxPerformanceSamples = 60,
            ToggleKey = KeyCode.F1
        };
    }
    
    /// <summary>
    /// 调试命令
    /// </summary>
    public enum DebugCommand
    {
        ShowUI,
        HideUI,
        ClearLogs,
        ForceGC,
        CaptureSnapshot,
        ExportLogs,
        ResetStats,
        ToggleVisualization
    }
    
    /// <summary>
    /// 调试快照
    /// </summary>
    [Serializable]
    public struct DebugSnapshot
    {
        public DateTime Timestamp;
        public NetworkDebugStats Stats;
        public NetworkQualityReport QualityReport;
        public ErrorAnalysisReport ErrorReport;
        public BandwidthStats BandwidthStats;
        public SyncPerformanceStats SyncStats;
        public NetworkObjectDebugInfo[] Objects;
        public DebugLogEntry[] RecentLogs;
        
        public override string ToString()
        {
            return $"快照 [{Timestamp:yyyy-MM-dd HH:mm:ss}] - {Stats}";
        }
    }
}
