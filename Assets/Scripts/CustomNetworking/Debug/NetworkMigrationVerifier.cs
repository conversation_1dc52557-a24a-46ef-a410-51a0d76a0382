using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using CustomNetworking.Core;
using CustomNetworking.Core.RPC;

namespace CustomNetworking.Debug
{
    /// <summary>
    /// 网络迁移验证器 - 验证从Photon Fusion到自定义网络框架的迁移完成情况
    /// </summary>
    public class NetworkMigrationVerifier : MonoBehaviour
    {
        [Header("验证设置")]
        [SerializeField] private bool verifyOnStart = true;
        [SerializeField] private bool showDetailedReport = true;

        private MigrationReport _report = new MigrationReport();

        private void Start()
        {
            if (verifyOnStart)
            {
                VerifyMigration();
            }
        }

        /// <summary>
        /// 验证迁移完成情况
        /// </summary>
        [ContextMenu("验证网络迁移")]
        public void VerifyMigration()
        {
            Debug.Log("[MigrationVerifier] 开始验证网络框架迁移...");

            _report = new MigrationReport();

            // 1. 验证自定义网络组件
            VerifyCustomNetworkComponents();

            // 2. 验证网络对象配置
            VerifyNetworkObjectConfiguration();

            // 3. 验证网络属性使用
            VerifyNetworkedProperties();

            // 4. 验证RPC系统
            VerifyRpcSystem();

            // 5. 验证传输层
            VerifyTransportLayer();

            // 输出报告
            OutputMigrationReport();
        }

        /// <summary>
        /// 验证自定义网络组件
        /// </summary>
        private void VerifyCustomNetworkComponents()
        {
            var networkBehaviours = FindObjectsOfType<NetworkBehaviour>();
            var networkObjects = FindObjectsOfType<NetworkObject>();

            _report.NetworkBehaviourCount = networkBehaviours.Length;
            _report.NetworkObjectCount = networkObjects.Length;

            // 检查每个NetworkBehaviour是否正确配置
            foreach (var behaviour in networkBehaviours)
            {
                var componentInfo = new ComponentInfo
                {
                    Name = behaviour.GetType().Name,
                    GameObject = behaviour.gameObject.name,
                    HasNetworkObject = behaviour.GetComponent<NetworkObject>() != null,
                    IsProperlyConfigured = true
                };

                if (!componentInfo.HasNetworkObject)
                {
                    componentInfo.IsProperlyConfigured = false;
                    componentInfo.Issues.Add("缺少NetworkObject组件");
                }

                _report.Components.Add(componentInfo);
            }

            Debug.Log($"[MigrationVerifier] 找到 {networkBehaviours.Length} 个NetworkBehaviour和 {networkObjects.Length} 个NetworkObject");
        }

        /// <summary>
        /// 验证网络对象配置
        /// </summary>
        private void VerifyNetworkObjectConfiguration()
        {
            var networkObjects = FindObjectsOfType<NetworkObject>();

            foreach (var networkObject in networkObjects)
            {
                var behaviours = networkObject.GetComponents<NetworkBehaviour>();
                if (behaviours.Length == 0)
                {
                    _report.Issues.Add($"NetworkObject在 {networkObject.gameObject.name} 上没有NetworkBehaviour组件");
                }
            }
        }

        /// <summary>
        /// 验证网络属性使用
        /// </summary>
        private void VerifyNetworkedProperties()
        {
            var networkBehaviours = FindObjectsOfType<NetworkBehaviour>();
            int totalNetworkedProperties = 0;

            foreach (var behaviour in networkBehaviours)
            {
                var type = behaviour.GetType();
                var properties = type.GetProperties();

                foreach (var property in properties)
                {
                    var networkedAttr = property.GetCustomAttributes(typeof(NetworkedAttribute), true);
                    if (networkedAttr.Length > 0)
                    {
                        totalNetworkedProperties++;

                        // 验证属性类型
                        if (!IsValidNetworkedType(property.PropertyType))
                        {
                            _report.Issues.Add($"不支持的网络属性类型: {property.PropertyType.Name} 在 {type.Name}.{property.Name}");
                        }
                    }
                }
            }

            _report.NetworkedPropertyCount = totalNetworkedProperties;
            Debug.Log($"[MigrationVerifier] 找到 {totalNetworkedProperties} 个网络属性");
        }

        /// <summary>
        /// 验证RPC系统
        /// </summary>
        private void VerifyRpcSystem()
        {
            // 检查RPC管理器是否存在
            if (RpcManager.Instance == null)
            {
                _report.Issues.Add("RpcManager实例未找到");
                return;
            }

            // 检查是否有RPC方法
            var networkBehaviours = FindObjectsOfType<NetworkBehaviour>();
            int rpcMethodCount = 0;

            foreach (var behaviour in networkBehaviours)
            {
                var type = behaviour.GetType();
                var methods = type.GetMethods();

                foreach (var method in methods)
                {
                    var rpcAttr = method.GetCustomAttributes(typeof(RpcAttribute), true);
                    if (rpcAttr.Length > 0)
                    {
                        rpcMethodCount++;
                    }
                }
            }

            _report.RpcMethodCount = rpcMethodCount;
            Debug.Log($"[MigrationVerifier] 找到 {rpcMethodCount} 个RPC方法");
        }

        /// <summary>
        /// 验证传输层
        /// </summary>
        private void VerifyTransportLayer()
        {
            var networkRunners = FindObjectsOfType<NetworkRunner>();
            _report.NetworkRunnerCount = networkRunners.Length;

            if (networkRunners.Length == 0)
            {
                _report.Issues.Add("未找到NetworkRunner实例");
            }

            // 检查传输层组件
            var webSocketTransports = FindObjectsOfType<WebSocketTransport>();
            _report.TransportCount = webSocketTransports.Length;

            Debug.Log($"[MigrationVerifier] 找到 {networkRunners.Length} 个NetworkRunner和 {webSocketTransports.Length} 个传输层");
        }

        /// <summary>
        /// 检查是否为有效的网络属性类型
        /// </summary>
        private bool IsValidNetworkedType(System.Type type)
        {
            // 基本类型
            if (type.IsPrimitive || type == typeof(string))
                return true;

            // Unity类型
            if (type == typeof(Vector3) || type == typeof(Vector2) || type == typeof(Quaternion) ||
                type == typeof(Color) || type == typeof(Color32))
                return true;

            // 自定义网络类型
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(NetworkString<>))
                return true;

            if (type == typeof(NetworkBool) || type == typeof(PlayerRef) || type == typeof(TickTimer))
                return true;

            return false;
        }

        /// <summary>
        /// 输出迁移报告
        /// </summary>
        private void OutputMigrationReport()
        {
            Debug.Log("=== 网络框架迁移验证报告 ===");
            Debug.Log($"NetworkBehaviour组件: {_report.NetworkBehaviourCount}");
            Debug.Log($"NetworkObject组件: {_report.NetworkObjectCount}");
            Debug.Log($"网络属性: {_report.NetworkedPropertyCount}");
            Debug.Log($"RPC方法: {_report.RpcMethodCount}");
            Debug.Log($"NetworkRunner: {_report.NetworkRunnerCount}");
            Debug.Log($"传输层组件: {_report.TransportCount}");

            if (_report.Issues.Count == 0)
            {
                Debug.Log("✅ 网络框架迁移验证通过！所有组件都正确配置。");
            }
            else
            {
                Debug.LogWarning($"⚠️ 发现 {_report.Issues.Count} 个问题：");
                foreach (var issue in _report.Issues)
                {
                    Debug.LogWarning($"- {issue}");
                }
            }

            if (showDetailedReport)
            {
                Debug.Log("\n=== 详细组件报告 ===");
                foreach (var component in _report.Components)
                {
                    string status = component.IsProperlyConfigured ? "✅" : "❌";
                    Debug.Log($"{status} {component.Name} ({component.GameObject})");

                    if (component.Issues.Count > 0)
                    {
                        foreach (var issue in component.Issues)
                        {
                            Debug.LogWarning($"  - {issue}");
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// 迁移报告
    /// </summary>
    [System.Serializable]
    public class MigrationReport
    {
        public int NetworkBehaviourCount;
        public int NetworkObjectCount;
        public int NetworkedPropertyCount;
        public int RpcMethodCount;
        public int NetworkRunnerCount;
        public int TransportCount;
        public List<string> Issues = new List<string>();
        public List<ComponentInfo> Components = new List<ComponentInfo>();
    }

    /// <summary>
    /// 组件信息
    /// </summary>
    [System.Serializable]
    public class ComponentInfo
    {
        public string Name;
        public string GameObject;
        public bool HasNetworkObject;
        public bool IsProperlyConfigured;
        public List<string> Issues = new List<string>();
    }
}
