using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using System.Collections;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 移动平台触摸按钮
    /// 支持触觉反馈、视觉反馈和性能优化
    /// </summary>
    public class TouchButton : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler, IPointerEnterHandler, IPointerExitHandler
    {
        [Header("按钮设置")]
        [SerializeField] private Image buttonImage;
        [SerializeField] private Text buttonText;
        [SerializeField] private Image iconImage;
        
        [Header("视觉反馈")]
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color pressedColor = Color.gray;
        [SerializeField] private Color disabledColor = Color.gray;
        [SerializeField] private float pressedScale = 0.95f;
        [SerializeField] private float animationDuration = 0.1f;
        
        [Header("触觉反馈")]
        [SerializeField] private bool enableHapticFeedback = true;
        #pragma warning disable CS0414 // Field is assigned but its value is never used
        [SerializeField] private HapticFeedbackType hapticType = HapticFeedbackType.Light;
        #pragma warning restore CS0414
        
        [Header("音效")]
        [SerializeField] private bool enableSoundFeedback = true;
        [SerializeField] private AudioClip pressSound;
        [SerializeField] private AudioClip releaseSound;
        
        [Header("行为设置")]
        [SerializeField] private bool interactable = true;
        [SerializeField] private bool repeatWhenHeld = false;
        [SerializeField] private float repeatRate = 0.1f;
        [SerializeField] private float initialRepeatDelay = 0.5f;
        
        [Header("冷却设置")]
        [SerializeField] private bool enableCooldown = false;
        [SerializeField] private float cooldownTime = 1f;
        [SerializeField] private Image cooldownOverlay;
        
        // 事件
        [Header("事件")]
        public UnityEvent OnButtonPressed;
        public UnityEvent OnButtonReleased;
        public UnityEvent OnButtonHeld;
        public UnityEvent OnCooldownComplete;
        
        // 内部状态
        private bool _isPressed;
        private bool _isHovered;
        private bool _isOnCooldown;
        private Vector3 _originalScale;
        private Coroutine _repeatCoroutine;
        private Coroutine _cooldownCoroutine;
        private AudioSource _audioSource;
        
        // 性能优化
        private float _lastPressTime;
        private const float PRESS_THROTTLE = 0.05f; // 防止过快点击
        
        public enum HapticFeedbackType
        {
            Light,
            Medium,
            Heavy
        }
        
        private void Start()
        {
            InitializeButton();
        }
        
        /// <summary>
        /// 初始化按钮
        /// </summary>
        private void InitializeButton()
        {
            _originalScale = transform.localScale;
            
            // 获取或创建AudioSource
            _audioSource = GetComponent<AudioSource>();
            if (_audioSource == null && enableSoundFeedback)
            {
                _audioSource = gameObject.AddComponent<AudioSource>();
                _audioSource.playOnAwake = false;
            }
            
            // 设置初始状态
            UpdateVisualState();
            
            // 设置冷却覆盖层
            if (cooldownOverlay != null)
            {
                cooldownOverlay.gameObject.SetActive(false);
            }
        }
        
        /// <summary>
        /// 指针按下事件
        /// </summary>
        public void OnPointerDown(PointerEventData eventData)
        {
            if (!CanInteract()) return;
            
            // 防止过快点击
            if (Time.time - _lastPressTime < PRESS_THROTTLE) return;
            _lastPressTime = Time.time;
            
            _isPressed = true;
            
            // 触觉反馈
            TriggerHapticFeedback();
            
            // 音效反馈
            PlayPressSound();
            
            // 视觉反馈
            StartCoroutine(AnimatePress());
            
            // 触发事件
            OnButtonPressed?.Invoke();
            
            // 开始重复按压
            if (repeatWhenHeld)
            {
                _repeatCoroutine = StartCoroutine(RepeatPress());
            }
        }
        
        /// <summary>
        /// 指针抬起事件
        /// </summary>
        public void OnPointerUp(PointerEventData eventData)
        {
            if (!_isPressed) return;
            
            _isPressed = false;
            
            // 停止重复按压
            if (_repeatCoroutine != null)
            {
                StopCoroutine(_repeatCoroutine);
                _repeatCoroutine = null;
            }
            
            // 音效反馈
            PlayReleaseSound();
            
            // 视觉反馈
            StartCoroutine(AnimateRelease());
            
            // 触发事件
            OnButtonReleased?.Invoke();
            
            // 开始冷却
            if (enableCooldown)
            {
                StartCooldown();
            }
        }
        
        /// <summary>
        /// 指针进入事件
        /// </summary>
        public void OnPointerEnter(PointerEventData eventData)
        {
            _isHovered = true;
            UpdateVisualState();
        }
        
        /// <summary>
        /// 指针离开事件
        /// </summary>
        public void OnPointerExit(PointerEventData eventData)
        {
            _isHovered = false;
            UpdateVisualState();
        }
        
        /// <summary>
        /// 检查是否可以交互
        /// </summary>
        private bool CanInteract()
        {
            return interactable && !_isOnCooldown && gameObject.activeInHierarchy;
        }
        
        /// <summary>
        /// 触发触觉反馈
        /// </summary>
        private void TriggerHapticFeedback()
        {
            if (!enableHapticFeedback) return;
            
            #if UNITY_IOS
            switch (hapticType)
            {
                case HapticFeedbackType.Light:
                    // iOS 轻触觉反馈
                    break;
                case HapticFeedbackType.Medium:
                    // iOS 中等触觉反馈
                    break;
                case HapticFeedbackType.Heavy:
                    Handheld.Vibrate();
                    break;
            }
            #elif UNITY_ANDROID
            // Android 震动反馈
            if (hapticType != HapticFeedbackType.Light)
            {
                Handheld.Vibrate();
            }
            #endif
        }
        
        /// <summary>
        /// 播放按下音效
        /// </summary>
        private void PlayPressSound()
        {
            if (enableSoundFeedback && _audioSource != null && pressSound != null)
            {
                _audioSource.PlayOneShot(pressSound);
            }
        }
        
        /// <summary>
        /// 播放释放音效
        /// </summary>
        private void PlayReleaseSound()
        {
            if (enableSoundFeedback && _audioSource != null && releaseSound != null)
            {
                _audioSource.PlayOneShot(releaseSound);
            }
        }
        
        /// <summary>
        /// 按压动画
        /// </summary>
        private IEnumerator AnimatePress()
        {
            Vector3 targetScale = _originalScale * pressedScale;
            float elapsed = 0f;
            
            while (elapsed < animationDuration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / animationDuration;
                transform.localScale = Vector3.Lerp(_originalScale, targetScale, t);
                yield return null;
            }
            
            transform.localScale = targetScale;
            UpdateVisualState();
        }
        
        /// <summary>
        /// 释放动画
        /// </summary>
        private IEnumerator AnimateRelease()
        {
            Vector3 currentScale = transform.localScale;
            float elapsed = 0f;
            
            while (elapsed < animationDuration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / animationDuration;
                transform.localScale = Vector3.Lerp(currentScale, _originalScale, t);
                yield return null;
            }
            
            transform.localScale = _originalScale;
            UpdateVisualState();
        }
        
        /// <summary>
        /// 重复按压协程
        /// </summary>
        private IEnumerator RepeatPress()
        {
            yield return new WaitForSeconds(initialRepeatDelay);
            
            while (_isPressed)
            {
                OnButtonHeld?.Invoke();
                yield return new WaitForSeconds(repeatRate);
            }
        }
        
        /// <summary>
        /// 开始冷却
        /// </summary>
        private void StartCooldown()
        {
            if (_isOnCooldown) return;
            
            _isOnCooldown = true;
            _cooldownCoroutine = StartCoroutine(CooldownCoroutine());
        }
        
        /// <summary>
        /// 冷却协程
        /// </summary>
        private IEnumerator CooldownCoroutine()
        {
            if (cooldownOverlay != null)
            {
                cooldownOverlay.gameObject.SetActive(true);
                cooldownOverlay.fillAmount = 1f;
            }
            
            float elapsed = 0f;
            
            while (elapsed < cooldownTime)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / cooldownTime;
                
                if (cooldownOverlay != null)
                {
                    cooldownOverlay.fillAmount = 1f - progress;
                }
                
                yield return null;
            }
            
            if (cooldownOverlay != null)
            {
                cooldownOverlay.gameObject.SetActive(false);
            }
            
            _isOnCooldown = false;
            UpdateVisualState();
            OnCooldownComplete?.Invoke();
        }
        
        /// <summary>
        /// 更新视觉状态
        /// </summary>
        private void UpdateVisualState()
        {
            if (buttonImage == null) return;

            Color targetColor;

            if (!interactable || _isOnCooldown)
            {
                targetColor = disabledColor;
            }
            else if (_isPressed)
            {
                targetColor = pressedColor;
            }
            else if (_isHovered)
            {
                // 悬停状态使用稍微亮一点的颜色
                targetColor = Color.Lerp(normalColor, pressedColor, 0.3f);
            }
            else
            {
                targetColor = normalColor;
            }

            buttonImage.color = targetColor;
        }
        
        /// <summary>
        /// 设置按钮可交互性
        /// </summary>
        public void SetInteractable(bool value)
        {
            interactable = value;
            UpdateVisualState();
        }
        
        /// <summary>
        /// 设置按钮文本
        /// </summary>
        public void SetButtonText(string text)
        {
            if (buttonText != null)
            {
                buttonText.text = text;
            }
        }
        
        /// <summary>
        /// 设置按钮图标
        /// </summary>
        public void SetButtonIcon(Sprite icon)
        {
            if (iconImage != null)
            {
                iconImage.sprite = icon;
            }
        }
        
        /// <summary>
        /// 强制触发按钮
        /// </summary>
        public void TriggerButton()
        {
            if (CanInteract())
            {
                OnButtonPressed?.Invoke();
                
                if (enableCooldown)
                {
                    StartCooldown();
                }
            }
        }
        
        private void OnDestroy()
        {
            // 清理协程
            if (_repeatCoroutine != null)
            {
                StopCoroutine(_repeatCoroutine);
            }
            
            if (_cooldownCoroutine != null)
            {
                StopCoroutine(_cooldownCoroutine);
            }
        }
    }
}
