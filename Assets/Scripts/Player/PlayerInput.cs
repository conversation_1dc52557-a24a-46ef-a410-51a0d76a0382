using UnityEngine;
using UnityEngine.InputSystem;
using CustomNetworking.Core;
using GooseDuckKill.Player;
// 显式使用UnityEngine的TouchPhase，避免与InputSystem的TouchPhase冲突
using TouchPhase = UnityEngine.TouchPhase;

/// <summary>
/// 处理玩家输入
/// 支持键盘、触摸屏和控制器输入
/// </summary>
public class PlayerInput : MonoBehaviour
{
    [Header("Input Settings")]
    [SerializeField] private float touchMovementSensitivity = 1.0f;
    [SerializeField] private bool useScreenJoystick = true;
    
    // 输入引用
    private InputAction moveAction;
    private InputAction interactAction;
    private InputAction actionAction;
    private InputAction reportAction;

    // 触摸屏虚拟摇杆区域和状态
    private Vector2 joystickCenter;
    private bool joystickActive;
    private int joystickTouchId = -1;
    private Vector2 joystickInput;
    
    // 最后的移动输入
    private Vector2 lastMovementInput;
    
    // 按钮输入状态
    private bool interactPressed;
    private bool actionPressed;
    private bool reportPressed;
    
    // Unity输入系统引用
    private InputActionAsset inputActions;
    
    // 玩家控制器引用
    private PlayerController playerController;

    private void Awake()
    {
        playerController = GetComponent<PlayerController>();
        SetupInputSystem();
    }
    
    private void SetupInputSystem()
    {
        // 加载输入动作资源
        inputActions = Resources.Load<InputActionAsset>("InputActions");
        if (inputActions == null)
        {
            Debug.LogError("无法加载InputActions资源。请确保在Resources文件夹中存在InputActions资源。");
            return;
        }
        
        // 获取动作引用
        moveAction = inputActions.FindAction("Player/Move");
        interactAction = inputActions.FindAction("Player/Interact");
        actionAction = inputActions.FindAction("Player/Action");
        reportAction = inputActions.FindAction("Player/Report");
        
        // 注册回调
        if (moveAction != null) moveAction.performed += OnMove;
        if (interactAction != null) interactAction.performed += OnInteract;
        if (actionAction != null) actionAction.performed += OnAction;
        if (reportAction != null) reportAction.performed += OnReport;
        
        // 启用输入动作
        inputActions.Enable();
    }
    
    private void OnEnable()
    {
        if (inputActions != null)
            inputActions.Enable();
    }
    
    private void OnDisable()
    {
        if (inputActions != null)
            inputActions.Disable();
    }
    
    private void OnDestroy()
    {
        // 移除回调
        if (moveAction != null) moveAction.performed -= OnMove;
        if (interactAction != null) interactAction.performed -= OnInteract;
        if (actionAction != null) actionAction.performed -= OnAction;
        if (reportAction != null) reportAction.performed -= OnReport;
    }
    
    private void Update()
    {
        // 检查是否需要处理触摸屏输入
        if (useScreenJoystick && Input.touchCount > 0)
        {
            ProcessTouchInput();
        }
    }
    
    private void ProcessTouchInput()
    {
        for (int i = 0; i < Input.touchCount; i++)
        {
            Touch touch = Input.GetTouch(i);
            
            // 处理左侧屏幕的触摸作为移动摇杆
            if (touch.position.x < Screen.width * 0.5f)
            {
                switch (touch.phase)
                {
                    case TouchPhase.Began:
                        if (!joystickActive)
                        {
                            joystickActive = true;
                            joystickCenter = touch.position;
                            joystickTouchId = touch.fingerId;
                        }
                        break;
                        
                    case TouchPhase.Moved:
                    case TouchPhase.Stationary:
                        if (joystickActive && touch.fingerId == joystickTouchId)
                        {
                            Vector2 touchDelta = touch.position - joystickCenter;
                            float maxDistance = Screen.height * 0.15f; // 摇杆最大距离
                            
                            // 计算归一化的方向
                            if (touchDelta.magnitude > maxDistance)
                            {
                                touchDelta = touchDelta.normalized * maxDistance;
                            }
                            
                            // 设置移动输入，应用触摸灵敏度
                            joystickInput = (touchDelta / maxDistance) * touchMovementSensitivity;
                            // 确保输入不超过1
                            if (joystickInput.magnitude > 1f)
                            {
                                joystickInput.Normalize();
                            }
                            lastMovementInput = joystickInput;
                        }
                        break;
                        
                    case TouchPhase.Ended:
                    case TouchPhase.Canceled:
                        if (joystickActive && touch.fingerId == joystickTouchId)
                        {
                            joystickActive = false;
                            joystickTouchId = -1;
                            joystickInput = Vector2.zero;
                            lastMovementInput = Vector2.zero;
                        }
                        break;
                }
            }
            // 处理右侧屏幕的触摸作为动作按钮
            else
            {
                if (touch.phase == TouchPhase.Began)
                {
                    // 根据触摸位置决定动作
                    if (touch.position.y > Screen.height * 0.75f)
                    {
                        reportPressed = true;
                    }
                    else if (touch.position.y > Screen.height * 0.5f)
                    {
                        actionPressed = true;
                    }
                    else
                    {
                        interactPressed = true;
                    }
                }
            }
        }
    }
    
    // 输入系统回调
    private void OnMove(InputAction.CallbackContext context)
    {
        if (!useScreenJoystick || Application.platform == RuntimePlatform.WindowsEditor || 
            Application.platform == RuntimePlatform.OSXEditor)
        {
            lastMovementInput = context.ReadValue<Vector2>();
        }
    }
    
    private void OnInteract(InputAction.CallbackContext context)
    {
        interactPressed = context.performed;
    }
    
    private void OnAction(InputAction.CallbackContext context)
    {
        actionPressed = context.performed;
    }
    
    private void OnReport(InputAction.CallbackContext context)
    {
        reportPressed = context.performed;
    }
    
    /// <summary>
    /// 获取网络输入
    /// </summary>
    public PlayerNetworkInput GetNetworkInput()
    {
        PlayerNetworkInput input = new PlayerNetworkInput
        {
            MovementInput = lastMovementInput,
            InteractPressed = interactPressed,
            ActionPressed = actionPressed,
            ReportPressed = reportPressed,
            EmergencyPressed = false // PC平台不支持紧急按钮
        };
        
        // 重置一次性按钮状态
        interactPressed = false;
        actionPressed = false;
        reportPressed = false;
        
        return input;
    }
    
    /// <summary>
    /// 获取当前移动输入
    /// </summary>
    public Vector2 GetMovementInput()
    {
        return lastMovementInput;
    }
} 