using System;
using CustomNetworking.Core;
using UnityEngine;

namespace GooseDuckKill.Player
{
    /// <summary>
    /// 网络输入结构，用于从本地传递到网络层
    /// </summary>
    public struct PlayerNetworkInput : INetworkInput
    {
        public Vector2 MovementInput;
        public NetworkBool InteractPressed;
        public NetworkBool ActionPressed;
        public NetworkBool ReportPressed;
        public NetworkBool EmergencyPressed;
    }

    /// <summary>
    /// 玩家状态枚举
    /// </summary>
    public enum PlayerState
    {
        Normal,     // 正常状态
        Dead,       // 死亡状态
        Ghost,      // 幽灵状态（死后观察）
        InTask,     // 执行任务中
        InMeeting   // 会议中
    }

    /// <summary>
    /// 玩家移动状态枚举
    /// </summary>
    public enum PlayerMovementState
    {
        Idle,       // 静止
        Walking,    // 行走
        Running     // 奔跑（某些角色的特殊能力）
    }

    /// <summary>
    /// 交互类型枚举
    /// </summary>
    public enum InteractionType
    {
        None,
        Task,
        Kill,
        Report,
        Vent,
        Meeting
    }

    /// <summary>
    /// 交互结果
    /// </summary>
    public class InteractionResult
    {
        public bool Success;
        public InteractionType Type;
        public GameObject Target;
        public string Message;

        public static InteractionResult Failed()
        {
            return new InteractionResult { Success = false };
        }

        public static InteractionResult Successful(InteractionType type, GameObject target = null, string message = "")
        {
            return new InteractionResult
            {
                Success = true,
                Type = type,
                Target = target,
                Message = message
            };
        }
    }
} 