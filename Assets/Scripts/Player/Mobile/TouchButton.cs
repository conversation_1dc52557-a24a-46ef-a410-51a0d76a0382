using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;
using UnityEngine.EventSystems;

namespace GooseDuckKill.Player.Mobile
{
    /// <summary>
    /// 触摸按钮组件
    /// 提供移动设备上的虚拟按钮功能
    /// </summary>
    public class TouchButton : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler, IPointerEnterHandler, IPointerExitHandler
    {
        [Header("按钮设置")]
        [SerializeField] private Image buttonImage;
        [SerializeField] private bool interactable = true;
        [SerializeField] private bool repeatPress = false;
        [SerializeField] private float repeatRate = 0.1f;
        
        [Header("视觉反馈")]
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color pressedColor = Color.gray;
        [SerializeField] private Color disabledColor = Color.gray;
        [SerializeField] private float fadeDuration = 0.1f;
        
        [Header("音效设置")]
        [SerializeField] private AudioClip pressSound;
        [SerializeField] private AudioClip releaseSound;
        [SerializeField] private float volume = 1f;
        
        [Header("事件")]
        public UnityEvent OnButtonPressed = new UnityEvent();
        public UnityEvent OnButtonReleased = new UnityEvent();
        public UnityEvent OnButtonHeld = new UnityEvent();
        
        // 状态变量
        private bool _isPressed = false;
        private bool _isPointerInside = false;
        private float _nextRepeatTime = 0f;
        
        // 组件引用
        private AudioSource _audioSource;
        
        /// <summary>
        /// 按钮是否被按下
        /// </summary>
        public bool IsPressed => _isPressed;
        
        /// <summary>
        /// 按钮是否可交互
        /// </summary>
        public bool Interactable
        {
            get => interactable;
            set
            {
                interactable = value;
                UpdateVisualState();
            }
        }
        
        private void Start()
        {
            InitializeButton();
        }
        
        private void Update()
        {
            // 处理重复按压
            if (_isPressed && repeatPress && Time.time >= _nextRepeatTime)
            {
                OnButtonHeld?.Invoke();
                _nextRepeatTime = Time.time + repeatRate;
            }
        }
        
        /// <summary>
        /// 初始化按钮
        /// </summary>
        private void InitializeButton()
        {
            // 获取或创建AudioSource组件
            _audioSource = GetComponent<AudioSource>();
            if (_audioSource == null && (pressSound != null || releaseSound != null))
            {
                _audioSource = gameObject.AddComponent<AudioSource>();
                _audioSource.playOnAwake = false;
                _audioSource.volume = volume;
            }
            
            // 设置初始视觉状态
            UpdateVisualState();
        }
        
        /// <summary>
        /// 处理指针按下事件
        /// </summary>
        public void OnPointerDown(PointerEventData eventData)
        {
            if (!interactable) return;
            
            _isPressed = true;
            _isPointerInside = true;
            
            // 触发按下事件
            OnButtonPressed?.Invoke();
            
            // 播放按下音效
            PlaySound(pressSound);
            
            // 更新视觉状态
            UpdateVisualState();
            
            // 设置重复按压时间
            if (repeatPress)
            {
                _nextRepeatTime = Time.time + repeatRate;
            }
        }
        
        /// <summary>
        /// 处理指针抬起事件
        /// </summary>
        public void OnPointerUp(PointerEventData eventData)
        {
            if (!interactable) return;
            
            bool wasPressed = _isPressed;
            _isPressed = false;
            
            // 只有在指针仍在按钮内时才触发释放事件
            if (wasPressed && _isPointerInside)
            {
                OnButtonReleased?.Invoke();
                PlaySound(releaseSound);
            }
            
            // 更新视觉状态
            UpdateVisualState();
        }
        
        /// <summary>
        /// 处理指针进入事件
        /// </summary>
        public void OnPointerEnter(PointerEventData eventData)
        {
            _isPointerInside = true;
        }
        
        /// <summary>
        /// 处理指针离开事件
        /// </summary>
        public void OnPointerExit(PointerEventData eventData)
        {
            _isPointerInside = false;
            
            // 如果指针离开按钮区域，取消按压状态
            if (_isPressed)
            {
                _isPressed = false;
                UpdateVisualState();
            }
        }
        
        /// <summary>
        /// 更新视觉状态
        /// </summary>
        private void UpdateVisualState()
        {
            if (buttonImage == null) return;
            
            Color targetColor;
            
            if (!interactable)
            {
                targetColor = disabledColor;
            }
            else if (_isPressed)
            {
                targetColor = pressedColor;
            }
            else
            {
                targetColor = normalColor;
            }
            
            // 平滑过渡颜色
            if (fadeDuration > 0)
            {
                StartCoroutine(FadeToColor(targetColor));
            }
            else
            {
                buttonImage.color = targetColor;
            }
        }
        
        /// <summary>
        /// 颜色渐变协程
        /// </summary>
        private System.Collections.IEnumerator FadeToColor(Color targetColor)
        {
            Color startColor = buttonImage.color;
            float elapsedTime = 0f;
            
            while (elapsedTime < fadeDuration)
            {
                elapsedTime += Time.deltaTime;
                float t = elapsedTime / fadeDuration;
                buttonImage.color = Color.Lerp(startColor, targetColor, t);
                yield return null;
            }
            
            buttonImage.color = targetColor;
        }
        
        /// <summary>
        /// 播放音效
        /// </summary>
        private void PlaySound(AudioClip clip)
        {
            if (_audioSource != null && clip != null)
            {
                _audioSource.clip = clip;
                _audioSource.volume = volume;
                _audioSource.Play();
            }
        }
        
        /// <summary>
        /// 设置按钮颜色
        /// </summary>
        public void SetColors(Color normal, Color pressed, Color disabled)
        {
            normalColor = normal;
            pressedColor = pressed;
            disabledColor = disabled;
            UpdateVisualState();
        }
        
        /// <summary>
        /// 模拟按钮按下
        /// </summary>
        public void SimulatePress()
        {
            if (!interactable) return;
            
            OnButtonPressed?.Invoke();
            PlaySound(pressSound);
        }
        
        /// <summary>
        /// 模拟按钮释放
        /// </summary>
        public void SimulateRelease()
        {
            if (!interactable) return;
            
            OnButtonReleased?.Invoke();
            PlaySound(releaseSound);
        }
    }
}
