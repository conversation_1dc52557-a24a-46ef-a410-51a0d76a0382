using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

namespace GooseDuckKill.Player.Mobile
{
    /// <summary>
    /// 虚拟摇杆组件
    /// 提供触摸屏上的虚拟摇杆控制
    /// </summary>
    public class VirtualJoystick : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUp<PERSON><PERSON><PERSON>, IDragHandler
    {
        [Header("摇杆设置")]
        [SerializeField] private RectTransform joystickBackground;
        [SerializeField] private RectTransform joystickHandle;
        [SerializeField] private float joystickRange = 50f;
        [SerializeField] private bool dynamicJoystick = false;
        
        [Header("输入设置")]
        [SerializeField] private float deadZone = 0.1f;
        [SerializeField] private bool snapToCenter = true;
        
        // 输入状态
        private Vector2 _inputVector = Vector2.zero;
        private Vector2 _joystickCenter;
        private bool _isDragging = false;
        
        // 组件引用
        private Canvas _canvas;
        private Camera _camera;
        
        /// <summary>
        /// 当前输入向量
        /// </summary>
        public Vector2 InputVector => _inputVector;
        
        /// <summary>
        /// 是否正在拖拽
        /// </summary>
        public bool IsDragging => _isDragging;
        
        private void Start()
        {
            InitializeJoystick();
        }
        
        /// <summary>
        /// 初始化摇杆
        /// </summary>
        private void InitializeJoystick()
        {
            // 获取Canvas组件
            _canvas = GetComponentInParent<Canvas>();
            
            // 获取相机引用
            if (_canvas.renderMode == RenderMode.ScreenSpaceCamera)
            {
                _camera = _canvas.worldCamera;
            }
            
            // 设置初始位置
            if (joystickBackground != null)
            {
                _joystickCenter = joystickBackground.anchoredPosition;
            }
            
            // 如果是动态摇杆，初始时隐藏
            if (dynamicJoystick)
            {
                SetJoystickVisibility(false);
            }
        }
        
        /// <summary>
        /// 处理指针按下事件
        /// </summary>
        public void OnPointerDown(PointerEventData eventData)
        {
            if (dynamicJoystick)
            {
                // 动态摇杆：在触摸位置显示摇杆
                Vector2 localPoint;
                RectTransformUtility.ScreenPointToLocalPointInRectangle(
                    transform as RectTransform, 
                    eventData.position, 
                    _camera, 
                    out localPoint);
                
                joystickBackground.anchoredPosition = localPoint;
                _joystickCenter = localPoint;
                SetJoystickVisibility(true);
            }
            
            _isDragging = true;
            OnDrag(eventData);
        }
        
        /// <summary>
        /// 处理指针抬起事件
        /// </summary>
        public void OnPointerUp(PointerEventData eventData)
        {
            _isDragging = false;
            _inputVector = Vector2.zero;
            
            // 重置摇杆手柄位置
            if (joystickHandle != null)
            {
                if (snapToCenter)
                {
                    joystickHandle.anchoredPosition = Vector2.zero;
                }
            }
            
            // 如果是动态摇杆，隐藏摇杆
            if (dynamicJoystick)
            {
                SetJoystickVisibility(false);
            }
        }
        
        /// <summary>
        /// 处理拖拽事件
        /// </summary>
        public void OnDrag(PointerEventData eventData)
        {
            if (!_isDragging) return;
            
            Vector2 localPoint;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                joystickBackground, 
                eventData.position, 
                _camera, 
                out localPoint);
            
            // 计算输入向量
            Vector2 inputDirection = localPoint;
            float inputDistance = inputDirection.magnitude;
            
            // 限制在摇杆范围内
            if (inputDistance > joystickRange)
            {
                inputDirection = inputDirection.normalized * joystickRange;
                inputDistance = joystickRange;
            }
            
            // 更新手柄位置
            if (joystickHandle != null)
            {
                joystickHandle.anchoredPosition = inputDirection;
            }
            
            // 计算标准化的输入向量
            float normalizedDistance = inputDistance / joystickRange;
            
            // 应用死区
            if (normalizedDistance < deadZone)
            {
                _inputVector = Vector2.zero;
            }
            else
            {
                // 重新映射到去除死区后的范围
                float adjustedDistance = (normalizedDistance - deadZone) / (1f - deadZone);
                _inputVector = inputDirection.normalized * adjustedDistance;
            }
        }
        
        /// <summary>
        /// 设置摇杆可见性
        /// </summary>
        private void SetJoystickVisibility(bool visible)
        {
            if (joystickBackground != null)
            {
                joystickBackground.gameObject.SetActive(visible);
            }
        }
        
        /// <summary>
        /// 设置摇杆范围
        /// </summary>
        public void SetJoystickRange(float range)
        {
            joystickRange = Mathf.Max(10f, range);
        }
        
        /// <summary>
        /// 设置死区
        /// </summary>
        public void SetDeadZone(float deadZone)
        {
            this.deadZone = Mathf.Clamp01(deadZone);
        }
        
        /// <summary>
        /// 重置摇杆
        /// </summary>
        public void ResetJoystick()
        {
            _inputVector = Vector2.zero;
            _isDragging = false;
            
            if (joystickHandle != null)
            {
                joystickHandle.anchoredPosition = Vector2.zero;
            }
            
            if (dynamicJoystick)
            {
                SetJoystickVisibility(false);
            }
        }
    }
}
