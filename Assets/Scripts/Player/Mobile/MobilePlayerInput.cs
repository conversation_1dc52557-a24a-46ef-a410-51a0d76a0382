using UnityEngine;
using UnityEngine.EventSystems;
using GooseDuckKill.Core;

namespace GooseDuckKill.Player.Mobile
{
    /// <summary>
    /// 移动平台玩家输入系统
    /// 支持触摸控制、虚拟摇杆和手势识别
    /// </summary>
    public class MobilePlayerInput : MonoBehaviour, IPlayerInput
    {
        [Header("虚拟摇杆设置")]
        [SerializeField] private VirtualJoystick movementJoystick;
        [SerializeField] private float joystickDeadZone = 0.1f;
        [SerializeField] private float movementSensitivity = 1f;
        
        [Header("触摸按钮")]
        [SerializeField] private TouchButton interactButton;
        [SerializeField] private TouchButton actionButton;
        [SerializeField] private TouchButton reportButton;
        [SerializeField] private TouchButton emergencyButton;
        
        [Header("手势设置")]
        [SerializeField] private bool enableGestures = true;
        [SerializeField] private float swipeThreshold = 50f;
        [SerializeField] private float tapTimeThreshold = 0.3f;
        [SerializeField] private float doubleTapTimeThreshold = 0.5f;
        
        [Header("移动优化")]
        [SerializeField] private bool enableHapticFeedback = true;
        [SerializeField] private bool adaptToPerformance = true;
        [SerializeField] private int maxTouchPoints = 2;
        
        // 输入状态
        private Vector2 _movementInput;
        private bool _interactPressed;
        private bool _actionPressed;
        private bool _reportPressed;
        private bool _emergencyPressed;
        
        // 手势识别
        private Vector2 _lastTouchPosition;
        private float _lastTapTime;
        private int _tapCount;
        private bool _isGestureActive;
        
        // 性能优化
        private float _lastInputTime;
        private const float INPUT_THROTTLE = 0.016f; // 60fps
        
        // 事件
        public System.Action<Vector2> OnSwipeDetected;
        public System.Action<Vector2> OnDoubleTap;
        public System.Action OnLongPress;
        
        private void Start()
        {
            InitializeMobileInput();
        }
        
        private void Update()
        {
            // 性能优化：限制输入更新频率
            if (Time.time - _lastInputTime < INPUT_THROTTLE && !adaptToPerformance)
                return;
                
            UpdateMovementInput();
            UpdateTouchInput();
            UpdateGestureInput();
            
            _lastInputTime = Time.time;
        }
        
        /// <summary>
        /// 初始化移动输入系统
        /// </summary>
        private void InitializeMobileInput()
        {
            // 设置触摸按钮事件
            if (interactButton != null)
            {
                interactButton.OnButtonPressed.AddListener(() => {
                    _interactPressed = true;
                    TriggerHapticFeedback(HapticType.Light);
                });
            }
            
            if (actionButton != null)
            {
                actionButton.OnButtonPressed.AddListener(() => {
                    _actionPressed = true;
                    TriggerHapticFeedback(HapticType.Medium);
                });
            }
            
            if (reportButton != null)
            {
                reportButton.OnButtonPressed.AddListener(() => {
                    _reportPressed = true;
                    TriggerHapticFeedback(HapticType.Heavy);
                });
            }
            
            if (emergencyButton != null)
            {
                emergencyButton.OnButtonPressed.AddListener(() => {
                    _emergencyPressed = true;
                    TriggerHapticFeedback(HapticType.Heavy);
                });
            }
            
            // 适配设备性能
            if (adaptToPerformance)
            {
                AdaptToDevicePerformance();
            }
        }
        
        /// <summary>
        /// 更新移动输入
        /// </summary>
        private void UpdateMovementInput()
        {
            if (movementJoystick != null)
            {
                Vector2 joystickInput = movementJoystick.InputVector;
                
                // 应用死区
                if (joystickInput.magnitude < joystickDeadZone)
                {
                    joystickInput = Vector2.zero;
                }
                
                // 应用灵敏度
                _movementInput = joystickInput * movementSensitivity;
            }
        }
        
        /// <summary>
        /// 更新触摸输入
        /// </summary>
        private void UpdateTouchInput()
        {
            // 限制触摸点数量以优化性能
            int touchCount = Mathf.Min(Input.touchCount, maxTouchPoints);
            
            for (int i = 0; i < touchCount; i++)
            {
                Touch touch = Input.GetTouch(i);
                ProcessTouch(touch);
            }
        }
        
        /// <summary>
        /// 处理单个触摸
        /// </summary>
        private void ProcessTouch(Touch touch)
        {
            switch (touch.phase)
            {
                case TouchPhase.Began:
                    OnTouchBegan(touch);
                    break;
                    
                case TouchPhase.Moved:
                    OnTouchMoved(touch);
                    break;
                    
                case TouchPhase.Ended:
                    OnTouchEnded(touch);
                    break;
                    
                case TouchPhase.Canceled:
                    OnTouchCanceled(touch);
                    break;
            }
        }
        
        /// <summary>
        /// 触摸开始
        /// </summary>
        private void OnTouchBegan(Touch touch)
        {
            _lastTouchPosition = touch.position;
            _isGestureActive = true;
            
            // 检查双击
            float timeSinceLastTap = Time.time - _lastTapTime;
            if (timeSinceLastTap < doubleTapTimeThreshold)
            {
                _tapCount++;
            }
            else
            {
                _tapCount = 1;
            }
            
            _lastTapTime = Time.time;
        }
        
        /// <summary>
        /// 触摸移动
        /// </summary>
        private void OnTouchMoved(Touch touch)
        {
            if (!_isGestureActive) return;
            
            Vector2 deltaPosition = touch.position - _lastTouchPosition;
            
            // 检查是否为滑动手势
            if (deltaPosition.magnitude > swipeThreshold)
            {
                OnSwipeDetected?.Invoke(deltaPosition.normalized);
                _isGestureActive = false;
            }
        }
        
        /// <summary>
        /// 触摸结束
        /// </summary>
        private void OnTouchEnded(Touch touch)
        {
            if (!_isGestureActive) return;
            
            float touchDuration = Time.time - _lastTapTime;
            
            // 检查点击类型
            if (touchDuration < tapTimeThreshold)
            {
                if (_tapCount >= 2)
                {
                    OnDoubleTap?.Invoke(touch.position);
                    _tapCount = 0;
                }
            }
            else
            {
                OnLongPress?.Invoke();
            }
            
            _isGestureActive = false;
        }
        
        /// <summary>
        /// 触摸取消
        /// </summary>
        private void OnTouchCanceled(Touch touch)
        {
            _isGestureActive = false;
        }
        
        /// <summary>
        /// 更新手势输入
        /// </summary>
        private void UpdateGestureInput()
        {
            if (!enableGestures) return;
            
            // 这里可以添加更复杂的手势识别逻辑
            // 例如：捏合缩放、旋转等
        }
        
        /// <summary>
        /// 触发触觉反馈
        /// </summary>
        private void TriggerHapticFeedback(HapticType type)
        {
            if (!enableHapticFeedback) return;
            
            #if UNITY_IOS
            switch (type)
            {
                case HapticType.Light:
                    // iOS 轻触觉反馈
                    break;
                case HapticType.Medium:
                    // iOS 中等触觉反馈
                    break;
                case HapticType.Heavy:
                    Handheld.Vibrate();
                    break;
            }
            #elif UNITY_ANDROID
            // Android 震动反馈
            if (type != HapticType.Light)
            {
                Handheld.Vibrate();
            }
            #endif
        }
        
        /// <summary>
        /// 适配设备性能
        /// </summary>
        private void AdaptToDevicePerformance()
        {
            // 根据设备性能调整输入设置
            int deviceMemory = SystemInfo.systemMemorySize;
            
            if (deviceMemory < 3000) // 3GB以下
            {
                // 低端设备优化
                maxTouchPoints = 1;
                enableGestures = false;
                movementSensitivity = 0.8f;
            }
            else if (deviceMemory < 6000) // 6GB以下
            {
                // 中端设备设置
                maxTouchPoints = 2;
                enableGestures = true;
                movementSensitivity = 1f;
            }
            else
            {
                // 高端设备设置
                maxTouchPoints = 2;
                enableGestures = true;
                movementSensitivity = 1.2f;
            }
        }
        
        /// <summary>
        /// 获取网络输入
        /// </summary>
        public PlayerNetworkInput GetNetworkInput()
        {
            var input = new PlayerNetworkInput
            {
                MovementInput = _movementInput,
                InteractPressed = _interactPressed,
                ActionPressed = _actionPressed,
                ReportPressed = _reportPressed,
                EmergencyPressed = _emergencyPressed
            };
            
            // 重置按钮状态
            _interactPressed = false;
            _actionPressed = false;
            _reportPressed = false;
            _emergencyPressed = false;
            
            return input;
        }
        
        /// <summary>
        /// 设置按钮可见性
        /// </summary>
        public void SetButtonVisibility(ButtonType buttonType, bool visible)
        {
            switch (buttonType)
            {
                case ButtonType.Interact:
                    if (interactButton != null)
                        interactButton.gameObject.SetActive(visible);
                    break;
                    
                case ButtonType.Action:
                    if (actionButton != null)
                        actionButton.gameObject.SetActive(visible);
                    break;
                    
                case ButtonType.Report:
                    if (reportButton != null)
                        reportButton.gameObject.SetActive(visible);
                    break;
                    
                case ButtonType.Emergency:
                    if (emergencyButton != null)
                        emergencyButton.gameObject.SetActive(visible);
                    break;
            }
        }
        
        /// <summary>
        /// 设置摇杆可见性
        /// </summary>
        public void SetJoystickVisibility(bool visible)
        {
            if (movementJoystick != null)
            {
                movementJoystick.gameObject.SetActive(visible);
            }
        }
    }
    
    /// <summary>
    /// 触觉反馈类型
    /// </summary>
    public enum HapticType
    {
        Light,
        Medium,
        Heavy
    }
    
    /// <summary>
    /// 按钮类型
    /// </summary>
    public enum ButtonType
    {
        Interact,
        Action,
        Report,
        Emergency
    }
}
