using System;
using System.Collections;
using UnityEngine;
using CustomNetworking.Core;
using GooseDuckKill.Player;

/// <summary>
/// 处理玩家移动
/// 使用DOTween实现平滑移动和方向转换
/// </summary>
public class PlayerMovement : NetworkBehaviour
{
    [Header("Movement Settings")]
    [SerializeField] private float moveSpeed = 5f;
    [SerializeField] private float rotationSpeed = 10f;
    [SerializeField] private float accelerationTime = 0.2f;
    [SerializeField] private float decelerationTime = 0.15f;
    
    [Header("Animation")]
    [SerializeField] private float directionChangeThreshold = 0.1f;
    
    [Header("Mobile Platform Settings")]
    [SerializeField] private float mobileSpeedMultiplier = 0.8f;
    
    // 网络状态
    [Networked] private Vector2 NetworkedMovementDirection { get; set; }
    [Networked] private NetworkBool IsMoving { get; set; }
    [Networked] private PlayerMovementState MovementState { get; set; }
    
    // 组件引用
    private Rigidbody2D rb;
    private PlayerController playerController;
    private PlayerAnimation playerAnimation;
    private SpriteRenderer spriteRenderer;
    
    // 本地状态
    private Vector2 currentMovementDirection;
    private Vector2 targetMovementDirection;
    private float currentSpeed;
    private float targetSpeed;
    private bool isLocalPlayer;
    private bool isMobilePlatform;
    
    // 速度变化协程
    private Coroutine speedCoroutine;
    
    // 事件
    public Action<PlayerMovementState> OnMovementStateChanged;
    public Action<Vector2> OnDirectionChanged;
    
    public Vector2 CurrentDirection => currentMovementDirection;
    public PlayerMovementState CurrentMovementState => MovementState;
    public bool IsCurrentlyMoving => IsMoving;
    
    protected override void Awake()
    {
        base.Awake();

        rb = GetComponent<Rigidbody2D>();
        playerController = GetComponent<PlayerController>();
        playerAnimation = GetComponent<PlayerAnimation>();
        spriteRenderer = GetComponentInChildren<SpriteRenderer>();

        // 检测是否是移动平台
        isMobilePlatform = Application.isMobilePlatform;
    }
    
    public override void Spawned()
    {
        base.Spawned();
        
        // 检查是否是本地玩家
        isLocalPlayer = Object.HasInputAuthority;
        
        // 初始化值
        currentMovementDirection = Vector2.zero;
        targetMovementDirection = Vector2.zero;
        currentSpeed = 0f;
        targetSpeed = 0f;
        
        // 设置网络值
        NetworkedMovementDirection = Vector2.zero;
        IsMoving = false;
        MovementState = PlayerMovementState.Idle;
    }
    
    private void OnEnable()
    {
        // 重置状态
        if (speedCoroutine != null)
        {
            StopCoroutine(speedCoroutine);
            speedCoroutine = null;
        }
    }

    private void OnDisable()
    {
        // 清理协程
        if (speedCoroutine != null)
        {
            StopCoroutine(speedCoroutine);
            speedCoroutine = null;
        }
    }
    
    /// <summary>
    /// 处理移动输入
    /// </summary>
    public void ProcessMovement(Vector2 movementInput)
    {
        // 只在本地玩家上执行
        if (!isLocalPlayer) return;
        
        // 处理移动输入
        Vector2 newTargetDirection = movementInput.normalized;
        bool isNowMoving = newTargetDirection.magnitude > 0.01f;
        
        // 更新目标方向和速度
        targetMovementDirection = newTargetDirection;
        targetSpeed = isNowMoving ? GetCurrentMoveSpeed() : 0f;
        
        // 更新网络状态
        if (isNowMoving != IsMoving || MovementState == PlayerMovementState.Idle && isNowMoving)
        {
            IsMoving = isNowMoving;
            MovementState = isNowMoving ? PlayerMovementState.Walking : PlayerMovementState.Idle;
            OnMovementStateChanged?.Invoke(MovementState);
        }
        
        // 更新网络方向
        NetworkedMovementDirection = targetMovementDirection;
    }
    
    // 在FixedUpdateNetwork中应用移动
    public override void FixedUpdateNetwork()
    {
        if (Object.HasStateAuthority)
        {
            // 服务器上的运动处理
            MovePlayer(NetworkedMovementDirection, Time.fixedDeltaTime);
        }
    }
    
    // 在普通Update中更新视觉效果
    private void Update()
    {
        // 处理平滑方向变化
        UpdateSmoothMovement();
        
        // 更新动画和朝向
        UpdateVisuals();
    }
    
    private void UpdateSmoothMovement()
    {
        // 如果本地玩家，使用目标方向，否则使用网络方向
        Vector2 targetDir = isLocalPlayer ? targetMovementDirection : NetworkedMovementDirection;
        
        // 平滑方向变化
        if ((targetDir - currentMovementDirection).magnitude > directionChangeThreshold)
        {
            currentMovementDirection = Vector2.Lerp(
                currentMovementDirection,
                targetDir,
                Time.deltaTime * rotationSpeed
            );
            
            OnDirectionChanged?.Invoke(currentMovementDirection);
        }
        
        // 使用协程平滑速度变化
        if (Mathf.Abs(targetSpeed - currentSpeed) > 0.01f)
        {
            if (speedCoroutine != null)
            {
                StopCoroutine(speedCoroutine);
            }

            float duration = targetSpeed > currentSpeed ? accelerationTime : decelerationTime;
            speedCoroutine = StartCoroutine(SmoothSpeedChange(targetSpeed, duration, targetSpeed > currentSpeed));
        }
    }
    
    private void UpdateVisuals()
    {
        // 更新方向朝向
        if (currentMovementDirection.magnitude > 0.01f)
        {
            // 左右翻转精灵
            if (Mathf.Abs(currentMovementDirection.x) > 0.1f)
            {
                spriteRenderer.flipX = currentMovementDirection.x < 0;
            }
        }
    }
    
    private void MovePlayer(Vector2 direction, float deltaTime)
    {
        // 计算移动
        Vector2 movement = direction * (moveSpeed * deltaTime);
        
        // 应用移动
        if (rb != null)
        {
            rb.MovePosition(rb.position + movement);
        }
        else
        {
            transform.position += new Vector3(movement.x, movement.y, 0);
        }
    }
    
    private float GetCurrentMoveSpeed()
    {
        float speed = moveSpeed;
        
        // 移动平台速度调整
        if (isMobilePlatform)
        {
            speed *= mobileSpeedMultiplier;
        }
        
        // 可以在这里添加其他速度修饰符（如角色能力、状态效果等）
        
        return speed;
    }
    
    // 停止所有移动
    public void StopMovement()
    {
        targetMovementDirection = Vector2.zero;
        targetSpeed = 0f;
        NetworkedMovementDirection = Vector2.zero;
        IsMoving = false;
        MovementState = PlayerMovementState.Idle;
        
        // 立即停止
        if (speedCoroutine != null)
        {
            StopCoroutine(speedCoroutine);
        }
        
        currentSpeed = 0f;
        
        // 通知动画系统
        OnMovementStateChanged?.Invoke(MovementState);
    }
    
    // 强制设置位置（例如，在通过传送点或被强制移动时）
    public void TeleportTo(Vector3 position)
    {
        if (Object.HasStateAuthority)
        {
            transform.position = position;
            
            if (rb != null)
            {
                rb.position = position;
                rb.linearVelocity = Vector2.zero;
            }
        }
    }

    /// <summary>
    /// 平滑速度变化协程
    /// </summary>
    private IEnumerator SmoothSpeedChange(float targetSpeed, float duration, bool isAccelerating)
    {
        float startSpeed = currentSpeed;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;

            // 应用缓动曲线
            if (isAccelerating)
            {
                // 加速使用 OutQuad 缓动
                t = 1f - (1f - t) * (1f - t);
            }
            else
            {
                // 减速使用 InQuad 缓动
                t = t * t;
            }

            currentSpeed = Mathf.Lerp(startSpeed, targetSpeed, t);
            yield return null;
        }

        currentSpeed = targetSpeed;
        speedCoroutine = null;
    }
}