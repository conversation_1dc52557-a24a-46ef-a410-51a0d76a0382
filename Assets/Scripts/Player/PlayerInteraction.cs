using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;
using GooseDuckKill.Player;

/// <summary>
/// 处理玩家交互系统
/// </summary>
public class PlayerInteraction : NetworkBehaviour
{
    [Header("Interaction Settings")]
    [SerializeField] private float interactionRadius = 2f;
    [SerializeField] private LayerMask interactableLayers;
    [SerializeField] private float killCooldown = 30f;
    [SerializeField] private float killDistance = 1.5f;
    
    [Header("Debug")]
    [SerializeField] private bool showInteractionRadius = false;
    
    // 网络变量
    [Networked] private TickTimer KillCooldownTimer { get; set; }
    
    // 组件引用
    private PlayerController playerController;
    private CircleCollider2D interactionCollider;
    
    // 可交互对象列表
    private List<GameObject> interactableObjects = new List<GameObject>();
    private GameObject closestInteractable;
    
    // 事件
    public Action<GameObject> OnInteractableEnter;
    public Action<GameObject> OnInteractableExit;
    public Action<GameObject> OnClosestInteractableChanged;
    
    // 属性
    public bool CanKill => KillCooldownTimer.ExpiredOrNotRunning(Runner);
    public float KillCooldownRemaining => KillCooldownTimer.RemainingTime(Runner) ?? 0f;
    public bool HasInteractable => closestInteractable != null;
    public GameObject ClosestInteractable => closestInteractable;
    
    protected override void Awake()
    {
        base.Awake();

        playerController = GetComponent<PlayerController>();

        // 创建或获取交互碰撞器
        interactionCollider = GetComponent<CircleCollider2D>();
        if (interactionCollider == null)
        {
            interactionCollider = gameObject.AddComponent<CircleCollider2D>();
            interactionCollider.isTrigger = true;
        }

        interactionCollider.radius = interactionRadius;
    }
    
    public override void Spawned()
    {
        base.Spawned();
        
        // 初始化网络变量
        if (Object.HasStateAuthority)
        {
            ResetKillCooldown();
        }
    }
    
    private void OnTriggerEnter2D(Collider2D other)
    {
        // 检查是否是可交互对象
        if (IsInLayerMask(other.gameObject.layer, interactableLayers))
        {
            // 添加到可交互对象列表
            if (!interactableObjects.Contains(other.gameObject))
            {
                interactableObjects.Add(other.gameObject);
                OnInteractableEnter?.Invoke(other.gameObject);
                
                // 更新最近的可交互对象
                UpdateClosestInteractable();
            }
        }
    }
    
    private void OnTriggerExit2D(Collider2D other)
    {
        // 检查是否是可交互对象
        if (IsInLayerMask(other.gameObject.layer, interactableLayers))
        {
            // 从可交互对象列表中移除
            if (interactableObjects.Contains(other.gameObject))
            {
                interactableObjects.Remove(other.gameObject);
                OnInteractableExit?.Invoke(other.gameObject);
                
                // 更新最近的可交互对象
                UpdateClosestInteractable();
            }
        }
    }
    
    private void Update()
    {
        // 每帧更新最近的可交互对象
        UpdateClosestInteractable();
    }
    
    // 更新最近的可交互对象
    private void UpdateClosestInteractable()
    {
        if (interactableObjects.Count == 0)
        {
            if (closestInteractable != null)
            {
                GameObject previous = closestInteractable;
                closestInteractable = null;
                OnClosestInteractableChanged?.Invoke(null);
            }
            return;
        }
        
        // 找到最近的可交互对象
        GameObject closest = null;
        float minDistance = float.MaxValue;
        
        foreach (GameObject obj in interactableObjects)
        {
            if (obj == null) continue;
            
            float distance = Vector2.Distance(transform.position, obj.transform.position);
            if (distance < minDistance)
            {
                minDistance = distance;
                closest = obj;
            }
        }
        
        // 如果最近的可交互对象发生变化，通知监听者
        if (closest != closestInteractable)
        {
            closestInteractable = closest;
            OnClosestInteractableChanged?.Invoke(closest);
        }
    }
    
    // 尝试与最近的可交互对象交互
    public InteractionResult TryInteract()
    {
        if (closestInteractable == null)
        {
            return InteractionResult.Failed();
        }
        
        // 根据可交互对象的类型执行不同的交互
        InteractionType type = DetermineInteractionType(closestInteractable);
        
        // 执行交互
        switch (type)
        {
            case InteractionType.Task:
                return InteractWithTask(closestInteractable);
            case InteractionType.Report:
                return InteractWithReport(closestInteractable);
            case InteractionType.Vent:
                return InteractWithVent(closestInteractable);
            case InteractionType.Meeting:
                return InteractWithMeeting(closestInteractable);
            default:
                return InteractionResult.Failed();
        }
    }
    
    // 尝试杀死附近的玩家
    public InteractionResult TryKill()
    {
        // 检查角色是否有权限杀人
        if (!playerController.HasKillAbility)
        {
            return InteractionResult.Failed();
        }
        
        // 检查冷却时间
        if (!CanKill)
        {
            return InteractionResult.Failed();
        }
        
        // 查找附近可杀的玩家
        GameObject target = FindNearestKillablePlayer();
        if (target == null)
        {
            return InteractionResult.Failed();
        }
        
        // 服务器上执行杀人逻辑
        if (Object.HasStateAuthority)
        {
            // 重置冷却
            ResetKillCooldown();
            
            // 获取目标玩家控制器
            PlayerController targetController = target.GetComponent<PlayerController>();
            if (targetController != null)
            {
                // 标记玩家为死亡
                targetController.Die(Object.InputAuthority);
            }
        }
        
        return InteractionResult.Successful(InteractionType.Kill, target);
    }
    
    // 重置杀人冷却时间
    private void ResetKillCooldown()
    {
        if (Object.HasStateAuthority)
        {
            KillCooldownTimer = TickTimer.CreateFromSeconds(Runner, killCooldown);
        }
    }
    
    // 确定交互类型
    private InteractionType DetermineInteractionType(GameObject interactable)
    {
        // 这里需要根据游戏中的交互对象类型来确定
        // 可以使用标签、组件或其他方式来区分不同类型
        
        if (interactable.CompareTag("Task"))
        {
            return InteractionType.Task;
        }
        else if (interactable.CompareTag("DeadBody"))
        {
            return InteractionType.Report;
        }
        else if (interactable.CompareTag("Vent"))
        {
            return InteractionType.Vent;
        }
        else if (interactable.CompareTag("EmergencyButton"))
        {
            return InteractionType.Meeting;
        }
        
        return InteractionType.None;
    }
    
    // 与任务交互
    private InteractionResult InteractWithTask(GameObject task)
    {
        // 任务交互逻辑
        return InteractionResult.Successful(InteractionType.Task, task);
    }
    
    // 与报告交互
    private InteractionResult InteractWithReport(GameObject body)
    {
        // 报告尸体逻辑
        return InteractionResult.Successful(InteractionType.Report, body);
    }
    
    // 与通风口交互
    private InteractionResult InteractWithVent(GameObject vent)
    {
        // 通风口交互逻辑
        // 检查是否有使用通风口的权限
        if (!playerController.HasVentAbility)
        {
            return InteractionResult.Failed();
        }
        
        return InteractionResult.Successful(InteractionType.Vent, vent);
    }
    
    // 与紧急会议按钮交互
    private InteractionResult InteractWithMeeting(GameObject meetingButton)
    {
        // 紧急会议逻辑
        return InteractionResult.Successful(InteractionType.Meeting, meetingButton);
    }
    
    // 寻找最近的可杀玩家
    private GameObject FindNearestKillablePlayer()
    {
        // 获取所有玩家
        PlayerController[] players = UnityEngine.Object.FindObjectsByType<PlayerController>(FindObjectsSortMode.None);
        
        GameObject nearest = null;
        float minDistance = killDistance;
        
        foreach (PlayerController player in players)
        {
            // 忽略自己
            if (player == playerController) continue;
            
            // 忽略已经死亡的玩家
            if (player.IsDead) continue;
            
            // 计算距离
            float distance = Vector2.Distance(transform.position, player.transform.position);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = player.gameObject;
            }
        }
        
        return nearest;
    }
    
    // 辅助方法：检查层是否在层掩码中
    private bool IsInLayerMask(int layer, LayerMask layerMask)
    {
        return layerMask == (layerMask | (1 << layer));
    }
    
    // 绘制交互范围（仅在编辑器中）
    private void OnDrawGizmos()
    {
        if (showInteractionRadius)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, interactionRadius);
            
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, killDistance);
        }
    }
} 