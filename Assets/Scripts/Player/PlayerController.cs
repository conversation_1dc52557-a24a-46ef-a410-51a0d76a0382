using System;
using UnityEngine;
using CustomNetworking.Core;
using GooseDuckKill.Core;

namespace GooseDuckKill.Player
{
    /// <summary>
    /// 玩家控制器核心类
    /// 协调玩家的所有组件和行为
    /// </summary>
    public class PlayerController : <PERSON>Behaviour, IPlayerController
{
    [Header("Player Settings")]
    [SerializeField] private string playerName = "Player";
    [SerializeField] private Color playerColor = Color.white;
    
    [Header("Components")]
    [SerializeField] private SpriteRenderer playerSprite;
    [SerializeField] private Transform nameTagTransform;
    [SerializeField] private TextMesh nameTagText;
    
    // 网络状态 - 优化同步频率
    [Networked] private NetworkString<_16> NetworkedPlayerName { get; set; }
    [Networked] private Color NetworkedPlayerColor { get; set; }
    [Networked] private PlayerState CurrentState { get; set; }
    [Networked] private NetworkBool IsImpostor { get; set; }
    [Networked] private NetworkBool IsGhost { get; set; }
    [Networked] private NetworkBool HasKillPermission { get; set; }
    [Networked] private NetworkBool HasVentPermission { get; set; }
    [Networked] private NetworkId KilledByPlayerId { get; set; }

    // 优化相关
    private PlayerState _lastState;
    private float _lastStateChangeTime;
    private const float STATE_SYNC_INTERVAL = 0.1f; // 状态同步间隔
    
    // 组件引用
    private PlayerMovement playerMovement;
    private PlayerAnimation playerAnimation;
    private PlayerInteraction playerInteraction;
    private PlayerInput playerInput;
    
    // 事件
    public Action<PlayerState> OnPlayerStateChanged;
    public Action<InteractionType, GameObject> OnInteraction;
    public Action OnActionPerformed;
    public Action<NetworkId> OnDeath;
    
    // 属性
    public bool IsDead => CurrentState == PlayerState.Dead || CurrentState == PlayerState.Ghost;
    public bool IsAlive => !IsDead;
    public bool HasKillAbility => HasKillPermission && IsImpostor;
    public bool HasVentAbility => HasVentPermission && IsImpostor;
    public bool IsLocalPlayer => Object.HasInputAuthority;
    public string PlayerName => NetworkedPlayerName.ToString();
    public Color PlayerColor => NetworkedPlayerColor;
    
    public override void Spawned()
    {
        base.Spawned();
        
        // 获取组件引用
        playerMovement = GetComponent<PlayerMovement>();
        playerAnimation = GetComponent<PlayerAnimation>();
        playerInteraction = GetComponent<PlayerInteraction>();
        playerInput = GetComponent<PlayerInput>();

        // 移动平台输入支持
        #if UNITY_ANDROID || UNITY_IOS
        var mobileInput = GetComponent<GooseDuckKill.Player.Mobile.MobilePlayerInput>();
        if (mobileInput != null && Object.HasInputAuthority)
        {
            // 启用移动输入
            mobileInput.enabled = true;
            if (playerInput != null)
            {
                playerInput.enabled = false; // 禁用传统输入
            }
        }
        #endif
        
        // 初始化网络状态
        if (Object.HasStateAuthority)
        {
            NetworkedPlayerName = playerName;
            NetworkedPlayerColor = playerColor;
            CurrentState = PlayerState.Normal;
            KilledByPlayerId = NetworkId.None;
        }
        
        // 更新玩家外观
        UpdatePlayerAppearance();
        
        // 设置名称标签
        if (nameTagText != null)
        {
            nameTagText.text = NetworkedPlayerName.ToString();
            nameTagText.color = NetworkedPlayerColor;
        }
    }
    
    // 处理网络输入
    public override void FixedUpdateNetwork()
    {
        // 获取网络输入
        if (GetInput(out PlayerNetworkInput input))
        {
            // 处理移动
            if (playerMovement != null && CurrentState == PlayerState.Normal)
            {
                playerMovement.ProcessMovement(input.MovementInput);
            }

            // 处理交互按钮
            if (input.InteractPressed)
            {
                HandleInteractInput();
            }

            // 处理动作按钮 (通常是杀人或特殊能力)
            if (input.ActionPressed)
            {
                HandleActionInput();
            }

            // 处理报告按钮
            if (input.ReportPressed)
            {
                HandleReportInput();
            }
        }

        // 优化状态同步 - 只在状态改变或达到同步间隔时同步
        if (Object.HasStateAuthority)
        {
            OptimizedStateSync();
        }

        // 检查杀手ID是否发生变化
        CheckKilledByChange();
    }

    /// <summary>
    /// 优化的状态同步
    /// </summary>
    private void OptimizedStateSync()
    {
        float currentTime = Time.time;

        // 检查状态是否改变
        if (_lastState != CurrentState)
        {
            _lastState = CurrentState;
            _lastStateChangeTime = currentTime;
            // 状态改变时立即同步
            return;
        }

        // 定期同步以确保一致性
        if (currentTime - _lastStateChangeTime > STATE_SYNC_INTERVAL)
        {
            _lastStateChangeTime = currentTime;
            // 这里可以添加额外的同步逻辑
        }
    }
    
    // 检查杀手ID是否发生变化
    private void CheckKilledByChange()
    {
        if (KilledByPlayerId.IsValid)
        {
            // 玩家被杀，可以在这里添加额外的视觉效果或音效
            OnDeath?.Invoke(KilledByPlayerId);
        }
    }
    
    private void Update()
    {
        // 本地玩家特定更新
        if (Object.HasInputAuthority)
        {
            // 可以在这里添加仅对本地玩家的更新
        }
    }
    
    // 处理交互输入
    private void HandleInteractInput()
    {
        // 检查玩家状态
        if (CurrentState != PlayerState.Normal) return;
        
        // 尝试交互
        if (playerInteraction != null)
        {
            InteractionResult result = playerInteraction.TryInteract();
            
            if (result.Success)
            {
                // 通知观察者
                OnInteraction?.Invoke(result.Type, result.Target);
                
                // 处理交互结果
                HandleInteractionResult(result);
            }
        }
    }
    
    // 处理动作输入
    private void HandleActionInput()
    {
        // 检查玩家状态
        if (CurrentState != PlayerState.Normal) return;
        
        bool actionPerformed = false;
        
        // 如果是杀手，尝试杀人
        if (IsImpostor && HasKillPermission)
        {
            if (playerInteraction != null)
            {
                InteractionResult result = playerInteraction.TryKill();
                
                if (result.Success)
                {
                    // 通知观察者
                    OnInteraction?.Invoke(result.Type, result.Target);
                    actionPerformed = true;
                }
            }
        }
        
        // 如果执行了动作，触发事件
        if (actionPerformed)
        {
            OnActionPerformed?.Invoke();
        }
    }
    
    // 处理报告输入
    private void HandleReportInput()
    {
        // 检查玩家状态
        if (CurrentState != PlayerState.Normal) return;
        
        // 这里可以添加特定的报告逻辑
        // 例如，如果玩家附近有尸体，可以报告
    }
    
    // 处理交互结果
    private void HandleInteractionResult(InteractionResult result)
    {
        switch (result.Type)
        {
            case InteractionType.Task:
                // 设置为任务状态
                if (Object.HasStateAuthority)
                {
                    ChangeState(PlayerState.InTask);
                }
                break;
                
            case InteractionType.Report:
                // 开始会议
                if (Object.HasStateAuthority)
                {
                    ChangeState(PlayerState.InMeeting);
                }
                break;
                
            case InteractionType.Vent:
                // 进入通风口
                // 这里需要更多的逻辑来处理通风口进入和退出
                break;
                
            case InteractionType.Meeting:
                // 召集紧急会议
                if (Object.HasStateAuthority)
                {
                    ChangeState(PlayerState.InMeeting);
                }
                break;
        }
    }
    
    // 更改玩家状态
    public void ChangeState(PlayerState newState)
    {
        if (!Object.HasStateAuthority) return;
        
        if (CurrentState != newState)
        {
            CurrentState = newState;
            
            // 处理状态变化
            switch (newState)
            {
                case PlayerState.Dead:
                    // 处理死亡状态
                    if (playerMovement != null)
                    {
                        playerMovement.StopMovement();
                    }
                    break;
                    
                case PlayerState.Ghost:
                    // 处理幽灵状态
                    break;
                    
                case PlayerState.InTask:
                    // 处理任务状态
                    if (playerMovement != null)
                    {
                        playerMovement.StopMovement();
                    }
                    break;
                    
                case PlayerState.InMeeting:
                    // 处理会议状态
                    if (playerMovement != null)
                    {
                        playerMovement.StopMovement();
                    }
                    break;
                    
                case PlayerState.Normal:
                    // 回到正常状态
                    break;
            }
            
            // 通知观察者
            OnPlayerStateChanged?.Invoke(newState);
        }
    }
    
    /// <summary>
    /// 设置玩家为鸭子（杀手）
    /// </summary>
    public void SetAsImpostor(bool isImpostor)
    {
        if (!Object.HasStateAuthority) return;
        
        // 设置玩家为杀手状态
        IsImpostor = isImpostor;
        
        Debug.Log($"玩家 {Object.InputAuthority} 被设置为 {(isImpostor ? "鸭子(杀手)" : "鹅")}");
    }
    
    // 玩家被杀
    public void Die(PlayerRef killerPlayerRef)
    {
        if (!Object.HasStateAuthority) return;
        
        // 设置状态
        ChangeState(PlayerState.Dead);
        
        // 记录凶手
        var killerObject = Runner.GetPlayerObject(killerPlayerRef);
        KilledByPlayerId = killerObject != null ? killerObject.Id : NetworkId.None;
    }
    
    // 成为幽灵
    public void BecomeGhost()
    {
        if (!Object.HasStateAuthority) return;
        
        // 设置状态
        ChangeState(PlayerState.Ghost);
        IsGhost = true;
    }
    
    // 重置任务状态
    public void CompleteTask()
    {
        if (!Object.HasStateAuthority) return;
        
        // 如果在任务状态，回到正常状态
        if (CurrentState == PlayerState.InTask)
        {
            ChangeState(PlayerState.Normal);
        }
    }
    
    // 结束会议
    public void EndMeeting()
    {
        if (!Object.HasStateAuthority) return;
        
        // 如果在会议状态，回到正常状态
        if (CurrentState == PlayerState.InMeeting)
        {
            ChangeState(PlayerState.Normal);
        }
    }
    
    // 更新玩家外观
    private void UpdatePlayerAppearance()
    {
        if (playerSprite != null)
        {
            playerSprite.color = NetworkedPlayerColor;
        }
    }
    
    // 重新设置玩家信息（例如名称和颜色）
    public void SetPlayerInfo(string name, Color color)
    {
        if (!Object.HasStateAuthority) return;
        
        NetworkedPlayerName = name;
        NetworkedPlayerColor = color;
        
        // 更新外观
        UpdatePlayerAppearance();
        
        // 更新名称标签
        if (nameTagText != null)
        {
            nameTagText.text = name;
            nameTagText.color = color;
        }
    }
    
    // 获取玩家的下一个网络输入
    public PlayerNetworkInput GetNextNetworkInput()
    {
        #if UNITY_ANDROID || UNITY_IOS
        // 移动平台输入
        var mobileInput = GetComponent<GooseDuckKill.Player.Mobile.MobilePlayerInput>();
        if (mobileInput != null && mobileInput.enabled)
        {
            return mobileInput.GetNetworkInput();
        }
        #endif

        // 传统输入
        if (playerInput != null && playerInput.enabled)
        {
            return playerInput.GetNetworkInput();
        }

        return new PlayerNetworkInput();
    }
    }
}
