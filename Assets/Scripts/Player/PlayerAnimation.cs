using UnityEngine;
using System.Collections;
using CustomNetworking.Core;
using GooseDuckKill.Player;

/// <summary>
/// 处理玩家动画
/// 使用DOTween实现流畅的动画效果
/// </summary>
public class PlayerAnimation : NetworkBehaviour
{
    [Header("Components")]
    [SerializeField] private Animator animator;
    [SerializeField] private SpriteRenderer spriteRenderer;
    
    [Header("Animation Settings")]
    [SerializeField] private float colorTransitionDuration = 0.3f;
    [SerializeField] private float scaleBounceDuration = 0.5f;
    [SerializeField] private float deathAnimationDuration = 1.0f;
    
    // 动画参数哈希（优化性能）
    private int isWalkingHash;
    private int isDeadHash;
    private int triggerInteractHash;
    private int triggerActionHash;
    private int directionXHash;
    private int directionYHash;
    
    // 组件引用
    private PlayerMovement playerMovement;
    private PlayerController playerController;
    
    // 当前协程
    private Coroutine currentDeathCoroutine;
    private Coroutine currentColorCoroutine;
    private Coroutine currentScaleCoroutine;
    
    protected override void Awake()
    {
        base.Awake();

        // 如果没有指定，则尝试自动查找组件
        if (animator == null) animator = GetComponentInChildren<Animator>();
        if (spriteRenderer == null) spriteRenderer = GetComponentInChildren<SpriteRenderer>();
        
        playerMovement = GetComponent<PlayerMovement>();
        playerController = GetComponent<PlayerController>();
        
        // 缓存动画参数哈希
        isWalkingHash = Animator.StringToHash("IsWalking");
        isDeadHash = Animator.StringToHash("IsDead");
        triggerInteractHash = Animator.StringToHash("TriggerInteract");
        triggerActionHash = Animator.StringToHash("TriggerAction");
        directionXHash = Animator.StringToHash("DirectionX");
        directionYHash = Animator.StringToHash("DirectionY");
    }
    
    public override void Spawned()
    {
        base.Spawned();
        
        // 订阅事件
        if (playerMovement != null)
        {
            playerMovement.OnMovementStateChanged += HandleMovementStateChanged;
            playerMovement.OnDirectionChanged += HandleDirectionChanged;
        }
        
        if (playerController != null)
        {
            playerController.OnPlayerStateChanged += HandlePlayerStateChanged;
            playerController.OnInteraction += HandleInteraction;
            playerController.OnActionPerformed += HandleActionPerformed;
        }
    }
    
    private void OnEnable()
    {
        // 确保动画状态正确
        if (animator != null && playerMovement != null)
        {
            UpdateAnimatorState(playerMovement.CurrentMovementState);
        }
    }
    
    private void OnDestroy()
    {
        // 取消订阅事件
        if (playerMovement != null)
        {
            playerMovement.OnMovementStateChanged -= HandleMovementStateChanged;
            playerMovement.OnDirectionChanged -= HandleDirectionChanged;
        }
        
        if (playerController != null)
        {
            playerController.OnPlayerStateChanged -= HandlePlayerStateChanged;
            playerController.OnInteraction -= HandleInteraction;
            playerController.OnActionPerformed -= HandleActionPerformed;
        }
        
        // 清理所有协程
        if (currentDeathCoroutine != null)
        {
            StopCoroutine(currentDeathCoroutine);
            currentDeathCoroutine = null;
        }

        if (currentColorCoroutine != null)
        {
            StopCoroutine(currentColorCoroutine);
            currentColorCoroutine = null;
        }

        if (currentScaleCoroutine != null)
        {
            StopCoroutine(currentScaleCoroutine);
            currentScaleCoroutine = null;
        }
    }
    
    // 处理移动状态变化
    private void HandleMovementStateChanged(PlayerMovementState newState)
    {
        UpdateAnimatorState(newState);
    }
    
    // 处理方向变化
    private void HandleDirectionChanged(Vector2 newDirection)
    {
        if (animator != null)
        {
            animator.SetFloat(directionXHash, newDirection.x);
            animator.SetFloat(directionYHash, newDirection.y);
        }
    }
    
    // 处理玩家状态变化
    private void HandlePlayerStateChanged(PlayerState newState)
    {
        switch (newState)
        {
            case PlayerState.Dead:
                PlayDeathAnimation();
                break;
            case PlayerState.Ghost:
                SetGhostAppearance();
                break;
            case PlayerState.Normal:
                ResetAppearance();
                break;
        }
        
        // 更新动画器状态
        if (animator != null)
        {
            animator.SetBool(isDeadHash, newState == PlayerState.Dead);
        }
    }
    
    // 处理交互动作
    private void HandleInteraction(InteractionType type, GameObject target)
    {
        if (animator != null)
        {
            animator.SetTrigger(triggerInteractHash);
        }
        
        // 根据交互类型播放特定动画
        switch (type)
        {
            case InteractionType.Task:
                PlayTaskAnimation();
                break;
            case InteractionType.Kill:
                PlayKillAnimation();
                break;
            case InteractionType.Report:
                PlayReportAnimation();
                break;
            case InteractionType.Vent:
                PlayVentAnimation();
                break;
        }
    }
    
    // 处理特殊动作
    private void HandleActionPerformed()
    {
        if (animator != null)
        {
            animator.SetTrigger(triggerActionHash);
        }
    }
    
    // 更新动画状态
    private void UpdateAnimatorState(PlayerMovementState state)
    {
        if (animator != null)
        {
            animator.SetBool(isWalkingHash, state == PlayerMovementState.Walking || state == PlayerMovementState.Running);
        }
    }
    
    #region 动画效果
    
    // 播放死亡动画
    private void PlayDeathAnimation()
    {
        if (spriteRenderer == null) return;

        // 停止之前的动画
        if (currentDeathCoroutine != null)
        {
            StopCoroutine(currentDeathCoroutine);
        }

        // 开始死亡动画协程
        currentDeathCoroutine = StartCoroutine(DeathAnimationCoroutine());
    }

    private IEnumerator DeathAnimationCoroutine()
    {
        float elapsed = 0f;
        Vector3 startRotation = transform.localEulerAngles;
        Vector3 startScale = transform.localScale;
        Color startColor = spriteRenderer.color;

        Vector3 targetRotation = new Vector3(0, 0, 90);
        Vector3 targetScale = Vector3.one * 0.5f;
        Color targetColor = new Color(0.5f, 0.5f, 0.5f, 0.7f);

        while (elapsed < deathAnimationDuration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / deathAnimationDuration;

            transform.localEulerAngles = Vector3.Lerp(startRotation, targetRotation, t);
            transform.localScale = Vector3.Lerp(startScale, targetScale, t);
            spriteRenderer.color = Color.Lerp(startColor, targetColor, t);

            yield return null;
        }

        transform.localEulerAngles = targetRotation;
        transform.localScale = targetScale;
        spriteRenderer.color = targetColor;
    }
    
    // 设置幽灵外观
    private void SetGhostAppearance()
    {
        if (spriteRenderer == null) return;

        // 停止之前的颜色动画
        if (currentColorCoroutine != null)
        {
            StopCoroutine(currentColorCoroutine);
        }

        // 半透明效果
        currentColorCoroutine = StartCoroutine(ColorTransitionCoroutine(new Color(1f, 1f, 1f, 0.5f), colorTransitionDuration));
    }
    
    // 重置外观
    private void ResetAppearance()
    {
        if (spriteRenderer == null) return;

        // 停止之前的动画
        if (currentColorCoroutine != null) StopCoroutine(currentColorCoroutine);
        if (currentScaleCoroutine != null) StopCoroutine(currentScaleCoroutine);
        if (currentDeathCoroutine != null) StopCoroutine(currentDeathCoroutine);

        // 重置旋转、缩放和颜色
        StartCoroutine(RotationTransitionCoroutine(Vector3.zero, 0.2f));
        currentScaleCoroutine = StartCoroutine(ScaleTransitionCoroutine(Vector3.one, 0.2f));
        currentColorCoroutine = StartCoroutine(ColorTransitionCoroutine(Color.white, colorTransitionDuration));
    }
    
    // 播放任务动画
    private void PlayTaskAnimation()
    {
        if (currentScaleCoroutine != null) StopCoroutine(currentScaleCoroutine);

        // 轻微的弹跳效果
        currentScaleCoroutine = StartCoroutine(BounceScaleCoroutine());
    }

    // 播放杀人动画
    private void PlayKillAnimation()
    {
        if (currentColorCoroutine != null) StopCoroutine(currentColorCoroutine);

        // 闪烁红色
        currentColorCoroutine = StartCoroutine(FlashColorCoroutine(Color.red, 0.1f, 2));
    }

    // 播放报告动画
    private void PlayReportAnimation()
    {
        if (currentScaleCoroutine != null) StopCoroutine(currentScaleCoroutine);

        // 惊讶动画
        currentScaleCoroutine = StartCoroutine(SurpriseScaleCoroutine());
    }

    // 播放通风口动画
    private void PlayVentAnimation()
    {
        if (currentScaleCoroutine != null) StopCoroutine(currentScaleCoroutine);

        // 缩小效果
        currentScaleCoroutine = StartCoroutine(VentScaleCoroutine());
    }

    // 协程辅助方法
    private IEnumerator ColorTransitionCoroutine(Color targetColor, float duration)
    {
        Color startColor = spriteRenderer.color;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            spriteRenderer.color = Color.Lerp(startColor, targetColor, t);
            yield return null;
        }

        spriteRenderer.color = targetColor;
    }

    private IEnumerator ScaleTransitionCoroutine(Vector3 targetScale, float duration)
    {
        Vector3 startScale = transform.localScale;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            transform.localScale = Vector3.Lerp(startScale, targetScale, t);
            yield return null;
        }

        transform.localScale = targetScale;
    }

    private IEnumerator RotationTransitionCoroutine(Vector3 targetRotation, float duration)
    {
        Vector3 startRotation = transform.localEulerAngles;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            transform.localEulerAngles = Vector3.Lerp(startRotation, targetRotation, t);
            yield return null;
        }

        transform.localEulerAngles = targetRotation;
    }

    private IEnumerator BounceScaleCoroutine()
    {
        Vector3 originalScale = transform.localScale;
        Vector3 bounceScale = originalScale * 1.1f;

        // 放大
        yield return StartCoroutine(ScaleTransitionCoroutine(bounceScale, scaleBounceDuration / 2));
        // 缩小
        yield return StartCoroutine(ScaleTransitionCoroutine(originalScale, scaleBounceDuration / 2));
    }

    private IEnumerator FlashColorCoroutine(Color flashColor, float flashDuration, int flashCount)
    {
        Color originalColor = spriteRenderer.color;

        for (int i = 0; i < flashCount; i++)
        {
            yield return StartCoroutine(ColorTransitionCoroutine(flashColor, flashDuration));
            yield return StartCoroutine(ColorTransitionCoroutine(originalColor, flashDuration));
        }
    }

    private IEnumerator SurpriseScaleCoroutine()
    {
        Vector3 originalScale = transform.localScale;

        // 惊讶动画序列
        yield return StartCoroutine(ScaleTransitionCoroutine(new Vector3(1.2f, 0.8f, 1f), 0.2f));
        yield return StartCoroutine(ScaleTransitionCoroutine(new Vector3(0.8f, 1.2f, 1f), 0.2f));
        yield return StartCoroutine(ScaleTransitionCoroutine(originalScale, 0.2f));
    }

    private IEnumerator VentScaleCoroutine()
    {
        Vector3 originalScale = transform.localScale;

        // 缩小
        yield return StartCoroutine(ScaleTransitionCoroutine(Vector3.one * 0.1f, 0.3f));

        // 隐藏
        if (spriteRenderer != null) spriteRenderer.enabled = false;
        yield return new WaitForSeconds(0.2f);

        // 显示
        if (spriteRenderer != null) spriteRenderer.enabled = true;

        // 恢复
        yield return StartCoroutine(ScaleTransitionCoroutine(originalScale, 0.3f));
    }
    
    #endregion
} 