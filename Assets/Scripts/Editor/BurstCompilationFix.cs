#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.IO;

namespace GooseDuckKill.Editor
{
    /// <summary>
    /// Burst编译修复工具 - 解决UI程序集的Burst编译问题
    /// </summary>
    public static class BurstCompilationFix
    {
        /// <summary>
        /// 修复Burst编译问题
        /// </summary>
        [MenuItem("Tools/Fix Burst Compilation Issues")]
        public static void FixBurstCompilationIssues()
        {
            Debug.Log("[BurstFix] 开始修复Burst编译问题...");
            
            try
            {
                // 清理编译缓存
                ClearCompilationCache();
                
                // 刷新资源数据库
                AssetDatabase.Refresh();
                
                // 强制重新编译
                AssetDatabase.ImportAsset("Assets", ImportAssetOptions.ImportRecursive);
                
                Debug.Log("[BurstFix] Burst编译问题修复完成！");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[BurstFix] 修复过程中出现错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 清理编译缓存
        /// </summary>
        private static void ClearCompilationCache()
        {
            // 清理Library/ScriptAssemblies
            string scriptAssembliesPath = Path.Combine(Application.dataPath, "../Library/ScriptAssemblies");
            if (Directory.Exists(scriptAssembliesPath))
            {
                Directory.Delete(scriptAssembliesPath, true);
                Debug.Log("[BurstFix] 已清理ScriptAssemblies缓存");
            }
            
            // 清理Library/Bee
            string beePath = Path.Combine(Application.dataPath, "../Library/Bee");
            if (Directory.Exists(beePath))
            {
                Directory.Delete(beePath, true);
                Debug.Log("[BurstFix] 已清理Bee缓存");
            }
            
            // 清理Temp目录
            string tempPath = Path.Combine(Application.dataPath, "../Temp");
            if (Directory.Exists(tempPath))
            {
                Directory.Delete(tempPath, true);
                Debug.Log("[BurstFix] 已清理Temp缓存");
            }
        }
        
        /// <summary>
        /// 自动在项目加载时运行修复
        /// </summary>
        [InitializeOnLoadMethod]
        private static void AutoFixOnLoad()
        {
            // 检查是否需要自动修复
            if (EditorPrefs.GetBool("BurstFix_AutoRun", false))
            {
                EditorPrefs.SetBool("BurstFix_AutoRun", false);
                EditorApplication.delayCall += () =>
                {
                    Debug.Log("[BurstFix] 自动运行Burst编译修复...");
                    FixBurstCompilationIssues();
                };
            }
        }
    }
}
#endif
