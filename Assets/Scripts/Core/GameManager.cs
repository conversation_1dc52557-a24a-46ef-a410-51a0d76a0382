using System;
using UnityEngine;
using CustomNetworking.Core;

namespace GooseDuckKill.Core
{
    /// <summary>
    /// 游戏管理器 - 管理游戏生命周期、状态和全局事件
    /// 使用单例模式便于全局访问
    /// </summary>
    public class GameManager : NetworkBehaviour
    {
        [Header("组件引用")]
        [SerializeField] private GameStateManager stateManager;
        [SerializeField] private RoleManager roleManager;
        [SerializeField] private ScoreManager scoreManager;
        
        // 网络同步属性
        [Networked] private NetworkBool IsGameActive { get; set; }
        [Networked] private int CurrentRound { get; set; }
        [Networked] private TickTimer GameTimer { get; set; }
        [Networked] private NetworkBool IsInMeeting { get; set; }
        [Networked] private PlayerRef MeetingCaller { get; set; }
        [Networked] private NetworkBool IsBodyReport { get; set; }
        
        // 单例实例
        private static GameManager _instance;
        
        // 事件
        public event Action OnGameStarted;
        public event Action OnGameEnded;
        public event Action<int> OnRoundChanged;
        public event Action<PlayerRef, bool> OnMeetingCalled;
        
        // 变更检测器
        private ChangeDetector _meetingChangeDetector;
        
        // 单例访问器
        public static GameManager Instance => _instance;
        
        // 属性
        public bool IsGameRunning => IsGameActive;
        public int Round => CurrentRound;
        public float RemainingTime => GameTimer.IsRunning ? GameTimer.RemainingTime(Runner) ?? 0f : 0f;
        public bool IsMeeting => IsInMeeting;
        
        // 组件访问器
        public GameStateManager State => stateManager;
        public RoleManager Roles => roleManager;
        public ScoreManager Score => scoreManager;
        
        // 配置属性
        [Header("游戏配置")]
        [SerializeField] private float gameDuration = 600f; // 10分钟
        [SerializeField] private float meetingDuration = 120f; // 2分钟
        [SerializeField] private float startingDuration = 10f; // 10秒
        [SerializeField] private float meetingCooldown = 60f; // 会议冷却时间
        
        // 内部状态
        private TickTimer _meetingCooldownTimer;
        
        #region Unity生命周期
        
        public override void Spawned()
        {
            base.Spawned();
            
            // 初始化变更检测器
            _meetingChangeDetector = new ChangeDetector();
            
            // 设置单例实例
            if (_instance == null)
            {
                _instance = this;
            }
            else if (_instance != this)
            {
                // 已经存在实例，销毁此对象
                Runner.Despawn(Object);
                return;
            }
            
            // 初始化组件
            InitializeComponents();
            
            // 设置初始状态
            if (Runner.IsServer)
            {
                IsGameActive = false;
                CurrentRound = 0;
                
                // 切换到大厅状态
                stateManager.ChangeState(GameStateManager.GameState.Lobby);
            }
            
            Debug.Log("GameManager 初始化完成");
        }
        
        /// <summary>
        /// 渲染更新 - 检测会议状态变化
        /// </summary>
        public override void Render()
        {
            base.Render();

            // 简化的变化检测 - 直接检测IsInMeeting属性变化
            if (_meetingChangeDetector.DetectChange(nameof(IsInMeeting), IsInMeeting))
            {
                // 会议状态发生变化
                OnMeetingCalled?.Invoke(MeetingCaller, IsBodyReport);
            }
        }

        
        public override void FixedUpdateNetwork()
        {
            if (!Runner.IsServer) return;
            
            // 检查当前状态
            switch (stateManager.CurrentState)
            {
                case GameStateManager.GameState.Starting:
                    // 检查开始倒计时
                    if (GameTimer.Expired(Runner))
                    {
                        // 开始倒计时结束，切换到游戏状态
                        stateManager.ChangeState(GameStateManager.GameState.Playing);
                        
                        // 重新设置游戏计时器为游戏时长
                        GameTimer = TickTimer.CreateFromSeconds(Runner, gameDuration);
                    }
                    break;
                    
                case GameStateManager.GameState.Playing:
                    // 检查游戏计时器
                    if (GameTimer.Expired(Runner))
                    {
                        // 游戏时间结束，检查胜利条件
                        scoreManager.DeclareVictory(ScoreManager.Team.Goose, ScoreManager.VictoryCondition.TimerExpired);
                    }
                    else
                    {
                        // 定期检查胜利条件
                        scoreManager.CheckVictoryConditions();
                    }
                    break;
                    
                case GameStateManager.GameState.Meeting:
                    // 检查会议计时器
                    if (GameTimer.Expired(Runner))
                    {
                        // 会议时间结束，返回游戏状态
                        EndMeeting();
                    }
                    break;
            }
        }
        
        #endregion
        
        #region 初始化方法
        
        private void InitializeComponents()
        {
            // 获取组件引用（如果未设置）
            if (stateManager == null) 
                stateManager = GetComponentInChildren<GameStateManager>();
            
            if (roleManager == null) 
                roleManager = GetComponentInChildren<RoleManager>();
            
            if (scoreManager == null) 
                scoreManager = GetComponentInChildren<ScoreManager>();
            
            // 如果仍然为空，则创建新组件
            if (stateManager == null)
            {
                GameObject stateObj = new GameObject("StateManager");
                stateObj.transform.SetParent(transform);
                stateManager = stateObj.AddComponent<GameStateManager>();
            }
            
            if (roleManager == null)
            {
                GameObject roleObj = new GameObject("RoleManager");
                roleObj.transform.SetParent(transform);
                roleManager = roleObj.AddComponent<RoleManager>();
            }
            
            if (scoreManager == null)
            {
                GameObject scoreObj = new GameObject("ScoreManager");
                scoreObj.transform.SetParent(transform);
                scoreManager = scoreObj.AddComponent<ScoreManager>();
            }
        }
        
        #endregion
        
        #region 游戏控制方法
        
        /// <summary>
        /// 开始游戏
        /// </summary>
        public void StartGame()
        {
            if (!Runner.IsServer) return;
            
            // 设置游戏状态
            IsGameActive = true;
            CurrentRound = 1;
            
            // 设置开始倒计时
            GameTimer = TickTimer.CreateFromSeconds(Runner, startingDuration);
            
            // 切换到开始状态
            stateManager.ChangeState(GameStateManager.GameState.Starting);
            
            // 分配角色
            roleManager.AssignRoles();
            
            // 触发游戏开始事件
            OnGameStarted?.Invoke();
            OnRoundChanged?.Invoke(CurrentRound);
            
            Debug.Log("游戏开始！");
        }
        
        /// <summary>
        /// 结束游戏
        /// </summary>
        public void EndGame()
        {
            if (!Runner.IsServer) return;
            
            // 更新状态
            IsGameActive = false;
            
            // 切换到结束状态
            stateManager.ChangeState(GameStateManager.GameState.Ending);
            
            // 触发游戏结束事件
            OnGameEnded?.Invoke();
            
            Debug.Log("游戏结束！");
        }
        
        /// <summary>
        /// 进入下一轮
        /// </summary>
        public void NextRound()
        {
            if (!Runner.IsServer) return;
            
            // 增加轮数
            CurrentRound++;
            
            // 重置游戏状态
            stateManager.ChangeState(GameStateManager.GameState.Starting);
            
            // 设置开始倒计时
            GameTimer = TickTimer.CreateFromSeconds(Runner, startingDuration);
            
            // 重新分配角色
            roleManager.AssignRoles();
            
            // 触发轮次变更事件
            OnRoundChanged?.Invoke(CurrentRound);
            
            Debug.Log($"进入第 {CurrentRound} 轮！");
        }
        
        /// <summary>
        /// 召开会议
        /// </summary>
        public void CallMeeting(PlayerRef caller, bool isBodyReport = false)
        {
            if (!Runner.IsServer) return;
            
            // 检查当前状态
            if (stateManager.CurrentState != GameStateManager.GameState.Playing)
                return;
            
            // 检查会议冷却 - 使用IsRunning和Expired代替null检查
            if (_meetingCooldownTimer.IsRunning && !_meetingCooldownTimer.Expired(Runner))
                return;
            
            // 设置会议数据
            IsInMeeting = true;
            MeetingCaller = caller;
            IsBodyReport = isBodyReport;
            
            // 设置会议计时器
            GameTimer = TickTimer.CreateFromSeconds(Runner, meetingDuration);
            
            // 切换到会议状态
            stateManager.ChangeState(GameStateManager.GameState.Meeting);
            
            Debug.Log($"会议开始！由 {caller} 召集，{(isBodyReport ? "发现尸体" : "紧急会议")}");
        }
        
        /// <summary>
        /// 结束会议
        /// </summary>
        public void EndMeeting()
        {
            if (!Runner.IsServer) return;
            
            // 重置会议数据
            IsInMeeting = false;
            
            // 设置会议冷却
            _meetingCooldownTimer = TickTimer.CreateFromSeconds(Runner, meetingCooldown);
            
            // 切换回游戏状态
            stateManager.ChangeState(GameStateManager.GameState.Playing);
            
            Debug.Log("会议结束！");
        }
        
        /// <summary>
        /// 重置游戏
        /// </summary>
        public void RestartGame()
        {
            if (!Runner.IsServer) return;
            
            // 重置所有状态
            IsGameActive = false;
            CurrentRound = 0;
            IsInMeeting = false;
            
            // 返回大厅状态
            stateManager.ChangeState(GameStateManager.GameState.Lobby);
            
            Debug.Log("游戏重置，返回大厅！");
        }
        
        #endregion
        
        #region 调试方法
        
        /// <summary>
        /// 调试用：强制切换状态（仅开发时使用）
        /// </summary>
        public void DebugForceState(GameStateManager.GameState state)
        {
            #if UNITY_EDITOR
            if (Runner.IsServer)
            {
                stateManager.ChangeState(state);
                Debug.Log($"[调试] 强制切换到状态: {state}");
            }
            #endif
        }
        
        #endregion
    }
} 