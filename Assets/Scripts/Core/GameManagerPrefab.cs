using UnityEngine;
using CustomNetworking.Core;
using GooseDuckKill.Network;

namespace GooseDuckKill.Core
{
    /// <summary>
    /// 游戏管理器预制体 - 用于在编辑器中配置GameManager
    /// </summary>
    public class GameManagerPrefab : MonoBehaviour
    {
        [Header("预制体引用")]
        [SerializeField] private GameObject gameManagerPrefab;
        
        [Header("网络引用")]
        [SerializeField] private NetworkManager networkManager;
        
        private void Start()
        {
            // 检查是否已经存在GameManager实例
            if (GameManager.Instance != null)
            {
                Debug.Log("GameManager实例已存在，无需创建新实例");
                return;
            }
            
            // 监听NetworkRunner创建事件
            if (networkManager != null)
            {
                networkManager.OnRunnerCreated += OnNetworkRunnerCreated;
                Debug.Log("注册NetworkRunner创建事件");
            }
            else
            {
                Debug.LogWarning("未找到NetworkManager引用，请手动设置");
            }
        }
        
        private void OnDestroy()
        {
            // 取消监听
            if (networkManager != null)
            {
                networkManager.OnRunnerCreated -= OnNetworkRunnerCreated;
            }
        }
        
        /// <summary>
        /// 当NetworkRunner创建时，生成GameManager
        /// </summary>
        private void OnNetworkRunnerCreated(NetworkRunner runner)
        {
            if (runner.IsServer && gameManagerPrefab != null)
            {
                // 创建GameManager实例
                runner.Spawn(gameManagerPrefab, Vector3.zero, Quaternion.identity);
                Debug.Log("GameManager实例已创建");
            }
        }
        
        /// <summary>
        /// 获取游戏管理器实例
        /// </summary>
        public GameManager GetGameManagerInstance()
        {
            return GameManager.Instance;
        }
    }
} 