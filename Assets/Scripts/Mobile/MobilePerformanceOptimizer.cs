using UnityEngine;
using UnityEngine.Rendering;
using CustomNetworking.Optimization;

namespace GooseDuckKill.Mobile
{
    /// <summary>
    /// 移动平台性能优化器
    /// 根据设备性能自动调整游戏设置
    /// </summary>
    public class MobilePerformanceOptimizer : MonoBehaviour
    {
        [Header("性能设置")]
        [SerializeField] private int targetFrameRate = 60;
        [SerializeField] private bool enableAutoOptimization = true;
        [SerializeField] private bool enableBatteryOptimization = true;
        [SerializeField] private bool enableThermalOptimization = true;
        
        [Header("质量等级")]
        [SerializeField] private QualityProfile lowEndProfile;
        [SerializeField] private QualityProfile midEndProfile;
        [SerializeField] private QualityProfile highEndProfile;
        
        [Header("网络优化")]
        [SerializeField] private float mobileMaxBandwidth = 50f; // KB/s
        [SerializeField] private int mobileSyncRate = 15; // Hz
        [SerializeField] private bool adaptToConnectionType = true;
        
        [Header("监控设置")]
        [SerializeField] private float performanceCheckInterval = 5f;
        [SerializeField] private int frameRateHistorySize = 60;
        [SerializeField] private float lowFrameRateThreshold = 25f;
        
        // 设备信息
        private DevicePerformanceLevel _deviceLevel;
        private bool _isLowPowerMode;
        private float _currentFrameRate;
        private float[] _frameRateHistory;
        private int _frameRateIndex;
        
        // 性能监控
        private float _lastPerformanceCheck;
        private int _consecutiveLowFrames;
        private const int LOW_FRAME_THRESHOLD = 30;
        
        public enum DevicePerformanceLevel
        {
            Low,
            Medium,
            High
        }
        
        [System.Serializable]
        public class QualityProfile
        {
            public string profileName;
            public int qualityLevel;
            public int targetFrameRate;
            public bool enableVSync;
            public int textureQuality;
            public int shadowQuality;
            public bool enablePostProcessing;
            public float renderScale;
            public int maxParticles;
            public float maxBandwidth;
            public int networkSyncRate;
        }
        
        private void Start()
        {
            InitializeOptimizer();
        }
        
        private void Update()
        {
            MonitorPerformance();
            
            if (enableAutoOptimization)
            {
                CheckAndOptimize();
            }
        }
        
        /// <summary>
        /// 初始化优化器
        /// </summary>
        private void InitializeOptimizer()
        {
            // 检测设备性能等级
            _deviceLevel = DetectDevicePerformanceLevel();
            
            // 初始化帧率历史
            _frameRateHistory = new float[frameRateHistorySize];
            
            // 应用初始优化设置
            ApplyOptimizationProfile(_deviceLevel);
            
            // 设置目标帧率
            Application.targetFrameRate = targetFrameRate;
            
            // 适配网络连接类型
            if (adaptToConnectionType)
            {
                AdaptToNetworkType();
            }
            
            Debug.Log($"[MobileOptimizer] 设备性能等级: {_deviceLevel}");
        }
        
        /// <summary>
        /// 检测设备性能等级
        /// </summary>
        private DevicePerformanceLevel DetectDevicePerformanceLevel()
        {
            // 获取设备信息
            int memorySize = SystemInfo.systemMemorySize;
            int processorCount = SystemInfo.processorCount;
            string deviceModel = SystemInfo.deviceModel;
            
            // 基于内存大小的基础判断
            DevicePerformanceLevel level;
            
            if (memorySize < 3000) // 3GB以下
            {
                level = DevicePerformanceLevel.Low;
            }
            else if (memorySize < 6000) // 6GB以下
            {
                level = DevicePerformanceLevel.Medium;
            }
            else
            {
                level = DevicePerformanceLevel.High;
            }
            
            // 基于处理器核心数调整
            if (processorCount < 4)
            {
                level = DevicePerformanceLevel.Low;
            }
            else if (processorCount >= 8 && level == DevicePerformanceLevel.Medium)
            {
                level = DevicePerformanceLevel.High;
            }
            
            // iOS设备特殊处理
            #if UNITY_IOS
            if (deviceModel.Contains("iPhone"))
            {
                // iPhone 6s及以下为低端
                if (deviceModel.Contains("iPhone6") || deviceModel.Contains("iPhone5"))
                {
                    level = DevicePerformanceLevel.Low;
                }
                // iPhone X及以上为高端
                else if (deviceModel.Contains("iPhoneX") || deviceModel.Contains("iPhone11") || 
                         deviceModel.Contains("iPhone12") || deviceModel.Contains("iPhone13"))
                {
                    level = DevicePerformanceLevel.High;
                }
            }
            #endif
            
            return level;
        }
        
        /// <summary>
        /// 应用优化配置
        /// </summary>
        private void ApplyOptimizationProfile(DevicePerformanceLevel level)
        {
            QualityProfile profile = GetQualityProfile(level);
            
            if (profile == null)
            {
                Debug.LogWarning("[MobileOptimizer] 未找到对应的质量配置");
                return;
            }
            
            // 应用Unity质量设置
            QualitySettings.SetQualityLevel(profile.qualityLevel);
            
            // 设置帧率
            Application.targetFrameRate = profile.targetFrameRate;
            targetFrameRate = profile.targetFrameRate;
            
            // 设置VSync
            QualitySettings.vSyncCount = profile.enableVSync ? 1 : 0;
            
            // 设置纹理质量
            QualitySettings.globalTextureMipmapLimit = profile.textureQuality;
            
            // 设置阴影质量
            QualitySettings.shadows = (ShadowQuality)profile.shadowQuality;
            
            // 设置渲染缩放
            if (profile.renderScale < 1f)
            {
                // 降低渲染分辨率
                Screen.SetResolution(
                    Mathf.RoundToInt(Screen.width * profile.renderScale),
                    Mathf.RoundToInt(Screen.height * profile.renderScale),
                    true
                );
            }
            
            // 网络优化设置
            ApplyNetworkOptimization(profile);
            
            Debug.Log($"[MobileOptimizer] 应用了 {profile.profileName} 配置");
        }
        
        /// <summary>
        /// 获取质量配置
        /// </summary>
        private QualityProfile GetQualityProfile(DevicePerformanceLevel level)
        {
            switch (level)
            {
                case DevicePerformanceLevel.Low:
                    return lowEndProfile;
                case DevicePerformanceLevel.Medium:
                    return midEndProfile;
                case DevicePerformanceLevel.High:
                    return highEndProfile;
                default:
                    return midEndProfile;
            }
        }
        
        /// <summary>
        /// 应用网络优化
        /// </summary>
        private void ApplyNetworkOptimization(QualityProfile profile)
        {
            var optimizer = NetworkBandwidthOptimizer.Instance;
            if (optimizer != null)
            {
                // 设置带宽限制
                optimizer.SetMaxBandwidth(profile.maxBandwidth);
            }
            
            // 设置网络同步频率
            var networkTransforms = FindObjectsOfType<CustomNetworking.Components.NetworkTransform>();
            foreach (var nt in networkTransforms)
            {
                nt.SetSyncRate(profile.networkSyncRate);
            }
        }
        
        /// <summary>
        /// 适配网络连接类型
        /// </summary>
        private void AdaptToNetworkType()
        {
            NetworkReachability reachability = Application.internetReachability;
            
            switch (reachability)
            {
                case NetworkReachability.ReachableViaCarrierDataNetwork:
                    // 移动数据：降低带宽使用
                    ApplyMobileDataOptimization();
                    break;
                    
                case NetworkReachability.ReachableViaLocalAreaNetwork:
                    // WiFi：正常带宽
                    ApplyWiFiOptimization();
                    break;
                    
                case NetworkReachability.NotReachable:
                    // 无网络连接
                    Debug.LogWarning("[MobileOptimizer] 无网络连接");
                    break;
            }
        }
        
        /// <summary>
        /// 移动数据优化
        /// </summary>
        private void ApplyMobileDataOptimization()
        {
            var optimizer = NetworkBandwidthOptimizer.Instance;
            if (optimizer != null)
            {
                optimizer.SetMaxBandwidth(mobileMaxBandwidth);
            }
            
            // 降低同步频率
            var networkTransforms = FindObjectsOfType<CustomNetworking.Components.NetworkTransform>();
            foreach (var nt in networkTransforms)
            {
                nt.SetSyncRate(mobileSyncRate);
            }
            
            Debug.Log("[MobileOptimizer] 应用移动数据优化");
        }
        
        /// <summary>
        /// WiFi优化
        /// </summary>
        private void ApplyWiFiOptimization()
        {
            var optimizer = NetworkBandwidthOptimizer.Instance;
            if (optimizer != null)
            {
                optimizer.SetMaxBandwidth(100f); // 正常带宽
            }
            
            // 正常同步频率
            var networkTransforms = FindObjectsOfType<CustomNetworking.Components.NetworkTransform>();
            foreach (var nt in networkTransforms)
            {
                nt.SetSyncRate(20);
            }
            
            Debug.Log("[MobileOptimizer] 应用WiFi优化");
        }
        
        /// <summary>
        /// 监控性能
        /// </summary>
        private void MonitorPerformance()
        {
            // 更新帧率历史
            _currentFrameRate = 1f / Time.deltaTime;
            _frameRateHistory[_frameRateIndex] = _currentFrameRate;
            _frameRateIndex = (_frameRateIndex + 1) % frameRateHistorySize;
            
            // 检查低帧率
            if (_currentFrameRate < lowFrameRateThreshold)
            {
                _consecutiveLowFrames++;
            }
            else
            {
                _consecutiveLowFrames = 0;
            }
        }
        
        /// <summary>
        /// 检查并优化
        /// </summary>
        private void CheckAndOptimize()
        {
            float currentTime = Time.time;
            
            if (currentTime - _lastPerformanceCheck < performanceCheckInterval)
                return;
                
            _lastPerformanceCheck = currentTime;
            
            // 检查是否需要降级
            if (_consecutiveLowFrames > LOW_FRAME_THRESHOLD)
            {
                OptimizeForLowPerformance();
                _consecutiveLowFrames = 0;
            }
            
            // 检查电池状态
            if (enableBatteryOptimization)
            {
                CheckBatteryStatus();
            }
            
            // 检查热量状态
            if (enableThermalOptimization)
            {
                CheckThermalStatus();
            }
        }
        
        /// <summary>
        /// 低性能优化
        /// </summary>
        private void OptimizeForLowPerformance()
        {
            // 降低质量等级
            if (_deviceLevel > DevicePerformanceLevel.Low)
            {
                _deviceLevel = (DevicePerformanceLevel)((int)_deviceLevel - 1);
                ApplyOptimizationProfile(_deviceLevel);
                
                Debug.Log($"[MobileOptimizer] 由于性能问题，降级到 {_deviceLevel}");
            }
        }
        
        /// <summary>
        /// 检查电池状态
        /// </summary>
        private void CheckBatteryStatus()
        {
            float batteryLevel = SystemInfo.batteryLevel;
            BatteryStatus batteryStatus = SystemInfo.batteryStatus;
            
            // 低电量模式
            if (batteryLevel < 0.2f && batteryStatus != BatteryStatus.Charging)
            {
                if (!_isLowPowerMode)
                {
                    EnableLowPowerMode();
                }
            }
            else if (_isLowPowerMode && (batteryLevel > 0.3f || batteryStatus == BatteryStatus.Charging))
            {
                DisableLowPowerMode();
            }
        }
        
        /// <summary>
        /// 检查热量状态
        /// </summary>
        private void CheckThermalStatus()
        {
            // iOS热量状态检查
            #if UNITY_IOS
            // 这里可以添加iOS特定的热量检测
            #endif
            
            // Android热量状态检查
            #if UNITY_ANDROID
            // 这里可以添加Android特定的热量检测
            #endif
        }
        
        /// <summary>
        /// 启用低功耗模式
        /// </summary>
        private void EnableLowPowerMode()
        {
            _isLowPowerMode = true;
            
            // 降低帧率
            Application.targetFrameRate = 30;
            
            // 降低质量
            QualitySettings.SetQualityLevel(0);
            
            // 减少网络同步
            var optimizer = NetworkBandwidthOptimizer.Instance;
            if (optimizer != null)
            {
                optimizer.SetMaxBandwidth(25f);
            }
            
            Debug.Log("[MobileOptimizer] 启用低功耗模式");
        }
        
        /// <summary>
        /// 禁用低功耗模式
        /// </summary>
        private void DisableLowPowerMode()
        {
            _isLowPowerMode = false;
            
            // 恢复正常设置
            ApplyOptimizationProfile(_deviceLevel);
            
            Debug.Log("[MobileOptimizer] 禁用低功耗模式");
        }
        
        /// <summary>
        /// 获取平均帧率
        /// </summary>
        public float GetAverageFrameRate()
        {
            float sum = 0f;
            for (int i = 0; i < frameRateHistorySize; i++)
            {
                sum += _frameRateHistory[i];
            }
            return sum / frameRateHistorySize;
        }
        
        /// <summary>
        /// 获取当前设备性能等级
        /// </summary>
        public DevicePerformanceLevel GetDevicePerformanceLevel()
        {
            return _deviceLevel;
        }
        
        /// <summary>
        /// 手动设置性能等级
        /// </summary>
        public void SetPerformanceLevel(DevicePerformanceLevel level)
        {
            _deviceLevel = level;
            ApplyOptimizationProfile(level);
        }
    }
}
