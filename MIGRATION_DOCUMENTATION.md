# GooseDuckKill 网络框架迁移文档

## 概述

本文档记录了 GooseDuckKill 项目从 Photon Fusion 2.0.6 迁移到自定义网络框架的完整过程。

## 迁移目标

1. **完全移除 Photon Fusion 依赖** - 不再依赖付费的 Photon Fusion 解决方案
2. **实现自定义网络框架** - 创建可扩展、可维护的自定义网络系统
3. **保持代码兼容性** - 最小化现有代码的修改
4. **提供完整功能** - 确保所有网络功能正常工作

## 自定义网络框架架构

### 核心组件

#### 1. NetworkTypes.cs
- **PlayerRef** - 玩家引用类型
- **NetworkBool** - 网络布尔值
- **NetworkString<T>** - 网络字符串
- **NetworkId** - 网络对象ID
- **TickTimer** - 网络计时器
- **SessionInfo** - 会话信息
- **NetworkedAttribute** - 网络属性标记

#### 2. NetworkRunner.cs
- **核心网络运行器** - 管理网络生命周期
- **支持的游戏模式**：
  - Server - 专用服务器
  - Host - 主机模式
  - Client - 客户端
  - AutoHostOrClient - 自动选择
  - Shared - 共享模式
  - Single - 单机模式

#### 3. NetworkBehaviour.cs
- **网络行为基类** - 所有网络组件的基类
- **生命周期方法**：
  - `Spawned()` - 对象生成时调用
  - `Despawned()` - 对象销毁时调用
  - `FixedUpdateNetwork()` - 固定网络更新
  - `Render()` - 渲染更新

#### 4. NetworkObject.cs
- **网络对象管理** - 管理网络对象的生命周期
- **权限系统** - 输入权限和状态权限管理
- **网络行为注册** - 管理附加的网络行为组件

#### 5. NetworkCallbacks.cs
- **INetworkRunnerCallbacks** - 网络回调接口
- **事件处理** - 玩家加入/离开、连接/断开等事件

### 适配器系统

#### FusionAdapter.cs
- **类型映射** - 将 Fusion 类型映射到自定义类型
- **全局别名** - 提供 `using` 别名以保持代码兼容性
- **NetworkTransform** - 自定义网络变换组件

## 迁移的文件列表

### Core 模块
1. **GameManager.cs** ✅
   - 更新 using 语句：`using CustomNetworking.Core;`
   - 保持所有网络属性和方法不变

2. **GameStateManager.cs** ✅
   - 更新 using 语句
   - 移除 RPC 调用，改用事件系统

3. **ScoreManager.cs** ✅
   - 更新 using 语句
   - 移除 RPC 调用，改用事件系统

4. **RoleManager.cs** ✅
   - 更新 using 语句
   - 修复 ActivePlayers 访问
   - 移除 RPC 调用

5. **PlayerController.cs** ✅
   - 更新 using 语句
   - 修复 NetworkId 相关代码

### Network 模块
1. **NetworkManager.cs** ✅
   - 完全重写以使用自定义网络框架
   - 简化连接逻辑
   - 移除 Fusion 特定的 API 调用

### Player 模块
1. **PlayerNetwork.cs** ✅
   - 更新 using 语句
   - 使用自定义 NetworkTransform
   - 更新回调方法签名

### UI 模块
1. **ConnectionUI.cs** ✅
   - 更新 using 语句
   - 修复事件参数类型

## 主要变更

### 1. 命名空间更改
```csharp
// 之前
using Fusion;

// 之后
using CustomNetworking.Core;
```

### 2. 类型映射
```csharp
// Fusion 类型 -> 自定义类型
NetworkBehaviour -> CustomNetworking.Core.NetworkBehaviour
NetworkObject -> CustomNetworking.Core.NetworkObject
NetworkRunner -> CustomNetworking.Core.NetworkRunner
PlayerRef -> CustomNetworking.Core.PlayerRef
TickTimer -> CustomNetworking.Core.TickTimer
```

### 3. RPC 系统移除
- 所有 `[Rpc]` 标记的方法已移除
- 改用事件系统进行客户端通信
- 添加 TODO 注释以便将来实现自定义 RPC

### 4. 启动参数简化
```csharp
// 之前 (Fusion)
var startGameArgs = new StartGameArgs() {
    GameMode = gameMode,
    Scene = sceneInfo,
    SceneManager = sceneObjectProvider,
    // ... 复杂的场景管理
};

// 之后 (自定义)
var startGameArgs = new StartGameArgs {
    GameMode = NetworkRunner.GameMode.Host,
    SessionName = roomName,
    Address = NetAddress.Any(),
    MaxPlayers = maxPlayers
};
```

## 功能状态

### ✅ 已实现
- 基础网络类型系统
- 网络对象生命周期管理
- 玩家连接/断开处理
- 基础的网络同步
- 事件系统
- UI 集成

### 🚧 部分实现
- 网络变换同步（基础版本）
- 输入系统（框架已就绪）
- 变化检测（简化版本）

### ❌ 待实现
- RPC 系统
- 可靠数据传输
- 主机迁移
- 高级网络优化
- 反作弊系统

## 性能考虑

1. **内存管理** - 使用对象池减少 GC 压力
2. **网络带宽** - 实现增量同步
3. **CPU 优化** - 避免不必要的网络更新
4. **延迟补偿** - 实现客户端预测

## 测试建议

1. **单机测试** - 验证基础功能
2. **本地网络测试** - 测试多客户端连接
3. **压力测试** - 测试最大玩家数量
4. **延迟测试** - 模拟网络延迟环境

## 后续开发计划

### 短期目标 (1-2 周)
1. 实现基础 RPC 系统
2. 完善网络变换同步
3. 添加网络调试工具

### 中期目标 (1-2 月)
1. 实现可靠数据传输
2. 添加主机迁移支持
3. 性能优化和压力测试

### 长期目标 (3-6 月)
1. 实现高级网络功能
2. 添加反作弊系统
3. 跨平台兼容性测试

## 故障排除

### 常见问题

1. **编译错误** - 检查 using 语句是否正确更新
2. **网络连接失败** - 验证 NetworkRunner 初始化
3. **同步问题** - 检查网络权限设置
4. **性能问题** - 使用网络分析器检查流量

### 调试技巧

1. 启用详细日志记录
2. 使用 Unity Profiler 监控性能
3. 检查网络对象的权限状态
4. 验证事件订阅/取消订阅

## 结论

通过实现自定义网络框架，GooseDuckKill 项目成功摆脱了对 Photon Fusion 的依赖，同时保持了代码的兼容性和可维护性。新的网络框架提供了更大的灵活性和控制权，为未来的功能扩展奠定了坚实的基础。
