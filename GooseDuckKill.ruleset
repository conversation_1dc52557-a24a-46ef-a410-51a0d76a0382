<?xml version="1.0" encoding="utf-8"?>
<RuleSet Name="GooseDuckKill Custom Rules" Description="Custom rules for GooseDuckKill project" ToolsVersion="16.0">
  <Localization ResourceAssembly="Microsoft.VisualStudio.CodeAnalysis.RuleSets.Strings.dll" ResourceBaseName="Microsoft.VisualStudio.CodeAnalysis.RuleSets.Strings.Localized">
    <Name Resource="GooseDuckKill_Title" />
    <Description Resource="GooseDuckKill_Description" />
  </Localization>
  
  <!-- Disable namespace folder matching rule -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="IDE0130" Action="None" />
  </Rules>
  
  <!-- Style rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp.Features" RuleNamespace="Microsoft.CodeAnalysis.CSharp.Features">
    <Rule Id="IDE0130" Action="None" />
  </Rules>
  
  <!-- Allow custom namespace patterns -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.NetAnalyzers" RuleNamespace="Microsoft.CodeAnalysis.NetAnalyzers">
    <Rule Id="CA1050" Action="None" />
  </Rules>
</RuleSet>
