# 🦆 Goose Duck Kill 3

一个基于 Unity 和自定义网络框架的多人社交推理游戏。

[![Unity Version](https://img.shields.io/badge/Unity-2023.3+-blue.svg)](https://unity3d.com/get-unity/download)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()

**语言 / Language**: [中文](README.md) | [English](README_EN.md)

> **重要说明**: 本项目已完全迁移至自定义网络框架，摆脱了对 Photon Fusion 的依赖，实现了零成本的网络解决方案。

## 📖 中文说明

### 项目背景
Goose Duck Kill 3 是一款深受 Among Us 启发的社交推理游戏，采用 Unity 引擎开发。项目最初使用 Photon Fusion 作为网络解决方案，但考虑到成本和自主性，我们开发了完全自主的网络框架。

### 技术特色
- **🔧 自主研发**: 完全自主的网络框架，无第三方依赖
- **💰 零成本**: 摆脱 Photon Fusion 的付费限制
- **🚀 高性能**: 基于 Tick 的确定性网络同步
- **🛡️ 安全可靠**: 服务器权威架构，有效防止作弊
- **📈 可扩展**: 模块化设计，易于功能扩展和维护

### 游戏玩法
这是一款 4-10 人的多人在线社交推理游戏：
- **鹅阵营（好人）**: 需要完成地图上的各种任务，同时识别并投票淘汰鸭子
- **鸭子阵营（内鬼）**: 需要暗中消灭鹅，破坏任务，并在会议中误导其他玩家
- **中立角色**: 拥有特殊能力和独特胜利条件的角色

### 开发理念
1. **开源精神**: 提供完整的源代码和详细文档
2. **技术创新**: 自主研发网络框架，探索最佳实践
3. **社区驱动**: 欢迎社区贡献和反馈
4. **持续改进**: 不断优化性能和用户体验

## 🎮 游戏概述

Goose Duck Kill 3 是一款社交推理游戏，玩家被分为不同的阵营：
- **🦆 鹅（好人）**: 完成任务并找出鸭子
- **🦆 鸭子（内鬼）**: 消灭鹅并破坏他们的努力
- **⚖️ 中立角色**: 拥有独特胜利条件的特殊角色

## ✨ 核心特性

### 🎯 游戏系统
- **动态角色分配**: 基于玩家数量的可配置角色比例
- **状态管理**: 大厅 → 开始 → 游戏 → 会议 → 结束的完整流程
- **计分系统**: 胜利条件追踪和团队计分
- **实时多人**: 使用自定义网络框架的实时同步

### 🎮 玩家机制
- **移动系统**: 2D 角色移动与网络同步
- **交互系统**: 任务完成、杀人、报告和通风口使用
- **角色能力**: 基于分配角色的不同能力
- **状态管理**: 正常、死亡、幽灵、任务中、会议中等状态

### 🌐 自定义网络框架
- **完全自主**: 摆脱 Photon Fusion 依赖，降低成本
- **高性能**: 基于 Tick 的确定性游戏模拟
- **服务器权威**: 防作弊的服务器验证逻辑
- **灵活扩展**: 模块化设计，易于功能扩展

#### 网络框架详细说明
我们的自定义网络框架包含以下核心组件：

**NetworkRunner（网络运行器）**
- 支持多种游戏模式：服务器、主机、客户端、自动选择等
- 管理网络生命周期和连接状态
- 提供 Tick 基础的确定性模拟

**NetworkBehaviour（网络行为）**
- 替代 Photon Fusion 的 NetworkBehaviour
- 提供网络生命周期方法：Spawned、Despawned、FixedUpdateNetwork
- 支持网络属性自动同步

**NetworkObject（网络对象）**
- 管理网络对象的生命周期
- 处理输入权限和状态权限
- 支持网络行为组件注册

**兼容性适配器**
- 提供 Fusion 兼容层，最小化代码修改
- 支持渐进式迁移
- 保持原有 API 接口不变

## 🚀 快速开始

### 📋 系统要求
- Unity 2023.3 或更高版本
- .NET Framework 4.8+
- Windows/macOS/Linux 支持

### 🔧 详细安装步骤

#### 1. 环境准备
确保您的开发环境满足以下要求：
- Unity 2023.3 LTS 或更高版本
- Visual Studio 2022 或 JetBrains Rider
- Git 版本控制工具

#### 2. 获取源代码
```bash
# 克隆项目仓库
git clone https://github.com/silverflute/goose-duck-kill3.git

# 进入项目目录
cd goose-duck-kill3
```

#### 3. Unity 项目设置
1. 启动 Unity Hub
2. 点击"添加"按钮，选择项目文件夹
3. 确认 Unity 版本为 2023.3 或更高
4. 打开项目（首次打开可能需要较长时间）

#### 4. 项目配置
1. 打开 `Window > Package Manager`
2. 确认所有必需的包已安装
3. 检查 `Project Settings > Player` 中的配置
4. 验证 `Project Settings > XR Plug-in Management` 设置

#### 5. 构建和运行
```bash
# 开发模式运行（在 Unity 编辑器中）
# 1. 打开 Assets/Scenes/MainScene
# 2. 点击 Play 按钮

# 构建发布版本
# 1. File > Build Settings
# 2. 选择目标平台
# 3. 点击 Build
```

### 🎮 详细游戏说明

#### 基本控制
- **WASD/方向键**: 角色移动
- **E/空格键**: 与环境交互（完成任务、使用设备）
- **Q/鼠标左键**: 执行角色特殊行动（杀人、使用能力）
- **R键**: 报告尸体或紧急情况
- **Tab键**: 查看任务列表和地图
- **ESC键**: 打开游戏菜单

#### 游戏机制详解

**任务系统**
- 鹅阵营需要完成各种任务来获得胜利
- 任务类型包括：修理设备、清理垃圾、连接电路等
- 任务进度对所有玩家可见
- 完成所有任务即可获得胜利

**会议系统**
- 发现尸体或按下紧急按钮可召开会议
- 会议期间所有玩家进入讨论阶段
- 讨论时间结束后进入投票阶段
- 得票最多的玩家将被淘汰

**角色能力**
- **鹅**: 完成任务、参与投票、报告尸体
- **鸭子**: 杀害其他玩家、破坏设备、使用通风口
- **中立角色**: 根据具体角色拥有不同的特殊能力

#### 胜利条件
- **鹅阵营胜利**: 完成所有任务 或 淘汰所有鸭子
- **鸭子阵营胜利**: 鸭子数量等于或超过鹅的数量
- **中立角色胜利**: 根据具体角色的特殊胜利条件

## 📁 项目结构

```
Assets/Scripts/
├── Core/                    # 核心游戏系统
│   ├── GameManager.cs       # 游戏生命周期管理
│   ├── GameStateManager.cs  # 状态机管理
│   ├── RoleManager.cs       # 角色分配与管理
│   └── ScoreManager.cs      # 计分与胜利条件
├── CustomNetworking/        # 自定义网络框架
│   ├── Core/               # 核心网络组件
│   ├── Adapters/           # Fusion 兼容适配器
│   └── Components/         # 网络组件
├── Network/                # 网络管理
│   └── NetworkManager.cs   # 网络连接管理
├── Player/                 # 玩家相关
│   ├── PlayerController.cs # 玩家控制器
│   ├── PlayerMovement.cs   # 移动系统
│   └── PlayerInteraction.cs # 交互系统
└── UI/                     # 用户界面
    └── ConnectionUI.cs     # 连接界面
```

## 🏗️ 架构设计

### 核心组件
- **GameManager**: 中央游戏协调器和状态管理
- **NetworkManager**: 处理所有网络操作
- **RoleManager**: 管理玩家角色和能力
- **ScoreManager**: 追踪分数和胜利条件
- **PlayerController**: 个人玩家行为和输入处理

### 自定义网络框架
- **NetworkRunner**: 网络运行器，支持多种游戏模式
- **NetworkBehaviour**: 网络行为基类
- **NetworkObject**: 网络对象管理
- **NetworkTypes**: 网络数据类型（PlayerRef, NetworkBool 等）

## 🎯 游戏流程

1. **大厅**: 玩家加入并等待游戏开始
2. **开始**: 分配角色，倒计时开始
3. **游戏**: 主要游戏循环
4. **会议**: 讨论和投票阶段
5. **结束**: 胜利画面和结果

## 🔧 配置选项

### 游戏设置
```csharp
// GameManager 配置
[Header("游戏设置")]
public float startingDuration = 10f;    // 开始倒计时
public float gameDuration = 300f;       // 游戏时长
public float meetingDuration = 120f;    // 会议时长

// RoleManager 配置
[Header("角色配置")]
public float duckRatio = 0.25f;         // 鸭子比例
public float neutralRatio = 0.1f;       // 中立角色比例
public int minDucks = 1;                // 最少鸭子数
public int maxDucks = 3;                // 最多鸭子数
```

### 网络设置
```csharp
// NetworkManager 配置
[Header("网络设置")]
public int maxPlayers = 10;             // 最大玩家数
public float tickRate = 60f;            // 网络更新频率
public bool enableLagCompensation = true; // 延迟补偿
```

## 🚧 开发状态

### ✅ 已实现
- 基础网络类型系统
- 网络对象生命周期管理
- 玩家连接/断开处理
- 基础的网络同步
- 事件系统
- UI 集成

### 🔄 进行中
- 网络变换同步（基础版本）
- 输入系统（框架已就绪）
- 变化检测（简化版本）

### 📋 待实现
- RPC 系统
- 可靠数据传输
- 主机迁移
- 高级网络优化
- 反作弊系统

## 📚 文档

- [文档中心](Docs/README.md) - 完整文档索引
- [迁移文档](MIGRATION_DOCUMENTATION.md) - Photon Fusion 迁移详情
- [架构指南](Docs/ARCHITECTURE.md) - 详细系统设计
- [API 参考](Docs/API_REFERENCE.md) - 代码文档
- [自定义网络后端](Docs/CUSTOM_NETWORK_BACKEND.md) - 网络框架详情
- [实现总结](Docs/IMPLEMENTATION_SUMMARY.md) - 实现概述
- [技术规划](Docs/GooseDuckKill_TechnicalPlan.md) - 项目技术规划

## 🛠️ 开发技术

- **Unity 2023.3+**: 游戏引擎
- **C# .NET**: 编程语言
- **自定义网络框架**: 替代 Photon Fusion
- **Universal Render Pipeline**: 图形管线

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](CONTRIBUTING.md) 了解详情。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🎯 性能优化

### 网络优化
- **对象池**: 减少 GC 压力
- **增量同步**: 只同步变化的数据
- **优先级系统**: 重要数据优先传输
- **压缩算法**: 减少带宽使用

### 渲染优化
- **LOD 系统**: 距离相关的细节层次
- **遮挡剔除**: 隐藏不可见对象
- **批处理**: 减少绘制调用
- **纹理压缩**: 优化内存使用

## 🔒 安全特性

### 反作弊系统
- **服务器验证**: 所有关键操作在服务器验证
- **输入清理**: 防止恶意输入
- **状态检查**: 检测不一致状态
- **速率限制**: 防止垃圾信息和洪水攻击

### 数据保护
- **加密通信**: 安全的数据传输
- **会话管理**: 安全的身份验证
- **隐私控制**: 用户数据保护

## 🧪 测试

### 单元测试
```bash
# 运行所有测试
Unity -batchmode -runTests -testPlatform EditMode

# 运行特定测试
Unity -batchmode -runTests -testFilter "NetworkingTests"
```

### 集成测试
- **本地网络测试**: 多客户端连接测试
- **压力测试**: 最大玩家数量测试
- **延迟测试**: 模拟网络延迟环境

## 📈 路线图

### 短期目标 (1-2 周)
- [ ] 实现基础 RPC 系统
- [ ] 完善网络变换同步
- [ ] 添加网络调试工具

### 中期目标 (1-2 月)
- [ ] 实现可靠数据传输
- [ ] 添加主机迁移支持
- [ ] 性能优化和压力测试

### 长期目标 (3-6 月)
- [ ] 实现高级网络功能
- [ ] 添加反作弊系统
- [ ] 跨平台兼容性测试

## 🐛 故障排除

### 常见问题

**Q: 编译错误 "找不到 Fusion 命名空间"**
A: 确保已更新 using 语句为 `using CustomNetworking.Core;`

**Q: 网络连接失败**
A: 检查 NetworkRunner 是否正确初始化，确认防火墙设置

**Q: 玩家同步问题**
A: 验证网络对象的权限设置，检查 HasStateAuthority 和 HasInputAuthority

**Q: 性能问题**
A: 使用 Unity Profiler 监控性能，检查网络流量

### 调试技巧
1. 启用详细日志记录
2. 使用 Unity Profiler 监控性能
3. 检查网络对象的权限状态
4. 验证事件订阅/取消订阅

## 📞 支持

如有问题或建议，请：
- 提交 [Issue](https://github.com/silverflute/goose-duck-kill3/issues)
- 查看 [Wiki](https://github.com/silverflute/goose-duck-kill3/wiki)
- 加入 [Discord 社区](https://discord.gg/your-server)

## 🙏 致谢

感谢所有为项目做出贡献的开发者和测试者。

---

**注意**: 本项目已从 Photon Fusion 迁移到自定义网络框架，实现了完全自主的网络解决方案，大幅降低了运营成本。
