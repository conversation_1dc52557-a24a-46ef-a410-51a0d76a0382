<Project>
  <PropertyGroup>
    <!-- 禁用命名空间与文件夹结构匹配的规则 -->
    <NoWarn>$(NoWarn);IDE0130</NoWarn>
    
    <!-- 启用EditorConfig支持 -->
    <EnableEditorConfigSupport>true</EnableEditorConfigSupport>
    
    <!-- 代码分析设置 -->
    <AnalysisLevel>latest</AnalysisLevel>
    <AnalysisMode>AllEnabledByDefault</AnalysisMode>
    
    <!-- 禁用特定的代码分析规则 -->
    <WarningsNotAsErrors>$(WarningsNotAsErrors);IDE0130</WarningsNotAsErrors>
  </PropertyGroup>
  
  <!-- 代码分析规则配置 -->
  <ItemGroup>
    <GlobalAnalyzerConfigFiles Include="$(MSBuildThisFileDirectory).editorconfig" />
  </ItemGroup>
  
  <!-- 自定义分析器规则 -->
  <PropertyGroup>
    <!-- 命名空间规则 -->
    <dotnet_style_namespace_match_folder>false</dotnet_style_namespace_match_folder>
    <dotnet_diagnostic_IDE0130_severity>none</dotnet_diagnostic_IDE0130_severity>
  </PropertyGroup>
</Project>
