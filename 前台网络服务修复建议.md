# Unity前台网络服务修复建议

## 📋 概述

基于前后台网络服务一致性分析，本文档提供具体的修复方案和可直接使用的代码，以确保前台Unity项目与后台Go服务的完全兼容。

## 🔧 修复方案1：网络配置统一

### 创建网络配置类

```csharp
// Assets/Scripts/Network/NetworkConfig.cs
using UnityEngine;

namespace GooseDuckKill.Network
{
    public static class NetworkConfig
    {
        // 开发环境配置
        public const string DEV_AUTH_SERVICE = "http://localhost:8000";
        public const string DEV_GAME_SERVICE = "http://localhost:8001";
        public const string DEV_ROOM_SERVICE = "http://localhost:8002";
        public const string DEV_ROOM_WEBSOCKET = "ws://localhost:8002/api/v1/rooms/ws";
        
        // 生产环境配置
        public const string PROD_AUTH_SERVICE = "https://auth.gooseduck.game";
        public const string PROD_GAME_SERVICE = "https://game.gooseduck.game";
        public const string PROD_ROOM_SERVICE = "https://room.gooseduck.game";
        public const string PROD_ROOM_WEBSOCKET = "wss://room.gooseduck.game/api/v1/rooms/ws";
        
        // 当前环境配置
        public static bool IsProduction => !Debug.isDebugBuild;
        
        public static string AuthServiceUrl => IsProduction ? PROD_AUTH_SERVICE : DEV_AUTH_SERVICE;
        public static string GameServiceUrl => IsProduction ? PROD_GAME_SERVICE : DEV_GAME_SERVICE;
        public static string RoomServiceUrl => IsProduction ? PROD_ROOM_SERVICE : DEV_ROOM_SERVICE;
        public static string RoomWebSocketUrl => IsProduction ? PROD_ROOM_WEBSOCKET : DEV_ROOM_WEBSOCKET;
        
        // API版本
        public const string API_VERSION = "v1";
        
        // 超时配置
        public const int REQUEST_TIMEOUT = 30;
        public const int WEBSOCKET_TIMEOUT = 10;
    }
}
```

## 🔧 修复方案2：统一数据模型

### API响应格式

```csharp
// Assets/Scripts/Network/Models/APIResponse.cs
using System;
using Newtonsoft.Json;

namespace GooseDuckKill.Network.Models
{
    [Serializable]
    public class APIResponse<T>
    {
        [JsonProperty("code")]
        public int Code { get; set; }
        
        [JsonProperty("message")]
        public string Message { get; set; }
        
        [JsonProperty("data")]
        public T Data { get; set; }
        
        [JsonProperty("timestamp")]
        public long Timestamp { get; set; }
        
        public bool IsSuccess => Code == 200;
    }
    
    [Serializable]
    public class APIErrorResponse
    {
        [JsonProperty("code")]
        public int Code { get; set; }
        
        [JsonProperty("message")]
        public string Message { get; set; }
        
        [JsonProperty("timestamp")]
        public long Timestamp { get; set; }
    }
}
```

### 房间相关数据模型

```csharp
// Assets/Scripts/Network/Models/Room.cs
using System;
using Newtonsoft.Json;

namespace GooseDuckKill.Network.Models
{
    [Serializable]
    public class Room
    {
        [JsonProperty("id")]
        public string Id { get; set; }
        
        [JsonProperty("name")]
        public string Name { get; set; }
        
        [JsonProperty("host_id")]
        public string HostId { get; set; }
        
        [JsonProperty("max_players")]
        public int MaxPlayers { get; set; }
        
        [JsonProperty("current_size")]
        public int CurrentSize { get; set; }
        
        [JsonProperty("status")]
        public string Status { get; set; }
        
        [JsonProperty("is_private")]
        public bool IsPrivate { get; set; }
        
        [JsonProperty("password")]
        public string Password { get; set; }
        
        [JsonProperty("settings")]
        public RoomSettings Settings { get; set; }
        
        [JsonProperty("created_at")]
        public string CreatedAt { get; set; }
        
        [JsonProperty("updated_at")]
        public string UpdatedAt { get; set; }
    }
    
    [Serializable]
    public class RoomSettings
    {
        [JsonProperty("game_mode")]
        public string GameMode { get; set; } = "classic";
        
        [JsonProperty("map_name")]
        public string MapName { get; set; } = "skeld";
        
        [JsonProperty("task_count")]
        public int TaskCount { get; set; } = 10;
        
        [JsonProperty("duck_ratio")]
        public float DuckRatio { get; set; } = 0.25f;
        
        [JsonProperty("meeting_time")]
        public int MeetingTime { get; set; } = 120;
        
        [JsonProperty("voting_time")]
        public int VotingTime { get; set; } = 30;
        
        [JsonProperty("emergency_meetings")]
        public int EmergencyMeetings { get; set; } = 1;
        
        [JsonProperty("kill_cooldown")]
        public int KillCooldown { get; set; } = 45;
        
        [JsonProperty("player_speed")]
        public float PlayerSpeed { get; set; } = 1.0f;
        
        [JsonProperty("crew_vision")]
        public float CrewVision { get; set; } = 1.0f;
        
        [JsonProperty("impostor_vision")]
        public float ImpostorVision { get; set; } = 1.5f;
        
        [JsonProperty("confirm_ejects")]
        public bool ConfirmEjects { get; set; } = true;
        
        [JsonProperty("visual_tasks")]
        public bool VisualTasks { get; set; } = true;
        
        [JsonProperty("anonymous_votes")]
        public bool AnonymousVotes { get; set; } = false;
    }
    
    [Serializable]
    public class CreateRoomRequest
    {
        [JsonProperty("name")]
        public string Name { get; set; }
        
        [JsonProperty("max_players")]
        public int MaxPlayers { get; set; } = 10;
        
        [JsonProperty("is_private")]
        public bool IsPrivate { get; set; } = false;
        
        [JsonProperty("password")]
        public string Password { get; set; } = "";
        
        [JsonProperty("settings")]
        public RoomSettings Settings { get; set; } = new RoomSettings();
    }
    
    [Serializable]
    public class JoinRoomRequest
    {
        [JsonProperty("password")]
        public string Password { get; set; } = "";
    }
}
```

### 玩家数据模型

```csharp
// Assets/Scripts/Network/Models/Player.cs
using System;
using Newtonsoft.Json;

namespace GooseDuckKill.Network.Models
{
    [Serializable]
    public class Player
    {
        [JsonProperty("id")]
        public string Id { get; set; }
        
        [JsonProperty("username")]
        public string Username { get; set; }
        
        [JsonProperty("role")]
        public string Role { get; set; }
        
        [JsonProperty("is_alive")]
        public bool IsAlive { get; set; } = true;
        
        [JsonProperty("position")]
        public Vector3Data Position { get; set; } = new Vector3Data();
        
        [JsonProperty("is_ready")]
        public bool IsReady { get; set; } = false;
        
        [JsonProperty("color")]
        public string Color { get; set; }
        
        [JsonProperty("hat")]
        public string Hat { get; set; }
        
        [JsonProperty("pet")]
        public string Pet { get; set; }
    }
    
    [Serializable]
    public class Vector3Data
    {
        [JsonProperty("x")]
        public float X { get; set; }
        
        [JsonProperty("y")]
        public float Y { get; set; }
        
        [JsonProperty("z")]
        public float Z { get; set; }
        
        public Vector3Data() { }
        
        public Vector3Data(float x, float y, float z)
        {
            X = x;
            Y = y;
            Z = z;
        }
        
        public UnityEngine.Vector3 ToVector3()
        {
            return new UnityEngine.Vector3(X, Y, Z);
        }
        
        public static Vector3Data FromVector3(UnityEngine.Vector3 vector)
        {
            return new Vector3Data(vector.x, vector.y, vector.z);
        }
    }
}
```

## 🔧 修复方案3：认证管理器

```csharp
// Assets/Scripts/Network/AuthManager.cs
using System;
using System.Collections;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;
using Newtonsoft.Json;
using GooseDuckKill.Network.Models;

namespace GooseDuckKill.Network
{
    public class AuthManager : MonoBehaviour
    {
        private static AuthManager _instance;
        public static AuthManager Instance => _instance;
        
        [Header("认证配置")]
        [SerializeField] private bool autoRefreshToken = true;
        [SerializeField] private float refreshThreshold = 300f; // 5分钟前刷新
        
        // Token信息
        private string _accessToken;
        private string _refreshToken;
        private DateTime _tokenExpireTime;
        
        // 事件
        public event Action<bool> OnAuthStateChanged;
        public event Action<string> OnAuthError;
        
        // 属性
        public bool IsAuthenticated => !string.IsNullOrEmpty(_accessToken) && DateTime.Now < _tokenExpireTime;
        public string AccessToken => _accessToken;
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                LoadTokenFromPlayerPrefs();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            if (autoRefreshToken)
            {
                StartCoroutine(AutoRefreshTokenCoroutine());
            }
        }
        
        /// <summary>
        /// 用户登录
        /// </summary>
        public async Task<bool> LoginAsync(string username, string password)
        {
            var loginRequest = new LoginRequest
            {
                Username = username,
                Password = password
            };
            
            try
            {
                var response = await SendAuthRequestAsync<LoginResponse>("/api/v1/auth/login", loginRequest);
                if (response.IsSuccess)
                {
                    SetTokens(response.Data.AccessToken, response.Data.RefreshToken, response.Data.ExpiresIn);
                    OnAuthStateChanged?.Invoke(true);
                    return true;
                }
                else
                {
                    OnAuthError?.Invoke(response.Message);
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnAuthError?.Invoke($"登录失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 用户注册
        /// </summary>
        public async Task<bool> RegisterAsync(string username, string email, string password)
        {
            var registerRequest = new RegisterRequest
            {
                Username = username,
                Email = email,
                Password = password
            };
            
            try
            {
                var response = await SendAuthRequestAsync<RegisterResponse>("/api/v1/auth/register", registerRequest);
                if (response.IsSuccess)
                {
                    // 注册成功后自动登录
                    return await LoginAsync(username, password);
                }
                else
                {
                    OnAuthError?.Invoke(response.Message);
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnAuthError?.Invoke($"注册失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 刷新Token
        /// </summary>
        public async Task<bool> RefreshTokenAsync()
        {
            if (string.IsNullOrEmpty(_refreshToken))
            {
                return false;
            }
            
            var refreshRequest = new RefreshTokenRequest
            {
                RefreshToken = _refreshToken
            };
            
            try
            {
                var response = await SendAuthRequestAsync<LoginResponse>("/api/v1/auth/refresh", refreshRequest);
                if (response.IsSuccess)
                {
                    SetTokens(response.Data.AccessToken, response.Data.RefreshToken, response.Data.ExpiresIn);
                    return true;
                }
                else
                {
                    // 刷新失败，清除Token
                    ClearTokens();
                    OnAuthStateChanged?.Invoke(false);
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"刷新Token失败: {ex.Message}");
                ClearTokens();
                OnAuthStateChanged?.Invoke(false);
                return false;
            }
        }
        
        /// <summary>
        /// 登出
        /// </summary>
        public async Task LogoutAsync()
        {
            try
            {
                if (!string.IsNullOrEmpty(_accessToken))
                {
                    await SendAuthRequestAsync<object>("/api/v1/auth/logout", null, true);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"登出请求失败: {ex.Message}");
            }
            finally
            {
                ClearTokens();
                OnAuthStateChanged?.Invoke(false);
            }
        }
        
        /// <summary>
        /// 获取认证头
        /// </summary>
        public string GetAuthHeader()
        {
            return string.IsNullOrEmpty(_accessToken) ? "" : $"Bearer {_accessToken}";
        }
        
        /// <summary>
        /// 发送认证相关请求
        /// </summary>
        private async Task<APIResponse<T>> SendAuthRequestAsync<T>(string endpoint, object requestData, bool requireAuth = false)
        {
            var url = NetworkConfig.AuthServiceUrl + endpoint;
            
            using (var request = new UnityWebRequest(url, "POST"))
            {
                // 设置请求体
                if (requestData != null)
                {
                    var jsonData = JsonConvert.SerializeObject(requestData);
                    var bodyRaw = System.Text.Encoding.UTF8.GetBytes(jsonData);
                    request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                }
                
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/json");
                
                // 添加认证头
                if (requireAuth && !string.IsNullOrEmpty(_accessToken))
                {
                    request.SetRequestHeader("Authorization", GetAuthHeader());
                }
                
                request.timeout = NetworkConfig.REQUEST_TIMEOUT;
                
                await request.SendWebRequest();
                
                if (request.result == UnityWebRequest.Result.Success)
                {
                    var responseText = request.downloadHandler.text;
                    return JsonConvert.DeserializeObject<APIResponse<T>>(responseText);
                }
                else
                {
                    throw new Exception($"HTTP Error: {request.responseCode} - {request.error}");
                }
            }
        }
        
        /// <summary>
        /// 设置Token
        /// </summary>
        private void SetTokens(string accessToken, string refreshToken, int expiresIn)
        {
            _accessToken = accessToken;
            _refreshToken = refreshToken;
            _tokenExpireTime = DateTime.Now.AddSeconds(expiresIn);
            
            SaveTokenToPlayerPrefs();
        }
        
        /// <summary>
        /// 清除Token
        /// </summary>
        private void ClearTokens()
        {
            _accessToken = "";
            _refreshToken = "";
            _tokenExpireTime = DateTime.MinValue;
            
            PlayerPrefs.DeleteKey("AccessToken");
            PlayerPrefs.DeleteKey("RefreshToken");
            PlayerPrefs.DeleteKey("TokenExpireTime");
        }
        
        /// <summary>
        /// 保存Token到PlayerPrefs
        /// </summary>
        private void SaveTokenToPlayerPrefs()
        {
            PlayerPrefs.SetString("AccessToken", _accessToken);
            PlayerPrefs.SetString("RefreshToken", _refreshToken);
            PlayerPrefs.SetString("TokenExpireTime", _tokenExpireTime.ToBinary().ToString());
        }
        
        /// <summary>
        /// 从PlayerPrefs加载Token
        /// </summary>
        private void LoadTokenFromPlayerPrefs()
        {
            _accessToken = PlayerPrefs.GetString("AccessToken", "");
            _refreshToken = PlayerPrefs.GetString("RefreshToken", "");
            
            var expireTimeString = PlayerPrefs.GetString("TokenExpireTime", "");
            if (!string.IsNullOrEmpty(expireTimeString) && long.TryParse(expireTimeString, out var expireTimeBinary))
            {
                _tokenExpireTime = DateTime.FromBinary(expireTimeBinary);
            }
        }
        
        /// <summary>
        /// 自动刷新Token协程
        /// </summary>
        private IEnumerator AutoRefreshTokenCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(60f); // 每分钟检查一次
                
                if (IsAuthenticated && !string.IsNullOrEmpty(_refreshToken))
                {
                    var timeToExpire = (_tokenExpireTime - DateTime.Now).TotalSeconds;
                    if (timeToExpire <= refreshThreshold)
                    {
                        await RefreshTokenAsync();
                    }
                }
            }
        }
    }
    
    // 认证相关数据模型
    [Serializable]
    public class LoginRequest
    {
        [JsonProperty("username")]
        public string Username { get; set; }
        
        [JsonProperty("password")]
        public string Password { get; set; }
    }
    
    [Serializable]
    public class RegisterRequest
    {
        [JsonProperty("username")]
        public string Username { get; set; }
        
        [JsonProperty("email")]
        public string Email { get; set; }
        
        [JsonProperty("password")]
        public string Password { get; set; }
    }
    
    [Serializable]
    public class RefreshTokenRequest
    {
        [JsonProperty("refresh_token")]
        public string RefreshToken { get; set; }
    }
    
    [Serializable]
    public class LoginResponse
    {
        [JsonProperty("access_token")]
        public string AccessToken { get; set; }
        
        [JsonProperty("refresh_token")]
        public string RefreshToken { get; set; }
        
        [JsonProperty("expires_in")]
        public int ExpiresIn { get; set; }
        
        [JsonProperty("user")]
        public UserInfo User { get; set; }
    }
    
    [Serializable]
    public class RegisterResponse
    {
        [JsonProperty("user_id")]
        public string UserId { get; set; }
        
        [JsonProperty("message")]
        public string Message { get; set; }
    }
    
    [Serializable]
    public class UserInfo
    {
        [JsonProperty("id")]
        public string Id { get; set; }
        
        [JsonProperty("username")]
        public string Username { get; set; }
        
        [JsonProperty("email")]
        public string Email { get; set; }
    }
}
```

## 📝 使用说明

1. **将上述代码文件添加到Unity项目中**
2. **更新现有的NetworkManager以使用新的配置和认证**
3. **确保安装了Newtonsoft.Json包**
4. **根据实际需求调整配置参数**

## 🔧 修复方案4：WebSocket消息统一

### WebSocket消息类型定义

```csharp
// Assets/Scripts/Network/WebSocketMessageTypes.cs
namespace GooseDuckKill.Network
{
    public static class WebSocketMessageTypes
    {
        // 房间相关消息 - 与后台保持一致
        public const string JOIN_ROOM = "join_room";
        public const string LEAVE_ROOM = "leave_room";
        public const string PLAYER_READY = "player_ready";
        public const string START_GAME = "start_game";

        // 游戏相关消息
        public const string PLAYER_MOVE = "player_move";
        public const string PLAYER_ACTION = "player_action";
        public const string PLAYER_INPUT = "player_input";

        // 通信相关消息
        public const string CHAT_MESSAGE = "chat_message";
        public const string HEARTBEAT = "heartbeat";

        // 服务器响应消息
        public const string ROOM_UPDATED = "room_updated";
        public const string PLAYER_JOINED = "player_joined";
        public const string PLAYER_LEFT = "player_left";
        public const string GAME_STARTED = "game_started";
        public const string GAME_STATE_UPDATE = "game_state_update";
        public const string ERROR = "error";

        // Tick同步消息
        public const string TICK_SYNC = "tick_sync";
        public const string TICK_INPUT = "tick_input";
        public const string TICK_STATE = "tick_state";
    }
}
```

### 统一的WebSocket消息结构

```csharp
// Assets/Scripts/Network/Models/WebSocketMessage.cs
using System;
using Newtonsoft.Json;

namespace GooseDuckKill.Network.Models
{
    [Serializable]
    public class WebSocketMessage
    {
        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("data")]
        public object Data { get; set; }

        [JsonProperty("timestamp")]
        public long Timestamp { get; set; }

        [JsonProperty("sender_id")]
        public string SenderId { get; set; }

        [JsonProperty("room_id")]
        public string RoomId { get; set; }

        public WebSocketMessage() { }

        public WebSocketMessage(string type, object data, string roomId = null)
        {
            Type = type;
            Data = data;
            RoomId = roomId;
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        }

        public T GetData<T>()
        {
            if (Data == null) return default(T);

            if (Data is T directCast)
                return directCast;

            try
            {
                var json = JsonConvert.SerializeObject(Data);
                return JsonConvert.DeserializeObject<T>(json);
            }
            catch
            {
                return default(T);
            }
        }
    }

    // 具体消息数据结构
    [Serializable]
    public class JoinRoomData
    {
        [JsonProperty("room_id")]
        public string RoomId { get; set; }

        [JsonProperty("password")]
        public string Password { get; set; }
    }

    [Serializable]
    public class PlayerMoveData
    {
        [JsonProperty("position")]
        public Vector3Data Position { get; set; }

        [JsonProperty("velocity")]
        public Vector3Data Velocity { get; set; }

        [JsonProperty("rotation")]
        public float Rotation { get; set; }
    }

    [Serializable]
    public class PlayerActionData
    {
        [JsonProperty("action")]
        public string Action { get; set; }

        [JsonProperty("target_id")]
        public string TargetId { get; set; }

        [JsonProperty("parameters")]
        public object Parameters { get; set; }
    }

    [Serializable]
    public class ChatMessageData
    {
        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("channel")]
        public string Channel { get; set; } = "all";
    }

    [Serializable]
    public class ErrorMessageData
    {
        [JsonProperty("code")]
        public int Code { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }
    }
}
```

### 改进的WebSocket管理器

```csharp
// Assets/Scripts/Network/ImprovedWebSocketManager.cs
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Newtonsoft.Json;
using GooseDuckKill.Network.Models;

namespace GooseDuckKill.Network
{
    public class ImprovedWebSocketManager : MonoBehaviour
    {
        [Header("WebSocket配置")]
        [SerializeField] private bool autoConnect = true;
        [SerializeField] private bool enableHeartbeat = true;
        [SerializeField] private float heartbeatInterval = 30f;
        [SerializeField] private int maxReconnectAttempts = 5;
        [SerializeField] private float reconnectDelay = 2f;

        // 连接状态
        public bool IsConnected { get; private set; }
        public bool IsConnecting { get; private set; }
        public string CurrentRoomId { get; private set; }

        // WebSocket连接
        private WebSocketConnection _connection;
        private Queue<WebSocketMessage> _outgoingMessages = new Queue<WebSocketMessage>();
        private Queue<WebSocketMessage> _incomingMessages = new Queue<WebSocketMessage>();

        // 重连机制
        private int _reconnectAttempts = 0;
        private float _lastHeartbeat = 0f;
        private Coroutine _heartbeatCoroutine;
        private Coroutine _reconnectCoroutine;

        // 事件
        public event Action OnConnected;
        public event Action OnDisconnected;
        public event Action<string> OnRoomJoined;
        public event Action<string> OnRoomLeft;
        public event Action<WebSocketMessage> OnMessageReceived;
        public event Action<string> OnError;

        private void Start()
        {
            if (autoConnect)
            {
                ConnectToServer();
            }
        }

        private void Update()
        {
            ProcessIncomingMessages();
            ProcessOutgoingMessages();
        }

        private void OnDestroy()
        {
            Disconnect();
        }

        /// <summary>
        /// 连接到WebSocket服务器
        /// </summary>
        public void ConnectToServer()
        {
            if (IsConnected || IsConnecting) return;

            StartCoroutine(ConnectCoroutine());
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public void Disconnect()
        {
            if (_heartbeatCoroutine != null)
            {
                StopCoroutine(_heartbeatCoroutine);
                _heartbeatCoroutine = null;
            }

            if (_reconnectCoroutine != null)
            {
                StopCoroutine(_reconnectCoroutine);
                _reconnectCoroutine = null;
            }

            if (_connection != null)
            {
                _connection.Close();
                _connection = null;
            }

            IsConnected = false;
            IsConnecting = false;
        }

        /// <summary>
        /// 加入房间
        /// </summary>
        public void JoinRoom(string roomId, string password = "")
        {
            if (!IsConnected)
            {
                OnError?.Invoke("未连接到服务器");
                return;
            }

            var joinData = new JoinRoomData
            {
                RoomId = roomId,
                Password = password
            };

            var message = new WebSocketMessage(WebSocketMessageTypes.JOIN_ROOM, joinData);
            SendMessage(message);
        }

        /// <summary>
        /// 离开房间
        /// </summary>
        public void LeaveRoom()
        {
            if (!IsConnected || string.IsNullOrEmpty(CurrentRoomId))
                return;

            var message = new WebSocketMessage(WebSocketMessageTypes.LEAVE_ROOM, null, CurrentRoomId);
            SendMessage(message);
        }

        /// <summary>
        /// 发送玩家移动
        /// </summary>
        public void SendPlayerMove(Vector3 position, Vector3 velocity, float rotation)
        {
            if (!IsConnected || string.IsNullOrEmpty(CurrentRoomId))
                return;

            var moveData = new PlayerMoveData
            {
                Position = Vector3Data.FromVector3(position),
                Velocity = Vector3Data.FromVector3(velocity),
                Rotation = rotation
            };

            var message = new WebSocketMessage(WebSocketMessageTypes.PLAYER_MOVE, moveData, CurrentRoomId);
            SendMessage(message);
        }

        /// <summary>
        /// 发送聊天消息
        /// </summary>
        public void SendChatMessage(string text, string channel = "all")
        {
            if (!IsConnected || string.IsNullOrEmpty(CurrentRoomId))
                return;

            var chatData = new ChatMessageData
            {
                Message = text,
                Channel = channel
            };

            var message = new WebSocketMessage(WebSocketMessageTypes.CHAT_MESSAGE, chatData, CurrentRoomId);
            SendMessage(message);
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        public void SendMessage(WebSocketMessage message)
        {
            if (!IsConnected)
            {
                Debug.LogWarning("Cannot send message: not connected to server");
                return;
            }

            // 设置发送者ID
            if (AuthManager.Instance != null && AuthManager.Instance.IsAuthenticated)
            {
                message.SenderId = "current_user_id"; // 从AuthManager获取用户ID
            }

            _outgoingMessages.Enqueue(message);
        }

        /// <summary>
        /// 连接协程
        /// </summary>
        private IEnumerator ConnectCoroutine()
        {
            IsConnecting = true;

            // 构建WebSocket URL，包含认证Token
            var wsUrl = NetworkConfig.RoomWebSocketUrl;
            if (AuthManager.Instance != null && AuthManager.Instance.IsAuthenticated)
            {
                wsUrl += $"?token={AuthManager.Instance.AccessToken}";
            }

            Debug.Log($"Connecting to WebSocket server: {wsUrl}");

            _connection = new WebSocketConnection(wsUrl);
            _connection.OnConnected += HandleConnectionEstablished;
            _connection.OnDisconnected += HandleConnectionLost;
            _connection.OnMessageReceived += HandleMessageReceived;
            _connection.OnError += HandleConnectionError;

            yield return _connection.Connect();

            if (_connection.IsConnected)
            {
                IsConnected = true;
                IsConnecting = false;
                _reconnectAttempts = 0;

                // 启动心跳
                if (enableHeartbeat)
                {
                    _heartbeatCoroutine = StartCoroutine(HeartbeatCoroutine());
                }

                OnConnected?.Invoke();
                Debug.Log("Successfully connected to WebSocket server");
            }
            else
            {
                IsConnecting = false;
                OnError?.Invoke("连接失败");
            }
        }

        /// <summary>
        /// 处理传入消息
        /// </summary>
        private void ProcessIncomingMessages()
        {
            while (_incomingMessages.Count > 0)
            {
                var message = _incomingMessages.Dequeue();
                HandleWebSocketMessage(message);
            }
        }

        /// <summary>
        /// 处理传出消息
        /// </summary>
        private void ProcessOutgoingMessages()
        {
            while (_outgoingMessages.Count > 0 && IsConnected)
            {
                var message = _outgoingMessages.Dequeue();
                var json = JsonConvert.SerializeObject(message);
                _connection.Send(json);
            }
        }

        /// <summary>
        /// 处理WebSocket消息
        /// </summary>
        private void HandleWebSocketMessage(WebSocketMessage message)
        {
            switch (message.Type)
            {
                case WebSocketMessageTypes.ROOM_UPDATED:
                    // 处理房间更新
                    break;

                case WebSocketMessageTypes.PLAYER_JOINED:
                    // 处理玩家加入
                    break;

                case WebSocketMessageTypes.PLAYER_LEFT:
                    // 处理玩家离开
                    break;

                case WebSocketMessageTypes.GAME_STARTED:
                    // 处理游戏开始
                    break;

                case WebSocketMessageTypes.ERROR:
                    var errorData = message.GetData<ErrorMessageData>();
                    OnError?.Invoke(errorData?.Message ?? "未知错误");
                    break;

                default:
                    // 转发给其他系统处理
                    OnMessageReceived?.Invoke(message);
                    break;
            }
        }

        /// <summary>
        /// 心跳协程
        /// </summary>
        private IEnumerator HeartbeatCoroutine()
        {
            while (IsConnected)
            {
                yield return new WaitForSeconds(heartbeatInterval);

                if (IsConnected)
                {
                    var heartbeat = new WebSocketMessage(WebSocketMessageTypes.HEARTBEAT, null);
                    SendMessage(heartbeat);
                }
            }
        }

        /// <summary>
        /// 连接建立处理
        /// </summary>
        private void HandleConnectionEstablished()
        {
            _lastHeartbeat = Time.time;
        }

        /// <summary>
        /// 连接丢失处理
        /// </summary>
        private void HandleConnectionLost()
        {
            IsConnected = false;
            OnDisconnected?.Invoke();

            // 尝试重连
            if (_reconnectAttempts < maxReconnectAttempts)
            {
                _reconnectCoroutine = StartCoroutine(ReconnectCoroutine());
            }
        }

        /// <summary>
        /// 消息接收处理
        /// </summary>
        private void HandleMessageReceived(string messageText)
        {
            try
            {
                var message = JsonConvert.DeserializeObject<WebSocketMessage>(messageText);
                _incomingMessages.Enqueue(message);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to parse WebSocket message: {ex.Message}");
            }
        }

        /// <summary>
        /// 连接错误处理
        /// </summary>
        private void HandleConnectionError(string error)
        {
            OnError?.Invoke($"WebSocket错误: {error}");
        }

        /// <summary>
        /// 重连协程
        /// </summary>
        private IEnumerator ReconnectCoroutine()
        {
            _reconnectAttempts++;
            Debug.Log($"Attempting to reconnect ({_reconnectAttempts}/{maxReconnectAttempts})...");

            yield return new WaitForSeconds(reconnectDelay);

            ConnectToServer();
        }
    }
}
```

## 🔄 下一步

完成这些修复后，前台将能够：
- 正确连接到后台各个服务
- 使用统一的数据格式
- 实现完整的认证流程
- 与后台API保持完全兼容
- 统一的WebSocket消息处理

建议按照优先级逐步实施这些修复方案：
1. 首先实现网络配置统一
2. 然后添加数据模型
3. 接着实现认证管理器
4. 最后完善WebSocket消息处理
