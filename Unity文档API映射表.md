# Unity文档API映射表

## 📋 概述

本文档提供了 `/Docs/Unity` 目录下文档中描述的API与实际项目代码中API的完整映射关系，帮助开发者快速找到正确的实现方式。

**更新时间**: 2025年6月24日  
**适用版本**: Unity 6000.1.1f1+  
**项目架构**: Photon Fusion + 自定义网络框架

## ⚠️ 重要说明

项目中存在两套并行的网络系统：
1. **Photon Fusion 系统** (`Assets/Scripts/Network/`) - 推荐用于基础网络功能
2. **自定义网络框架** (`Assets/Scripts/CustomNetworking/`) - 用于高级网络功能

## 🔗 网络管理器API映射

### 连接管理

#### 文档API
```csharp
// 文档中的API
ModernGameNetworkManager.Instance.ConnectToServer();
ModernGameNetworkManager.Instance.JoinGame(gameId, asHost);
ModernGameNetworkManager.Instance.LeaveGame();
```

#### 实际API
```csharp
// Photon Fusion 系统 (推荐)
NetworkManager.Instance.ConnectToServer();
NetworkManager.Instance.CreateRoom(roomName, isOpen, isVisible);
NetworkManager.Instance.JoinRoom(roomName);
NetworkManager.Instance.LeaveRoom();

// 自定义WebSocket系统 (高级功能)
ImprovedWebSocketManager webSocket = GetComponent<ImprovedWebSocketManager>();
webSocket.ConnectToServer();
webSocket.JoinRoom(roomId, password);
webSocket.LeaveRoom();
```

### 网络状态查询

#### 文档API
```csharp
// 文档中的API
bool isConnected = ModernGameNetworkManager.Instance.IsConnected;
bool isHost = ModernGameNetworkManager.Instance.IsHost;
string currentHost = ModernGameNetworkManager.Instance.CurrentHost;
```

#### 实际API
```csharp
// Photon Fusion 系统
bool isConnected = NetworkManager.Instance.IsConnected;
bool isHost = NetworkManager.Instance.IsHost;
NetworkRunner runner = NetworkManager.Instance.Runner;

// 自定义WebSocket系统
bool isConnected = ImprovedWebSocketManager.Instance.IsConnected;
string currentRoomId = ImprovedWebSocketManager.Instance.CurrentRoomId;
```

## 🚀 RPC系统API映射

### RPC调用

#### 文档API
```csharp
// 文档中的API
rpcManager.SendReliableRPC("PlayerMove", "others", position, velocity);
rpcManager.SendUnreliableRPC("PlayerMove", "others", position, velocity);
rpcManager.SendRPCToAll("GameStateUpdate", gameState);
rpcManager.SendRPCToServer("PlayerAction", actionData);
```

#### 实际API
```csharp
// 自定义RPC系统
using CustomNetworking.Core.RPC;

RpcManager rpcManager = GetComponent<RpcManager>();
rpcManager.SendRpc(
    sender: this,
    methodName: "PlayerMove", 
    target: RpcTargets.Others,
    reliable: true,
    priority: RpcPriority.Normal,
    targetPlayer: PlayerRef.None,
    includeSender: false,
    parameters: new object[] { position, velocity }
);

// 简化的调用方式
rpcManager.CallReliableRPC("PlayerMove", RpcTargets.Others, 
    new { position, velocity }, null, RpcPriority.Normal);
```

### RPC注册

#### 文档API
```csharp
// 文档中的API
rpcManager.RegisterRPCHandler("PlayerMove", OnPlayerMoveRPC);
```

#### 实际API
```csharp
// 自定义RPC系统
rpcManager.RegisterMethod(this, "PlayerMove", OnPlayerMoveRPC);

// 方法签名
private void OnPlayerMoveRPC(object[] parameters)
{
    // 处理RPC调用
}
```

## 🔄 主机迁移API映射

### 主机迁移管理

#### 文档API
```csharp
// 文档中的API
hostMigrationManager.RegisterAsCandidate();
hostMigrationManager.HandleHostChanged(oldHost, newHost);
```

#### 实际API
```csharp
// 自定义主机迁移系统
using CustomNetworking.HostMigration;

HostMigrationManager hostMigration = GetComponent<HostMigrationManager>();
hostMigration.RegisterAsCandidate(gameId, playerId, priority);

// 事件订阅
hostMigration.OnHostChanged += (oldHost, newHost) => {
    Debug.Log($"Host changed: {oldHost} -> {newHost}");
};
```

## 🔗 网络同步API映射

### 属性同步

#### 文档API
```csharp
// 文档中的API
[NetworkSync(syncRate: 20f, deltaCompress: true)]
public Vector3 Position { get; set; }

networkSyncManager.RegisterNetworkObject(networkObject);
```

#### 实际API
```csharp
// Photon Fusion 系统 (推荐)
[Networked] public Vector3 NetworkPosition { get; set; }
[Networked] public Quaternion NetworkRotation { get; set; }
[Networked] public PlayerState CurrentState { get; set; }

// 自定义同步系统
using CustomNetworking.Synchronization;

NetworkSynchronizationManager syncManager = GetComponent<NetworkSynchronizationManager>();
syncManager.RegisterSyncObject(objectId, this);
```

## 🛡️ 权限管理API映射

### 权限检查

#### 文档API
```csharp
// 文档中的API
bool hasAuthority = authorityManager.HasAuthority(playerId, "move");
authorityManager.RequestGrantAuthority(playerId, playerId, authorities);
```

#### 实际API
```csharp
// 自定义权限系统
using CustomNetworking.Authority;

AuthorityManager authManager = GetComponent<AuthorityManager>();
bool hasAuthority = authManager.HasAuthority(objectId, player, AuthorityType.Modify);

// 设置权限
authManager.SetObjectAuthority(objectId, owner, 
    new List<AuthorityType> { AuthorityType.Modify, AuthorityType.Read });
```

## 🎮 玩家控制器API映射

### 玩家网络输入

#### 文档API
```csharp
// 文档中的API
public class ModernNetworkPlayer : MonoBehaviour
{
    [NetworkSync] public Vector3 Position { get; set; }
    
    void MovePlayer(Vector2 input) { }
    void PerformAction(string actionType) { }
}
```

#### 实际API
```csharp
// Photon Fusion 系统
public class PlayerController : NetworkBehaviour
{
    [Networked] public Vector3 NetworkPosition { get; set; }
    [Networked] public PlayerState CurrentState { get; set; }
    
    public override void FixedUpdateNetwork()
    {
        if (GetInput(out PlayerNetworkInput input))
        {
            ProcessMovement(input.MovementInput);
        }
    }
    
    public void ChangeState(PlayerState newState)
    {
        if (Object.HasStateAuthority)
        {
            CurrentState = newState;
        }
    }
}
```

## 🎯 游戏管理器API映射

### 游戏状态管理

#### 文档API
```csharp
// 文档中的API
public class ModernGameManager : MonoBehaviour
{
    public void StartGame() { }
    public void EndGame() { }
    public void CallMeeting(PlayerRef caller, bool isBodyReport) { }
}
```

#### 实际API
```csharp
// Photon Fusion 系统
public class GameManager : NetworkBehaviour
{
    [Networked] private NetworkBool IsGameActive { get; set; }
    [Networked] private NetworkBool IsInMeeting { get; set; }
    
    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    public void StartGameRPC()
    {
        if (Object.HasStateAuthority)
        {
            IsGameActive = true;
        }
    }
    
    public void CallMeeting(PlayerRef caller, bool isBodyReport)
    {
        if (Object.HasStateAuthority)
        {
            IsInMeeting = true;
            MeetingCaller = caller;
            IsBodyReport = isBodyReport;
        }
    }
}
```

## 📡 WebSocket通信API映射

### 消息发送

#### 文档API
```csharp
// 文档中的API
webSocketManager.SendMessage(message);
webSocketManager.SendPlayerMove(position, velocity, rotation);
webSocketManager.SendChatMessage(text, channel);
```

#### 实际API
```csharp
// 自定义WebSocket系统
ImprovedWebSocketManager webSocket = GetComponent<ImprovedWebSocketManager>();

// 发送自定义消息
WebSocketMessage message = new WebSocketMessage("custom_event", data, roomId);
webSocket.SendMessage(message);

// 发送玩家移动
webSocket.SendPlayerMove(position, velocity, rotation);

// 发送聊天消息
webSocket.SendChatMessage(text, "all");
```

## 🔧 配置管理API映射

### 网络配置

#### 文档API
```csharp
// 文档中的API
public static class ServerConfig
{
    public const string AUTH_BASE_URL = "http://localhost:8001";
    public const int TICK_RATE = 60;
    public const int RPC_TIMEOUT = 5000;
}
```

#### 实际API
```csharp
// 实际配置系统
NetworkConfig config = NetworkConfig.Instance;

// 获取服务器URL
string authUrl = config.GetServiceUrl(ServiceType.Auth);
string roomUrl = config.GetServiceUrl(ServiceType.Room);

// WebSocket配置
ImprovedWebSocketManager webSocket = GetComponent<ImprovedWebSocketManager>();
webSocket.heartbeatInterval = 30f;
webSocket.maxReconnectAttempts = 5;
```

## 📚 使用建议

### 推荐使用方案

1. **基础网络功能**: 使用 Photon Fusion 系统
   - 房间管理: `NetworkManager`
   - 玩家同步: `PlayerController` + `NetworkBehaviour`
   - 游戏状态: `GameManager` + `[Networked]` 属性

2. **高级网络功能**: 使用自定义网络框架
   - WebSocket通信: `ImprovedWebSocketManager`
   - 自定义RPC: `RpcManager`
   - 权限管理: `AuthorityManager`

3. **混合使用**: 在同一项目中同时使用两套系统
   - 确保避免功能冲突
   - 明确各自的职责范围
   - 统一错误处理和日志记录

---

**维护说明**: 本映射表需要随着代码更新而同步更新  
**反馈渠道**: 如发现映射错误或遗漏，请及时反馈给技术团队
