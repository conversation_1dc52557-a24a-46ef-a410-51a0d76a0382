# Global analyzer configuration for <PERSON><PERSON>uck<PERSON>ill project
is_global = true

# Namespace rules
dotnet_diagnostic.IDE0130.severity = none

# Style rules
dotnet_style_namespace_match_folder = false

# Naming conventions
dotnet_naming_rule.namespace_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.namespace_should_be_pascal_case.symbols = namespace_symbols
dotnet_naming_rule.namespace_should_be_pascal_case.style = pascal_case

dotnet_naming_symbols.namespace_symbols.applicable_kinds = namespace
dotnet_naming_symbols.namespace_symbols.applicable_accessibilities = *

dotnet_naming_style.pascal_case.capitalization = pascal_case

# Allow custom namespace patterns like GooseDuck<PERSON><PERSON>.Core
dotnet_code_quality.CA1050.api_surface = private, internal
