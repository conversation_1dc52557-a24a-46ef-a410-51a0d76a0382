root = true

# All files
[*]
charset = utf-8
insert_final_newline = true
trim_trailing_whitespace = true

# C# files
[*.cs]
indent_style = space
indent_size = 4
end_of_line = crlf

# Namespace rules
dotnet_style_namespace_match_folder = false

# IDE0130: Namespace does not match folder structure
dotnet_diagnostic.IDE0130.severity = none

# Rider specific settings
resharper_check_namespace_highlighting = none
resharper_namespace_does_not_match_folder_structure_highlighting = none

# Code style rules
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true
csharp_new_line_between_query_expression_clauses = true

# Indentation preferences
csharp_indent_case_contents = true
csharp_indent_switch_labels = true
csharp_indent_labels = flush_left

# Space preferences
csharp_space_after_cast = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_parentheses = false
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_around_binary_operators = before_and_after
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_call_empty_parameter_list_parentheses = false

# Naming conventions
dotnet_naming_rule.namespace_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.namespace_should_be_pascal_case.symbols = namespace_symbols
dotnet_naming_rule.namespace_should_be_pascal_case.style = pascal_case

dotnet_naming_symbols.namespace_symbols.applicable_kinds = namespace
dotnet_naming_symbols.namespace_symbols.applicable_accessibilities = *

dotnet_naming_style.pascal_case.capitalization = pascal_case

# Allow GooseDuckKill.* namespace pattern
dotnet_code_quality_unused_parameters = all

# Unity specific settings
[*.unity]
indent_style = space
indent_size = 2

[*.meta]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2
